/**
 * Blueprint Generator Module
 * Generates reusable code templates and configurations from recreated elements
 */

class BlueprintGenerator {
    constructor() {
        this.templates = new Map();
        this.configurations = new Map();
        this.metadata = {};
    }

    /**
     * Generate complete blueprint from recreation state
     */
    async generateBlueprint(recreationState, analysisData, options = {}) {
        try {
            const blueprint = {
                metadata: this.generateMetadata(analysisData, options),
                configuration: this.generateConfiguration(recreationState, analysisData),
                templates: {
                    html: this.generateHTMLTemplate(recreationState),
                    css: this.generateCSSTemplate(recreationState, analysisData),
                    javascript: this.generateJavaScriptTemplate(recreationState),
                    svg: this.generateSVGTemplate(recreationState)
                },
                assets: this.generateAssets(analysisData),
                documentation: this.generateDocumentation(recreationState, analysisData),
                examples: this.generateExamples(recreationState)
            };

            return blueprint;
        } catch (error) {
            console.error('Blueprint generation failed:', error);
            throw error;
        }
    }

    /**
     * Generate metadata for the blueprint
     */
    generateMetadata(analysisData, options) {
        return {
            name: options.name || `ImageRecreation_${Date.now()}`,
            version: '1.0.0',
            description: options.description || 'Auto-generated image recreation blueprint',
            author: options.author || 'Image Recreation App',
            created: new Date().toISOString(),
            sourceImage: {
                width: analysisData.imageSize.width,
                height: analysisData.imageSize.height,
                processingTime: analysisData.processingTime
            },
            elements: {
                total: analysisData.elements.length,
                types: this.getElementTypeCounts(analysisData.elements)
            },
            colors: analysisData.colors.length,
            tags: this.generateTags(analysisData),
            license: 'MIT'
        };
    }

    /**
     * Generate configuration object
     */
    generateConfiguration(recreationState, analysisData) {
        return {
            canvas: {
                width: analysisData.imageSize.width,
                height: analysisData.imageSize.height,
                scale: recreationState.scale,
                offset: recreationState.offset
            },
            elements: recreationState.elements.map(element => ({
                id: element.id,
                type: element.type,
                properties: this.extractElementProperties(element),
                style: this.extractElementStyle(element),
                interactions: this.extractElementInteractions(element)
            })),
            connections: recreationState.connections.map(conn => ({
                type: 'line',
                start: { x: conn.x1, y: conn.y1 },
                end: { x: conn.x2, y: conn.y2 },
                style: 'dashed'
            })),
            colorPalette: analysisData.colors.map(color => ({
                hex: color.hex,
                rgb: { r: color.r, g: color.g, b: color.b },
                frequency: color.frequency,
                usage: this.determineColorUsage(color, recreationState.elements)
            })),
            settings: {
                interactive: true,
                responsive: true,
                animations: false
            }
        };
    }

    /**
     * Generate HTML template
     */
    generateHTMLTemplate(recreationState) {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{BLUEPRINT_NAME}}</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>{{BLUEPRINT_NAME}}</h1>
            <div class="controls">
                <button id="toggleInteraction">Toggle Interaction</button>
                <button id="exportSVG">Export SVG</button>
                <button id="resetView">Reset View</button>
            </div>
        </header>
        
        <main class="canvas-container">
            <svg id="mainCanvas" class="recreation-canvas" 
                 width="{{CANVAS_WIDTH}}" height="{{CANVAS_HEIGHT}}">
                <!-- Elements will be generated here -->
                {{ELEMENTS_PLACEHOLDER}}
            </svg>
        </main>
        
        <aside class="properties-panel">
            <h3>Element Properties</h3>
            <div id="elementProperties">
                <p>Select an element to view properties</p>
            </div>
        </aside>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/interactjs/dist/interact.min.js"></script>
    <script src="recreation.js"></script>
</body>
</html>`;
    }

    /**
     * Generate CSS template
     */
    generateCSSTemplate(recreationState, analysisData) {
        const colorVariables = analysisData.colors.map((color, index) => 
            `  --color-${index + 1}: ${color.hex};`
        ).join('\n');

        return `/* Auto-generated CSS for {{BLUEPRINT_NAME}} */

:root {
${colorVariables}
  --canvas-bg: #ffffff;
  --border-color: #cccccc;
  --selection-color: rgba(0, 123, 255, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    display: grid;
    grid-template-areas: 
        "header header"
        "main aside";
    grid-template-columns: 1fr 300px;
    grid-template-rows: auto 1fr;
    height: 100vh;
    gap: 1rem;
    padding: 1rem;
}

header {
    grid-area: header;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.controls {
    display: flex;
    gap: 0.5rem;
}

.controls button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    background: #007bff;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s;
}

.controls button:hover {
    background: #0056b3;
}

.canvas-container {
    grid-area: main;
    background: var(--canvas-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
}

.recreation-canvas {
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
}

.properties-panel {
    grid-area: aside;
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow-y: auto;
}

/* Element styles */
.recreated-circle {
    cursor: pointer;
    transition: all 0.2s ease;
}

.recreated-circle:hover {
    stroke-width: 3;
    filter: brightness(1.1);
}

.recreated-rectangle {
    cursor: pointer;
    transition: all 0.2s ease;
}

.recreated-rectangle:hover {
    stroke-width: 3;
    filter: brightness(1.1);
}

.recreated-line {
    cursor: pointer;
    transition: all 0.2s ease;
}

.recreated-line:hover {
    stroke-width: 4;
}

.recreated-text {
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.recreated-text:hover {
    filter: brightness(1.2);
}

.element-connection {
    pointer-events: none;
    opacity: 0.6;
}

.selected {
    filter: drop-shadow(0 0 5px var(--selection-color));
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        grid-template-areas: 
            "header"
            "main"
            "aside";
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .properties-panel {
        max-height: 200px;
    }
}`;
    }

    /**
     * Generate JavaScript template
     */
    generateJavaScriptTemplate(recreationState) {
        return `// Auto-generated JavaScript for {{BLUEPRINT_NAME}}

class RecreationApp {
    constructor() {
        this.canvas = document.getElementById('mainCanvas');
        this.elements = new Map();
        this.selectedElement = null;
        this.isInteractive = true;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadElements();
        this.makeInteractive();
    }
    
    setupEventListeners() {
        document.getElementById('toggleInteraction').addEventListener('click', () => {
            this.toggleInteraction();
        });
        
        document.getElementById('exportSVG').addEventListener('click', () => {
            this.exportSVG();
        });
        
        document.getElementById('resetView').addEventListener('click', () => {
            this.resetView();
        });
        
        // Canvas click to deselect
        this.canvas.addEventListener('click', (e) => {
            if (e.target === this.canvas) {
                this.deselectAll();
            }
        });
    }
    
    loadElements() {
        // Load elements from configuration
        const config = {{CONFIGURATION_PLACEHOLDER}};
        
        config.elements.forEach(elementConfig => {
            this.createElement(elementConfig);
        });
    }
    
    createElement(config) {
        let element;
        
        switch (config.type) {
            case 'circle':
                element = this.createCircle(config);
                break;
            case 'rectangle':
                element = this.createRectangle(config);
                break;
            case 'line':
                element = this.createLine(config);
                break;
            case 'text':
                element = this.createText(config);
                break;
        }
        
        if (element) {
            this.canvas.appendChild(element);
            this.elements.set(config.id, {
                element: element,
                config: config
            });
        }
    }
    
    createCircle(config) {
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('cx', config.properties.x);
        circle.setAttribute('cy', config.properties.y);
        circle.setAttribute('r', config.properties.radius);
        this.applyStyle(circle, config.style);
        circle.setAttribute('class', 'recreated-circle');
        circle.setAttribute('data-id', config.id);
        return circle;
    }
    
    createRectangle(config) {
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('x', config.properties.x);
        rect.setAttribute('y', config.properties.y);
        rect.setAttribute('width', config.properties.width);
        rect.setAttribute('height', config.properties.height);
        this.applyStyle(rect, config.style);
        rect.setAttribute('class', 'recreated-rectangle');
        rect.setAttribute('data-id', config.id);
        return rect;
    }
    
    createLine(config) {
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', config.properties.x1);
        line.setAttribute('y1', config.properties.y1);
        line.setAttribute('x2', config.properties.x2);
        line.setAttribute('y2', config.properties.y2);
        this.applyStyle(line, config.style);
        line.setAttribute('class', 'recreated-line');
        line.setAttribute('data-id', config.id);
        return line;
    }
    
    createText(config) {
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', config.properties.x);
        text.setAttribute('y', config.properties.y);
        text.setAttribute('font-size', config.properties.fontSize);
        text.textContent = config.properties.text;
        this.applyStyle(text, config.style);
        text.setAttribute('class', 'recreated-text');
        text.setAttribute('data-id', config.id);
        return text;
    }
    
    applyStyle(element, style) {
        Object.keys(style).forEach(property => {
            element.setAttribute(property, style[property]);
        });
    }
    
    makeInteractive() {
        if (!this.isInteractive) return;
        
        this.elements.forEach((elementData, id) => {
            const element = elementData.element;
            
            element.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectElement(id);
            });
            
            // Add drag functionality if interact.js is available
            if (typeof interact !== 'undefined') {
                interact(element).draggable({
                    listeners: {
                        move: (event) => {
                            this.moveElement(id, event.dx, event.dy);
                        }
                    }
                });
            }
        });
    }
    
    selectElement(id) {
        this.deselectAll();
        
        const elementData = this.elements.get(id);
        if (elementData) {
            elementData.element.classList.add('selected');
            this.selectedElement = id;
            this.showElementProperties(elementData.config);
        }
    }
    
    deselectAll() {
        this.elements.forEach(elementData => {
            elementData.element.classList.remove('selected');
        });
        this.selectedElement = null;
        this.hideElementProperties();
    }
    
    moveElement(id, dx, dy) {
        const elementData = this.elements.get(id);
        if (!elementData) return;
        
        const element = elementData.element;
        const config = elementData.config;
        
        switch (config.type) {
            case 'circle':
                const cx = parseFloat(element.getAttribute('cx')) + dx;
                const cy = parseFloat(element.getAttribute('cy')) + dy;
                element.setAttribute('cx', cx);
                element.setAttribute('cy', cy);
                break;
                
            case 'rectangle':
                const x = parseFloat(element.getAttribute('x')) + dx;
                const y = parseFloat(element.getAttribute('y')) + dy;
                element.setAttribute('x', x);
                element.setAttribute('y', y);
                break;
                
            case 'line':
                const x1 = parseFloat(element.getAttribute('x1')) + dx;
                const y1 = parseFloat(element.getAttribute('y1')) + dy;
                const x2 = parseFloat(element.getAttribute('x2')) + dx;
                const y2 = parseFloat(element.getAttribute('y2')) + dy;
                element.setAttribute('x1', x1);
                element.setAttribute('y1', y1);
                element.setAttribute('x2', x2);
                element.setAttribute('y2', y2);
                break;
                
            case 'text':
                const textX = parseFloat(element.getAttribute('x')) + dx;
                const textY = parseFloat(element.getAttribute('y')) + dy;
                element.setAttribute('x', textX);
                element.setAttribute('y', textY);
                break;
        }
    }
    
    showElementProperties(config) {
        const panel = document.getElementById('elementProperties');
        panel.innerHTML = \`
            <h4>\${config.type.toUpperCase()} Element</h4>
            <div class="property-group">
                <label>ID:</label>
                <span>\${config.id}</span>
            </div>
            <div class="property-group">
                <label>Type:</label>
                <span>\${config.type}</span>
            </div>
            \${this.generatePropertyInputs(config)}
        \`;
    }
    
    hideElementProperties() {
        const panel = document.getElementById('elementProperties');
        panel.innerHTML = '<p>Select an element to view properties</p>';
    }
    
    generatePropertyInputs(config) {
        // Generate input fields based on element type
        let inputs = '';
        
        Object.keys(config.properties).forEach(prop => {
            inputs += \`
                <div class="property-group">
                    <label>\${prop}:</label>
                    <input type="text" value="\${config.properties[prop]}" 
                           onchange="app.updateProperty('\${config.id}', '\${prop}', this.value)">
                </div>
            \`;
        });
        
        return inputs;
    }
    
    updateProperty(elementId, property, value) {
        const elementData = this.elements.get(elementId);
        if (elementData) {
            elementData.config.properties[property] = value;
            elementData.element.setAttribute(property, value);
        }
    }
    
    toggleInteraction() {
        this.isInteractive = !this.isInteractive;
        
        if (this.isInteractive) {
            this.makeInteractive();
        } else {
            // Remove interaction
            this.elements.forEach(elementData => {
                if (typeof interact !== 'undefined') {
                    interact(elementData.element).unset();
                }
            });
        }
    }
    
    exportSVG() {
        const svgData = new XMLSerializer().serializeToString(this.canvas);
        const blob = new Blob([svgData], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = '{{BLUEPRINT_NAME}}.svg';
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    resetView() {
        this.deselectAll();
        // Reset any transformations or zoom
        this.canvas.setAttribute('viewBox', '0 0 {{CANVAS_WIDTH}} {{CANVAS_HEIGHT}}');
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new RecreationApp();
});`;
    }

    /**
     * Generate SVG template
     */
    generateSVGTemplate(recreationState) {
        const elements = recreationState.elements.map(element => {
            return this.generateSVGElement(element);
        }).join('\n    ');

        return `<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" 
     width="{{CANVAS_WIDTH}}" height="{{CANVAS_HEIGHT}}"
     viewBox="0 0 {{CANVAS_WIDTH}} {{CANVAS_HEIGHT}}">
    
    <defs>
        <style>
            .recreated-circle { cursor: pointer; }
            .recreated-rectangle { cursor: pointer; }
            .recreated-line { cursor: pointer; }
            .recreated-text { cursor: pointer; user-select: none; }
        </style>
    </defs>
    
    ${elements}
    
</svg>`;
    }

    generateSVGElement(element) {
        switch (element.type) {
            case 'circle':
                return `<circle cx="${element.data.x}" cy="${element.data.y}" r="${element.data.radius}" 
                        fill="${element.data.color?.hex || '#cccccc'}" stroke="#333" stroke-width="2" 
                        class="recreated-circle" />`;
            
            case 'rectangle':
                return `<rect x="${element.data.x}" y="${element.data.y}" 
                        width="${element.data.width}" height="${element.data.height}"
                        fill="${element.data.color?.hex || '#cccccc'}" stroke="#333" stroke-width="2" 
                        class="recreated-rectangle" />`;
            
            case 'line':
                return `<line x1="${element.data.x1}" y1="${element.data.y1}" 
                        x2="${element.data.x2}" y2="${element.data.y2}"
                        stroke="#333" stroke-width="3" class="recreated-line" />`;
            
            case 'text':
                return `<text x="${element.data.x}" y="${element.data.y}" 
                        font-size="${element.data.fontSize || 16}" font-family="Arial, sans-serif"
                        fill="#333" class="recreated-text">${element.data.text || 'Text'}</text>`;
            
            default:
                return '';
        }
    }

    /**
     * Generate assets (images, fonts, etc.)
     */
    generateAssets(analysisData) {
        return {
            colorPalette: {
                name: 'extracted-colors.json',
                content: JSON.stringify(analysisData.colors, null, 2)
            },
            elementData: {
                name: 'elements.json',
                content: JSON.stringify(analysisData.elements, null, 2)
            }
        };
    }

    /**
     * Generate documentation
     */
    generateDocumentation(recreationState, analysisData) {
        return {
            readme: this.generateReadme(recreationState, analysisData),
            api: this.generateAPIDoc(recreationState),
            usage: this.generateUsageGuide(recreationState)
        };
    }

    generateReadme(recreationState, analysisData) {
        return `# {{BLUEPRINT_NAME}}

Auto-generated image recreation blueprint created by Image Recreation App.

## Overview

This blueprint recreates an image with ${analysisData.elements.length} detected elements including:
${this.getElementTypeCounts(analysisData.elements).map(type => `- ${type.count} ${type.type}(s)`).join('\n')}

## Files Included

- \`index.html\` - Main HTML template
- \`styles.css\` - Styling and layout
- \`recreation.js\` - Interactive functionality
- \`template.svg\` - Static SVG version
- \`config.json\` - Element configuration
- \`colors.json\` - Extracted color palette

## Quick Start

1. Open \`index.html\` in a web browser
2. Elements are interactive by default
3. Click elements to select and view properties
4. Drag elements to reposition them
5. Use controls to export or reset the view

## Customization

### Modifying Elements

Edit the configuration in \`recreation.js\` or use the properties panel to adjust:
- Position and size
- Colors and styling
- Text content
- Interaction behavior

### Adding New Elements

Use the \`createElement()\` method with appropriate configuration:

\`\`\`javascript
app.createElement({
    id: 'new-element',
    type: 'circle',
    properties: { x: 100, y: 100, radius: 50 },
    style: { fill: '#ff0000', stroke: '#000000' }
});
\`\`\`

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Dependencies

- interact.js (for drag functionality)

## License

MIT License - Feel free to use and modify as needed.
`;
    }

    generateAPIDoc(recreationState) {
        return `# API Documentation

## RecreationApp Class

### Constructor
\`new RecreationApp()\`

Creates a new instance of the recreation app.

### Methods

#### createElement(config)
Creates a new SVG element from configuration.

**Parameters:**
- \`config\` (Object) - Element configuration
  - \`id\` (String) - Unique identifier
  - \`type\` (String) - Element type ('circle', 'rectangle', 'line', 'text')
  - \`properties\` (Object) - Element properties
  - \`style\` (Object) - Styling attributes

#### selectElement(id)
Selects an element by ID.

**Parameters:**
- \`id\` (String) - Element ID

#### moveElement(id, dx, dy)
Moves an element by the specified offset.

**Parameters:**
- \`id\` (String) - Element ID
- \`dx\` (Number) - X offset
- \`dy\` (Number) - Y offset

#### exportSVG()
Exports the current canvas as an SVG file.

#### resetView()
Resets the canvas view to default state.

### Events

The app dispatches custom events for integration:

- \`elementSelected\` - When an element is selected
- \`elementMoved\` - When an element is moved
- \`canvasClicked\` - When the canvas background is clicked
`;
    }

    generateUsageGuide(recreationState) {
        return `# Usage Guide

## Getting Started

1. **Open the Application**
   - Open \`index.html\` in a web browser
   - The recreation will load automatically

2. **Interacting with Elements**
   - Click any element to select it
   - Selected elements show in the properties panel
   - Drag elements to move them around

3. **Using Controls**
   - **Toggle Interaction**: Enable/disable element interaction
   - **Export SVG**: Download the current state as SVG
   - **Reset View**: Return to original layout

## Advanced Usage

### Customizing Colors

Edit the CSS variables in \`styles.css\`:

\`\`\`css
:root {
    --color-1: #your-color;
    --color-2: #another-color;
}
\`\`\`

### Adding Animations

Add CSS transitions or animations:

\`\`\`css
.recreated-circle {
    transition: all 0.3s ease;
}

.recreated-circle:hover {
    transform: scale(1.1);
}
\`\`\`

### Programmatic Control

Access the app instance:

\`\`\`javascript
// Select an element
app.selectElement('element-id');

// Move an element
app.moveElement('element-id', 10, 20);

// Add event listeners
document.addEventListener('elementSelected', (e) => {
    console.log('Selected:', e.detail.elementId);
});
\`\`\`

## Troubleshooting

### Elements Not Interactive
- Check that interact.js is loaded
- Verify \`isInteractive\` is true
- Check browser console for errors

### Styling Issues
- Ensure CSS file is properly linked
- Check for CSS conflicts
- Verify SVG namespace declarations

### Performance Issues
- Reduce number of elements for better performance
- Disable animations on slower devices
- Use CSS transforms instead of attribute changes
`;
    }

    /**
     * Generate usage examples
     */
    generateExamples(recreationState) {
        return {
            basic: this.generateBasicExample(),
            advanced: this.generateAdvancedExample(),
            integration: this.generateIntegrationExample()
        };
    }

    generateBasicExample() {
        return `// Basic usage example

// Create a simple recreation
const app = new RecreationApp();

// Add a new circle
app.createElement({
    id: 'my-circle',
    type: 'circle',
    properties: {
        x: 150,
        y: 150,
        radius: 30
    },
    style: {
        fill: '#ff6b6b',
        stroke: '#333',
        'stroke-width': '2'
    }
});

// Select and modify
app.selectElement('my-circle');
app.moveElement('my-circle', 50, 0);`;
    }

    generateAdvancedExample() {
        return `// Advanced usage with custom interactions

class CustomRecreationApp extends RecreationApp {
    constructor() {
        super();
        this.setupCustomEvents();
    }
    
    setupCustomEvents() {
        // Add double-click to delete
        this.canvas.addEventListener('dblclick', (e) => {
            const elementId = e.target.getAttribute('data-id');
            if (elementId) {
                this.deleteElement(elementId);
            }
        });
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Delete' && this.selectedElement) {
                this.deleteElement(this.selectedElement);
            }
        });
    }
    
    deleteElement(id) {
        const elementData = this.elements.get(id);
        if (elementData) {
            elementData.element.remove();
            this.elements.delete(id);
            this.deselectAll();
        }
    }
    
    // Add animation support
    animateElement(id, property, targetValue, duration = 1000) {
        const elementData = this.elements.get(id);
        if (!elementData) return;
        
        const element = elementData.element;
        const startValue = parseFloat(element.getAttribute(property));
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = startValue + (targetValue - startValue) * progress;
            element.setAttribute(property, currentValue);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
}

// Usage
const customApp = new CustomRecreationApp();
customApp.animateElement('my-circle', 'r', 50, 2000);`;
    }

    generateIntegrationExample() {
        return `// Integration with other frameworks

// React component example
import React, { useEffect, useRef } from 'react';

function RecreationComponent({ config }) {
    const canvasRef = useRef();
    const appRef = useRef();
    
    useEffect(() => {
        if (canvasRef.current) {
            appRef.current = new RecreationApp();
            
            // Load configuration
            config.elements.forEach(elementConfig => {
                appRef.current.createElement(elementConfig);
            });
        }
        
        return () => {
            // Cleanup
            if (appRef.current) {
                appRef.current.destroy();
            }
        };
    }, [config]);
    
    const handleExport = () => {
        if (appRef.current) {
            appRef.current.exportSVG();
        }
    };
    
    return (
        <div>
            <button onClick={handleExport}>Export</button>
            <div ref={canvasRef} />
        </div>
    );
}

`;
    }

    /**
     * Helper methods
     */
    getElementTypeCounts(elements) {
        const counts = {};
        elements.forEach(element => {
            counts[element.type] = (counts[element.type] || 0) + 1;
        });
        
        return Object.entries(counts).map(([type, count]) => ({ type, count }));
    }
    
    extractElementProperties(element) {
        const properties = {};
        
        switch (element.type) {
            case 'circle':
                properties.x = element.data.x;
                properties.y = element.data.y;
                properties.radius = element.data.radius;
                break;
            case 'rectangle':
                properties.x = element.data.x;
                properties.y = element.data.y;
                properties.width = element.data.width;
                properties.height = element.data.height;
                break;
            case 'line':
                properties.x1 = element.data.x1;
                properties.y1 = element.data.y1;
                properties.x2 = element.data.x2;
                properties.y2 = element.data.y2;
                break;
            case 'text':
                properties.x = element.data.x;
                properties.y = element.data.y;
                properties.text = element.data.text;
                properties.fontSize = element.data.fontSize || 16;
                break;
        }
        
        return properties;
    }
    
    extractElementStyle(element) {
        return {
            fill: element.data.color?.hex || '#cccccc',
            stroke: '#333333',
            'stroke-width': '2'
        };
    }
    
    extractElementInteractions(element) {
        return {
            draggable: true,
            selectable: true,
            resizable: false
        };
    }
    
    determineColorUsage(color, elements) {
        const usage = [];
        elements.forEach(element => {
            if (element.data.color?.hex === color.hex) {
                usage.push(element.id);
            }
        });
        return usage;
    }
    
    generateTags(analysisData) {
        const tags = ['auto-generated', 'image-recreation'];
        
        // Add tags based on element types
        const elementTypes = [...new Set(analysisData.elements.map(e => e.type))];
        tags.push(...elementTypes);
        
        // Add color-based tags
        if (analysisData.colors.length > 10) {
            tags.push('colorful');
        }
        
        return tags;
    }
}

// ES6 module export
export default BlueprintGenerator;