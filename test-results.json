{"numTotalTestSuites": 10, "numPassedTestSuites": 10, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 21, "numPassedTests": 21, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "startTime": 1754422084657, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["", "Vision Mode Integration Tests", "MCP Agent Communication and Workflow Orchestration"], "fullName": " Vision Mode Integration Tests MCP Agent Communication and Workflow Orchestration should successfully register and communicate with MCP agents", "status": "passed", "title": "should successfully register and communicate with MCP agents", "duration": 5, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "MCP Agent Communication and Workflow Orchestration"], "fullName": " Vision Mode Integration Tests MCP Agent Communication and Workflow Orchestration should orchestrate complex workflows across multiple agents", "status": "passed", "title": "should orchestrate complex workflows across multiple agents", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "MCP Agent Communication and Workflow Orchestration"], "fullName": " Vision Mode Integration Tests MCP Agent Communication and Workflow Orchestration should handle agent failures gracefully", "status": "passed", "title": "should handle agent failures gracefully", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "UI Component Interactions with Backend Systems"], "fullName": " Vision Mode Integration Tests UI Component Interactions with Backend Systems should render main Vision Mode interface with all components", "status": "passed", "title": "should render main Vision Mode interface with all components", "duration": 40, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "UI Component Interactions with Backend Systems"], "fullName": " Vision Mode Integration Tests UI Component Interactions with Backend Systems should update dashboard with real-time metrics", "status": "passed", "title": "should update dashboard with real-time metrics", "duration": 58, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "UI Component Interactions with Backend Systems"], "fullName": " Vision Mode Integration Tests UI Component Interactions with Backend Systems should allow agent control through UI", "status": "passed", "title": "should allow agent control through UI", "duration": 37, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Real-time Data Flow and Visualization"], "fullName": " Vision Mode Integration Tests Real-time Data Flow and Visualization should display semantic analysis results in visualization panel", "status": "passed", "title": "should display semantic analysis results in visualization panel", "duration": 22, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Real-time Data Flow and Visualization"], "fullName": " Vision Mode Integration Tests Real-time Data Flow and Visualization should update visualizations with real-time data", "status": "passed", "title": "should update visualizations with real-time data", "duration": 11, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Export Functionality"], "fullName": " Vision Mode Integration Tests Export Functionality should create export jobs with different formats", "status": "passed", "title": "should create export jobs with different formats", "duration": 24, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Export Functionality"], "fullName": " Vision Mode Integration Tests Export Functionality should support multiple export formats", "status": "passed", "title": "should support multiple export formats", "duration": 18, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Settings Persistence and Configuration Management"], "fullName": " Vision Mode Integration Tests Settings Persistence and Configuration Management should save and load agent configurations", "status": "passed", "title": "should save and load agent configurations", "duration": 27, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Settings Persistence and Configuration Management"], "fullName": " Vision Mode Integration Tests Settings Persistence and Configuration Management should update automation settings", "status": "passed", "title": "should update automation settings", "duration": 23, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Integration with PDF Recreation App"], "fullName": " Vision Mode Integration Tests Integration with PDF Recreation App should integrate seamlessly with existing PDF app routes", "status": "passed", "title": "should integrate seamlessly with existing PDF app routes", "duration": 5, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Integration with PDF Recreation App"], "fullName": " Vision Mode Integration Tests Integration with PDF Recreation App should handle document upload and processing", "status": "passed", "title": "should handle document upload and processing", "duration": 4, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "End-to-End Integration Scenarios"], "fullName": " Vision Mode Integration Tests End-to-End Integration Scenarios should complete full document analysis workflow", "status": "passed", "title": "should complete full document analysis workflow", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "End-to-End Integration Scenarios"], "fullName": " Vision Mode Integration Tests End-to-End Integration Scenarios should handle system-wide error recovery", "status": "passed", "title": "should handle system-wide error recovery", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "End-to-End Integration Scenarios"], "fullName": " Vision Mode Integration Tests End-to-End Integration Scenarios should maintain performance under load", "status": "passed", "title": "should maintain performance under load", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "End-to-End Integration Scenarios"], "fullName": " Vision Mode Integration Tests End-to-End Integration Scenarios should validate data consistency across components", "status": "passed", "title": "should validate data consistency across components", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Performance and Scalability"], "fullName": " Vision Mode Integration Tests Performance and Scalability should handle large datasets efficiently", "status": "passed", "title": "should handle large datasets efficiently", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Performance and Scalability"], "fullName": " Vision Mode Integration Tests Performance and Scalability should manage memory usage effectively", "status": "passed", "title": "should manage memory usage effectively", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Vision Mode Integration Tests", "Performance and Scalability"], "fullName": " Vision Mode Integration Tests Performance and Scalability should scale with concurrent users", "status": "passed", "title": "should scale with concurrent users", "duration": 0, "failureMessages": []}], "startTime": 1754422086827, "endTime": 1754422087108, "status": "passed", "message": "", "name": "C:/Users/<USER>/pdf/src/vision-mode/tests/integration.test.ts"}]}