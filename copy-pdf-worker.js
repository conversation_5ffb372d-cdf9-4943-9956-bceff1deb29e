import { copyFileSync } from 'fs';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

const source = resolve(__dirname, 'node_modules', 'pdfjs-dist', 'build', 'pdf.worker.mjs');
const destination = resolve(__dirname, 'public', 'pdf.worker.mjs');

copyFileSync(source, destination);

console.log('Successfully copied pdf.worker.mjs to public directory.');