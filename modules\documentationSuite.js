/**
 * Documentation Suite
 * Comprehensive documentation system with API docs, user guides, tutorials, and interactive examples
 */

class DocumentationSuite {
    constructor() {
        this.isInitialized = false;
        this.documentationData = new Map();
        this.tutorials = new Map();
        this.examples = new Map();
        this.apiDocs = new Map();
        this.userGuides = new Map();
        this.faqs = new Map();
        this.searchIndex = new Map();
        this.eventListeners = new Map();
        this.analytics = {
            pageViews: new Map(),
            searchQueries: [],
            userFeedback: [],
            popularContent: new Map()
        };
        this.settings = {
            theme: 'light',
            language: 'en',
            showCodeExamples: true,
            enableSearch: true,
            enableAnalytics: true,
            autoGenerateAPI: true
        };
        
        // Note: init() should be called explicitly via initialize() method
        // Do not call init() from constructor as it's async
    }

    async init() {
        console.log('INIT METHOD CALLED - START');
        console.log('this in init method:', this);
        console.log('typeof this:', typeof this);
        console.log('this === undefined:', this === undefined);
        console.log('this === null:', this === null);
        
        if (!this) {
            console.error('CRITICAL ERROR: this is undefined in init method!');
            throw new Error('DocumentationSuite init method called with undefined context');
        }
        
        if (!this.apiDocs) {
            console.error('CRITICAL ERROR: this.apiDocs is undefined!');
            console.log('this object keys:', Object.keys(this || {}));
            throw new Error('DocumentationSuite apiDocs not initialized');
        }
        
        debugger; // This will pause execution in browser dev tools
        try {
            console.log('🚀 Initializing Documentation Suite...');
            
            // Initialize documentation data
            await this.loadDocumentationData();
            
            // Setup search functionality
            this.setupSearchIndex();
            
            // Initialize tutorials and examples
            this.initializeTutorials();
            this.initializeExamples();
            
            // Setup API documentation
            this.setupAPIDocumentation();
            
            // Initialize user guides
            this.initializeUserGuides();
            
            // Setup FAQ system
            this.initializeFAQs();
            
            // Setup analytics
            this.setupAnalytics();
            
            this.isInitialized = true;
            console.log('✅ Documentation Suite initialized successfully');
            
            // Dispatch initialization event
            console.log('Getting available modules for dispatch event...');
            console.log('this before getAvailableModules:', this);
            console.log('this.apiDocs before getAvailableModules:', this.apiDocs);
            let modules;
            try {
                console.log('About to call getAvailableModules...');
                modules = this.getAvailableModules();
                console.log('Available modules retrieved:', modules);
            } catch (moduleError) {
                console.error('Error getting available modules:', moduleError);
                console.error('Module error stack:', moduleError.stack);
                modules = [];
            }
            
            try {
                console.log('About to dispatch event...');
                this.dispatchEvent('documentation-initialized', {
                    timestamp: Date.now(),
                    modules: modules
                });
                console.log('Event dispatched successfully');
            } catch (dispatchError) {
                console.error('Error dispatching event:', dispatchError);
                console.error('Dispatch error stack:', dispatchError.stack);
            }
            
        } catch (error) {
            console.error('❌ Failed to initialize Documentation Suite:', error);
            throw error;
        }
    }

    async loadDocumentationData() {
        // Load comprehensive documentation structure
        this.documentationData.set('overview', {
            title: 'Image Recreation App Documentation',
            description: 'Comprehensive documentation for the Image Recreation App - a sophisticated tool that analyzes uploaded images and recreates them as interactive web components.',
            sections: [
                'getting-started',
                'api-reference',
                'tutorials',
                'examples',
                'user-guides',
                'troubleshooting',
                'faq'
            ],
            lastUpdated: new Date().toISOString()
        });

        // Load module documentation
        const modules = [
            'ImageAnalyzer',
            'RecreationEngine', 
            'BlueprintGenerator',
            'InteractiveEditor',
            'PerformanceOptimizer',
            'ErrorHandler',
            'AIEnhancement',
            'TemplateLibrary',
            'AdvancedExport',
            'TestingSuite',
            'SecurityValidator',
            'DocumentationSuite'
        ];

        for (const module of modules) {
            await this.loadModuleDocumentation(module);
        }
    }

    async loadModuleDocumentation(moduleName) {
        const moduleDoc = {
            name: moduleName,
            description: this.getModuleDescription(moduleName),
            methods: this.getModuleMethods(moduleName),
            properties: this.getModuleProperties(moduleName),
            events: this.getModuleEvents(moduleName),
            examples: this.getModuleExamples(moduleName),
            dependencies: this.getModuleDependencies(moduleName),
            version: '1.0.0',
            lastUpdated: new Date().toISOString()
        };

        this.apiDocs.set(moduleName, moduleDoc);
    }

    getModuleDescription(moduleName) {
        const descriptions = {
            'ImageAnalyzer': 'Advanced image analysis engine with computer vision capabilities for element detection, color extraction, and pattern recognition.',
            'RecreationEngine': 'Powerful code generation engine that converts analyzed image elements into clean, semantic HTML, CSS, and JavaScript.',
            'BlueprintGenerator': 'Complete project packaging system that generates downloadable projects with all dependencies and documentation.',
            'InteractiveEditor': 'Real-time editing system with drag-and-drop manipulation, property panels, and comprehensive editing tools.',
            'PerformanceOptimizer': 'Advanced performance optimization system with Web Workers, virtual rendering, and memory management.',
            'ErrorHandler': 'Centralized error handling system with recovery mechanisms, logging, and user-friendly error reporting.',
            'AIEnhancement': 'AI-powered enhancement system with TensorFlow.js integration for intelligent suggestions and improvements.',
            'TemplateLibrary': 'Comprehensive template management system with search, categorization, sharing, and recommendation features.',
            'AdvancedExport': 'Multi-format export system supporting React, Vue, Angular, and design tool integrations.',
            'TestingSuite': 'Comprehensive testing framework with unit, integration, performance, and visual regression testing.',
            'SecurityValidator': 'Enterprise-grade security system with file validation, input sanitization, and privacy protection.',
            'DocumentationSuite': 'Complete documentation system with API docs, tutorials, examples, and interactive guides.'
        };
        return descriptions[moduleName] || 'Module documentation';
    }

    getModuleMethods(moduleName) {
        // This would typically be auto-generated from code analysis
        const methods = {
            'ImageAnalyzer': [
                { name: 'analyzeImage', description: 'Analyze uploaded image and extract elements', parameters: ['imageData'], returns: 'Promise<AnalysisResult>' },
                { name: 'detectShapes', description: 'Detect shapes in the image', parameters: ['imageData', 'options'], returns: 'Array<Shape>' },
                { name: 'extractColors', description: 'Extract color palette from image', parameters: ['imageData'], returns: 'Array<Color>' },
                { name: 'detectText', description: 'Detect text regions using OCR', parameters: ['imageData'], returns: 'Array<TextRegion>' }
            ],
            'RecreationEngine': [
                { name: 'generateHTML', description: 'Generate HTML structure from analysis', parameters: ['analysisData'], returns: 'string' },
                { name: 'generateCSS', description: 'Generate CSS styles from elements', parameters: ['elements'], returns: 'string' },
                { name: 'generateJavaScript', description: 'Generate JavaScript for interactions', parameters: ['elements'], returns: 'string' },
                { name: 'recreateFromAnalysis', description: 'Complete recreation from analysis data', parameters: ['analysisData'], returns: 'RecreationResult' }
            ],
            'BlueprintGenerator': [
                { name: 'generateBlueprint', description: 'Generate complete project blueprint', parameters: ['recreationData'], returns: 'Blueprint' },
                { name: 'packageProject', description: 'Package project for download', parameters: ['blueprint'], returns: 'Blob' },
                { name: 'generateDocumentation', description: 'Generate project documentation', parameters: ['recreationState'], returns: 'Documentation' }
            ]
        };
        return methods[moduleName] || [];
    }

    getModuleProperties(moduleName) {
        const properties = {
            'ImageAnalyzer': [
                { name: 'isInitialized', type: 'boolean', description: 'Whether the analyzer is initialized' },
                { name: 'supportedFormats', type: 'Array<string>', description: 'Supported image formats' },
                { name: 'analysisOptions', type: 'Object', description: 'Current analysis configuration' }
            ],
            'RecreationEngine': [
                { name: 'isInitialized', type: 'boolean', description: 'Whether the engine is initialized' },
                { name: 'generationOptions', type: 'Object', description: 'Code generation options' },
                { name: 'outputFormats', type: 'Array<string>', description: 'Supported output formats' }
            ]
        };
        return properties[moduleName] || [];
    }

    getModuleEvents(moduleName) {
        const events = {
            'ImageAnalyzer': [
                { name: 'analysis-started', description: 'Fired when image analysis begins' },
                { name: 'analysis-progress', description: 'Fired during analysis progress' },
                { name: 'analysis-completed', description: 'Fired when analysis is complete' },
                { name: 'analysis-error', description: 'Fired when analysis fails' }
            ],
            'RecreationEngine': [
                { name: 'recreation-started', description: 'Fired when recreation begins' },
                { name: 'recreation-progress', description: 'Fired during recreation progress' },
                { name: 'recreation-completed', description: 'Fired when recreation is complete' },
                { name: 'recreation-error', description: 'Fired when recreation fails' }
            ]
        };
        return events[moduleName] || [];
    }

    getModuleExamples(moduleName) {
        const examples = {
            'ImageAnalyzer': [
                {
                    title: 'Basic Image Analysis',
                    code: `
const analyzer = new ImageAnalyzer();
const result = await analyzer.analyzeImage(imageData);
console.log('Detected elements:', result.elements);
                    `,
                    description: 'Analyze an image and get detected elements'
                },
                {
                    title: 'Custom Analysis Options',
                    code: `
const options = {
    detectShapes: true,
    extractColors: true,
    detectText: true,
    minConfidence: 0.8
};
const result = await analyzer.analyzeImage(imageData, options);
                    `,
                    description: 'Analyze with custom options'
                }
            ],
            'RecreationEngine': [
                {
                    title: 'Generate HTML from Analysis',
                    code: `
const engine = new RecreationEngine();
const html = engine.generateHTML(analysisData);
console.log('Generated HTML:', html);
                    `,
                    description: 'Generate HTML structure from analysis data'
                }
            ]
        };
        return examples[moduleName] || [];
    }

    getModuleDependencies(moduleName) {
        const dependencies = {
            'ImageAnalyzer': ['OpenCV.js', 'Tesseract.js'],
            'RecreationEngine': ['ImageAnalyzer'],
            'BlueprintGenerator': ['RecreationEngine', 'JSZip'],
            'InteractiveEditor': ['Fabric.js', 'interact.js'],
            'PerformanceOptimizer': ['Web Workers API'],
            'AIEnhancement': ['TensorFlow.js'],
            'AdvancedExport': ['RecreationEngine', 'BlueprintGenerator']
        };
        return dependencies[moduleName] || [];
    }

    setupSearchIndex() {
        try {
            console.log('Setting up search index...');
            // Create searchable index of all documentation content
            this.searchIndex.clear();
            
            console.log('Indexing API documentation...');
            // Index API documentation
            for (const [moduleName, moduleDoc] of this.apiDocs) {
                console.log(`Processing module: ${moduleName}`, moduleDoc);
                
                // Skip if moduleName is invalid
                if (!moduleName || typeof moduleName !== 'string') {
                    console.warn('Skipping invalid module name:', moduleName);
                    continue;
                }
                
                const description = moduleDoc.description || '';
                console.log(`Module description: ${description}`);
                
                this.indexContent(`api-${moduleName}`, {
                    title: moduleDoc.name || '',
                    content: description,
                    type: 'api',
                    module: moduleName,
                    searchTerms: [(moduleName && typeof moduleName === 'string') ? moduleName.toLowerCase() : '', ...(description && typeof description === 'string' ? description.toLowerCase().split(' ') : [])]
                });
            
                // Index methods
                console.log(`Indexing methods for module: ${moduleName}`);
                if (moduleDoc.methods && Array.isArray(moduleDoc.methods)) {
                    console.log(`Found ${moduleDoc.methods.length} methods`);
                    moduleDoc.methods.forEach(method => {
                        console.log('Processing method:', method);
                        if (!method || !method.name) {
                            console.warn('Skipping invalid method:', method);
                            return; // Skip invalid methods
                        }
                        console.log(`Method name: ${method.name}`);
                        const methodDescription = method.description || '';
                        console.log(`Method description: ${methodDescription}`);
                        this.indexContent(`method-${moduleName}-${method.name}`, {
                            title: `${moduleName}.${method.name}()`,
                            content: methodDescription,
                            type: 'method',
                            module: moduleName,
                            searchTerms: [(method.name && typeof method.name === 'string') ? method.name.toLowerCase() : '', ...(methodDescription && typeof methodDescription === 'string' ? methodDescription.toLowerCase().split(' ') : [])]
                        });
                    });
                } else {
                    console.log(`No methods found for module: ${moduleName}`);
                }
            }
        } catch (error) {
            console.error('Error in setupSearchIndex:', error);
            console.error('Error stack:', error.stack);
        }
    }

    indexContent(id, content) {
        this.searchIndex.set(id, content);
        
        // Add to search terms for quick lookup
        content.searchTerms.forEach(term => {
            if (!this.searchIndex.has(`term-${term}`)) {
                this.searchIndex.set(`term-${term}`, []);
            }
            this.searchIndex.get(`term-${term}`).push(id);
        });
    }

    initializeTutorials() {
        const tutorials = [
            {
                id: 'getting-started',
                title: 'Getting Started with Image Recreation App',
                description: 'Learn the basics of using the Image Recreation App',
                difficulty: 'beginner',
                estimatedTime: '10 minutes',
                steps: [
                    {
                        title: 'Upload Your Image',
                        content: 'Start by uploading an image file (JPG, PNG, SVG, or WebP) using the upload button.',
                        code: null,
                        image: null
                    },
                    {
                        title: 'Analyze the Image',
                        content: 'Click "Analyze Image" to detect elements, colors, and patterns in your image.',
                        code: null,
                        image: null
                    },
                    {
                        title: 'Review Analysis Results',
                        content: 'Examine the detected elements and their properties in the analysis panel.',
                        code: null,
                        image: null
                    },
                    {
                        title: 'Generate Recreation',
                        content: 'Click "Recreate" to generate HTML, CSS, and JavaScript code from the analysis.',
                        code: null,
                        image: null
                    },
                    {
                        title: 'Export Your Project',
                        content: 'Download your recreated project as a complete package with all files.',
                        code: null,
                        image: null
                    }
                ],
                tags: ['beginner', 'tutorial', 'basics'],
                lastUpdated: new Date().toISOString()
            },
            {
                id: 'advanced-features',
                title: 'Advanced Features and Customization',
                description: 'Explore advanced features like AI enhancement, templates, and interactive editing',
                difficulty: 'intermediate',
                estimatedTime: '20 minutes',
                steps: [
                    {
                        title: 'AI-Powered Enhancement',
                        content: 'Use AI features to improve pattern recognition and get intelligent suggestions.',
                        code: `
// Enable AI enhancement
const aiEnhancement = new AIEnhancement();
await aiEnhancement.enhanceAnalysis(analysisData);
                        `,
                        image: null
                    },
                    {
                        title: 'Template Library',
                        content: 'Browse and apply templates from the community library.',
                        code: `
// Search templates
const templates = templateLibrary.searchTemplates('dashboard');
const template = templates[0];
templateLibrary.applyTemplate(template.id);
                        `,
                        image: null
                    },
                    {
                        title: 'Interactive Editing',
                        content: 'Use the interactive editor to fine-tune your recreation.',
                        code: `
// Enable interactive editing
const editor = new InteractiveEditor();
editor.enableEditing();
editor.selectElement(elementId);
                        `,
                        image: null
                    }
                ],
                tags: ['advanced', 'ai', 'templates', 'editing'],
                lastUpdated: new Date().toISOString()
            },
            {
                id: 'api-integration',
                title: 'API Integration and Automation',
                description: 'Learn how to integrate the Image Recreation App into your own applications',
                difficulty: 'advanced',
                estimatedTime: '30 minutes',
                steps: [
                    {
                        title: 'Initialize the Application',
                        content: 'Set up the Image Recreation App in your project.',
                        code: `
// Initialize the application (assuming ImageRecreationApp is already imported)
const app = new ImageRecreationApp();
await app.initialize();
                        `,
                        image: null
                    },
                    {
                        title: 'Programmatic Image Analysis',
                        content: 'Analyze images programmatically using the API.',
                        code: `
// Analyze image programmatically
const imageData = await loadImageData('path/to/image.jpg');
const analysis = await app.imageAnalyzer.analyzeImage(imageData);
console.log('Analysis results:', analysis);
                        `,
                        image: null
                    },
                    {
                        title: 'Automated Recreation',
                        content: 'Generate code automatically without user interaction.',
                        code: `
// Generate recreation automatically
const recreation = await app.recreationEngine.recreateFromAnalysis(analysis);
const blueprint = await app.blueprintGenerator.generateBlueprint(recreation);
                        `,
                        image: null
                    }
                ],
                tags: ['api', 'integration', 'automation', 'advanced'],
                lastUpdated: new Date().toISOString()
            }
        ];

        tutorials.forEach(tutorial => {
            this.tutorials.set(tutorial.id, tutorial);
        });
    }

    initializeExamples() {
        const examples = [
            {
                id: 'basic-usage',
                title: 'Basic Usage Example',
                description: 'Simple example of analyzing and recreating an image',
                category: 'basic',
                code: `
// Basic usage example
const app = new ImageRecreationApp();

// Upload and analyze image
const fileInput = document.getElementById('imageUpload');
fileInput.addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        // Analyze the image
        const analysis = await app.analyzeImage(file);
        console.log('Analysis complete:', analysis);
        
        // Generate recreation
        const recreation = await app.recreateFromAnalysis(analysis);
        console.log('Recreation complete:', recreation);
        
        // Export project
        const blueprint = await app.generateBlueprint(recreation);
        app.downloadProject(blueprint);
    }
});
                `,
                tags: ['basic', 'upload', 'analysis', 'recreation'],
                lastUpdated: new Date().toISOString()
            },
            {
                id: 'custom-analysis',
                title: 'Custom Analysis Configuration',
                description: 'Example of using custom analysis options',
                category: 'intermediate',
                code: `
// Custom analysis configuration
const analysisOptions = {
    detectShapes: true,
    extractColors: true,
    detectText: true,
    minConfidence: 0.8,
    maxElements: 50,
    colorPalette: 'dominant',
    shapeDetection: {
        circles: true,
        rectangles: true,
        polygons: true,
        curves: true
    },
    textDetection: {
        ocr: true,
        language: 'eng',
        minTextSize: 12
    }
};

const analysis = await app.imageAnalyzer.analyzeImage(imageData, analysisOptions);
                `,
                tags: ['analysis', 'configuration', 'options'],
                lastUpdated: new Date().toISOString()
            },
            {
                id: 'ai-enhancement',
                title: 'AI-Powered Enhancement',
                description: 'Example of using AI features for better results',
                category: 'advanced',
                code: `
// AI-powered enhancement example
const aiOptions = {
    enablePatternRecognition: true,
    enableLayoutUnderstanding: true,
    enableColorHarmony: true,
    enableSmartGrouping: true,
    confidenceThreshold: 0.7
};

// Enhance analysis with AI
const enhancedAnalysis = await app.aiEnhancement.enhanceAnalysis(analysis, aiOptions);

// Get AI suggestions
const suggestions = await app.aiEnhancement.getSuggestions(enhancedAnalysis);
console.log('AI suggestions:', suggestions);

// Apply AI improvements
const improvedRecreation = await app.aiEnhancement.applyImprovements(recreation, suggestions);
                `,
                tags: ['ai', 'enhancement', 'suggestions', 'advanced'],
                lastUpdated: new Date().toISOString()
            },
            {
                id: 'template-integration',
                title: 'Template Library Integration',
                description: 'Example of working with templates',
                category: 'intermediate',
                code: `
// Template library integration
const templateLibrary = app.templateLibrary;

// Search for templates
const searchResults = templateLibrary.searchTemplates('dashboard', {
    category: 'web-app',
    tags: ['modern', 'responsive'],
    difficulty: 'intermediate'
});

// Apply a template
const template = searchResults[0];
await templateLibrary.applyTemplate(template.id);

// Save custom template
const customTemplate = {
    name: 'My Custom Template',
    description: 'Custom template based on my design',
    category: 'custom',
    tags: ['personal', 'unique'],
    recreationData: currentRecreation
};

await templateLibrary.saveTemplate(customTemplate);
                `,
                tags: ['templates', 'library', 'search', 'custom'],
                lastUpdated: new Date().toISOString()
            },
            {
                id: 'export-formats',
                title: 'Multiple Export Formats',
                description: 'Example of exporting to different formats',
                category: 'intermediate',
                code: `
// Multiple export formats example
const exportOptions = {
    format: 'react', // 'html', 'react', 'vue', 'angular', 'svelte'
    framework: {
        typescript: true,
        styledComponents: true,
        proptypes: true
    },
    css: {
        framework: 'tailwind', // 'css', 'tailwind', 'styled-components'
        responsive: true,
        darkMode: true
    },
    optimization: {
        minify: true,
        treeshake: true,
        bundleAnalysis: true
    }
};

// Export to React component
const reactExport = await app.advancedExport.exportToReact(recreation, exportOptions);

// Export to design tools
const figmaExport = await app.advancedExport.exportToFigma(recreation);
const sketchExport = await app.advancedExport.exportToSketch(recreation);

// Download exports
app.downloadFile(reactExport.code, 'Component.tsx');
app.downloadFile(figmaExport, 'design.fig');
                `,
                tags: ['export', 'formats', 'react', 'design-tools'],
                lastUpdated: new Date().toISOString()
            }
        ];

        examples.forEach(example => {
            this.examples.set(example.id, example);
        });
    }

    setupAPIDocumentation() {
        // API documentation is already loaded in loadModuleDocumentation
        // This method sets up additional API documentation features
        
        // Create API reference structure
        const apiReference = {
            overview: {
                title: 'API Reference Overview',
                description: 'Complete API reference for the Image Recreation App',
                version: '1.0.0',
                baseUrl: 'https://image-recreation-app.com/api',
                authentication: 'API Key',
                rateLimit: '1000 requests per hour'
            },
            endpoints: new Map(),
            schemas: new Map(),
            examples: new Map()
        };

        // Add REST API endpoints (if applicable)
        apiReference.endpoints.set('analyze', {
            method: 'POST',
            path: '/api/analyze',
            description: 'Analyze an uploaded image',
            parameters: {
                image: { type: 'file', required: true, description: 'Image file to analyze' },
                options: { type: 'object', required: false, description: 'Analysis options' }
            },
            responses: {
                200: { description: 'Analysis successful', schema: 'AnalysisResult' },
                400: { description: 'Invalid request', schema: 'Error' },
                500: { description: 'Server error', schema: 'Error' }
            }
        });

        this.apiDocs.set('api-reference', apiReference);
    }

    initializeUserGuides() {
        const userGuides = [
            {
                id: 'user-interface-guide',
                title: 'User Interface Guide',
                description: 'Complete guide to the Image Recreation App interface',
                sections: [
                    {
                        title: 'Main Dashboard',
                        content: 'The main dashboard provides access to all core features including image upload, analysis, recreation, and export.',
                        subsections: [
                            { title: 'Upload Area', content: 'Drag and drop images or click to browse files.' },
                            { title: 'Analysis Panel', content: 'View detected elements and their properties.' },
                            { title: 'Recreation Preview', content: 'See the generated code and live preview.' },
                            { title: 'Export Options', content: 'Choose from multiple export formats and options.' }
                        ]
                    },
                    {
                        title: 'Analysis Tools',
                        content: 'Advanced tools for customizing image analysis.',
                        subsections: [
                            { title: 'Shape Detection', content: 'Configure shape detection algorithms and thresholds.' },
                            { title: 'Color Extraction', content: 'Customize color palette extraction settings.' },
                            { title: 'Text Recognition', content: 'OCR settings and text detection options.' }
                        ]
                    },
                    {
                        title: 'Interactive Editor',
                        content: 'Real-time editing tools for fine-tuning recreations.',
                        subsections: [
                            { title: 'Element Selection', content: 'Select and modify individual elements.' },
                            { title: 'Property Panels', content: 'Edit element properties like size, color, and position.' },
                            { title: 'Layer Management', content: 'Organize elements in layers and groups.' }
                        ]
                    }
                ],
                lastUpdated: new Date().toISOString()
            },
            {
                id: 'troubleshooting-guide',
                title: 'Troubleshooting Guide',
                description: 'Solutions to common issues and problems',
                sections: [
                    {
                        title: 'Upload Issues',
                        content: 'Common problems with image uploads and solutions.',
                        subsections: [
                            { title: 'File Format Not Supported', content: 'Ensure your image is in JPG, PNG, SVG, or WebP format.' },
                            { title: 'File Too Large', content: 'Reduce image size to under 10MB or use compression.' },
                            { title: 'Upload Fails', content: 'Check internet connection and try again.' }
                        ]
                    },
                    {
                        title: 'Analysis Problems',
                        content: 'Issues with image analysis and detection.',
                        subsections: [
                            { title: 'Poor Detection Results', content: 'Try adjusting analysis settings or using higher quality images.' },
                            { title: 'Analysis Takes Too Long', content: 'Large images may take longer to process. Consider resizing.' },
                            { title: 'No Elements Detected', content: 'Ensure image has clear, distinct elements and good contrast.' }
                        ]
                    },
                    {
                        title: 'Export Issues',
                        content: 'Problems with code generation and export.',
                        subsections: [
                            { title: 'Export Fails', content: 'Check browser compatibility and try different export format.' },
                            { title: 'Generated Code Issues', content: 'Validate HTML/CSS and check for browser-specific issues.' },
                            { title: 'Download Problems', content: 'Ensure browser allows downloads and check popup blockers.' }
                        ]
                    }
                ],
                lastUpdated: new Date().toISOString()
            },
            {
                id: 'best-practices',
                title: 'Best Practices Guide',
                description: 'Tips and best practices for optimal results',
                sections: [
                    {
                        title: 'Image Preparation',
                        content: 'How to prepare images for best analysis results.',
                        subsections: [
                            { title: 'Image Quality', content: 'Use high-resolution images with good contrast and clarity.' },
                            { title: 'File Format', content: 'PNG is best for graphics, JPG for photos, SVG for vector graphics.' },
                            { title: 'Composition', content: 'Ensure elements are clearly separated and well-defined.' }
                        ]
                    },
                    {
                        title: 'Analysis Optimization',
                        content: 'Tips for getting better analysis results.',
                        subsections: [
                            { title: 'Settings Configuration', content: 'Adjust confidence thresholds based on image complexity.' },
                            { title: 'Element Types', content: 'Enable only needed detection types for faster processing.' },
                            { title: 'Preprocessing', content: 'Consider image preprocessing for complex or noisy images.' }
                        ]
                    },
                    {
                        title: 'Code Quality',
                        content: 'Best practices for generated code quality.',
                        subsections: [
                            { title: 'Semantic HTML', content: 'Use semantic HTML options for better accessibility.' },
                            { title: 'CSS Organization', content: 'Choose appropriate CSS methodology (BEM, CSS Modules, etc.).' },
                            { title: 'Performance', content: 'Enable optimization options for production use.' }
                        ]
                    }
                ],
                lastUpdated: new Date().toISOString()
            }
        ];

        userGuides.forEach(guide => {
            this.userGuides.set(guide.id, guide);
        });
    }

    initializeFAQs() {
        const faqs = [
            {
                id: 'general',
                category: 'General',
                questions: [
                    {
                        question: 'What is the Image Recreation App?',
                        answer: 'The Image Recreation App is a sophisticated tool that analyzes uploaded images and recreates them as interactive web components with full code generation capabilities. It uses advanced computer vision and AI to detect elements, extract properties, and generate clean, semantic code.',
                        tags: ['general', 'overview']
                    },
                    {
                        question: 'What image formats are supported?',
                        answer: 'The app supports JPG, PNG, SVG, and WebP image formats. For best results, use high-resolution images with good contrast and clarity.',
                        tags: ['formats', 'upload']
                    },
                    {
                        question: 'Is there a file size limit?',
                        answer: 'Yes, the maximum file size is 10MB. For larger files, consider compressing the image or reducing its resolution while maintaining quality.',
                        tags: ['limits', 'upload']
                    },
                    {
                        question: 'How accurate is the element detection?',
                        answer: 'Detection accuracy depends on image quality and complexity. The app uses advanced algorithms and AI to achieve high accuracy, typically 85-95% for well-prepared images.',
                        tags: ['accuracy', 'detection']
                    }
                ]
            },
            {
                id: 'technical',
                category: 'Technical',
                questions: [
                    {
                        question: 'What technologies does the app use?',
                        answer: 'The app uses OpenCV.js for computer vision, TensorFlow.js for AI features, Fabric.js for canvas manipulation, and various other modern web technologies for optimal performance.',
                        tags: ['technology', 'stack']
                    },
                    {
                        question: 'Can I integrate this into my own application?',
                        answer: 'Yes! The app provides a comprehensive API for integration. Check the API documentation and integration tutorials for detailed instructions.',
                        tags: ['integration', 'api']
                    },
                    {
                        question: 'What code formats can be exported?',
                        answer: 'The app can export HTML/CSS/JS, React components, Vue components, Angular components, Svelte components, and design tool formats (Figma, Sketch, Adobe XD).',
                        tags: ['export', 'formats']
                    },
                    {
                        question: 'Is the generated code production-ready?',
                        answer: 'Yes, the generated code follows modern best practices, includes accessibility features, and can be optimized for production use with minification and bundling options.',
                        tags: ['code-quality', 'production']
                    }
                ]
            },
            {
                id: 'features',
                category: 'Features',
                questions: [
                    {
                        question: 'What is AI enhancement?',
                        answer: 'AI enhancement uses machine learning to improve pattern recognition, suggest layout improvements, generate color harmonies, and provide intelligent element grouping for better results.',
                        tags: ['ai', 'enhancement']
                    },
                    {
                        question: 'How does the template library work?',
                        answer: 'The template library provides pre-built templates and allows you to save custom templates. You can search, categorize, and share templates with the community.',
                        tags: ['templates', 'library']
                    },
                    {
                        question: 'Can I edit the recreation after generation?',
                        answer: 'Yes! The interactive editor allows real-time editing with drag-and-drop manipulation, property panels, undo/redo, and comprehensive editing tools.',
                        tags: ['editing', 'interactive']
                    },
                    {
                        question: 'What security measures are in place?',
                        answer: 'The app includes comprehensive security features: file validation, input sanitization, privacy protection, rate limiting, and CSRF protection to ensure safe operation.',
                        tags: ['security', 'privacy']
                    }
                ]
            }
        ];

        faqs.forEach(faq => {
            this.faqs.set(faq.id, faq);
        });
    }

    setupAnalytics() {
        if (!this.settings.enableAnalytics) return;

        // Initialize analytics tracking
        this.analytics = {
            pageViews: new Map(),
            searchQueries: [],
            userFeedback: [],
            popularContent: new Map(),
            sessionData: {
                startTime: Date.now(),
                pageViews: 0,
                searchCount: 0,
                feedbackCount: 0
            }
        };

        // Track page views
        this.trackPageView('documentation-home');
    }

    // Search functionality
    search(query, options = {}) {
        const {
            type = 'all', // 'all', 'api', 'tutorials', 'examples', 'guides', 'faq'
            limit = 10,
            includeContent = false
        } = options;

        const results = [];
        const searchTerms = (query && typeof query === 'string') ? query.toLowerCase().split(' ') : [];

        // Track search query
        this.trackSearch(query);

        // Search in different content types
        if (type === 'all' || type === 'api') {
            results.push(...this.searchAPI(searchTerms, limit));
        }
        if (type === 'all' || type === 'tutorials') {
            results.push(...this.searchTutorials(searchTerms, limit));
        }
        if (type === 'all' || type === 'examples') {
            results.push(...this.searchExamples(searchTerms, limit));
        }
        if (type === 'all' || type === 'guides') {
            results.push(...this.searchUserGuides(searchTerms, limit));
        }
        if (type === 'all' || type === 'faq') {
            results.push(...this.searchFAQs(searchTerms, limit));
        }

        // Sort by relevance
        results.sort((a, b) => b.relevance - a.relevance);

        return results.slice(0, limit);
    }

    searchAPI(searchTerms, limit) {
        const results = [];
        
        for (const [moduleName, moduleDoc] of this.apiDocs) {
            if (moduleName === 'api-reference') continue;
            
            const relevance = this.calculateRelevance(searchTerms, [
                moduleDoc.name,
                moduleDoc.description || '',
                ...(moduleDoc.methods || []).map(m => m.name || ''),
                ...(moduleDoc.methods || []).map(m => m.description || '')
            ]);

            if (relevance > 0) {
                results.push({
                    type: 'api',
                    title: moduleDoc.name,
                    description: moduleDoc.description,
                    url: `#api/${moduleName}`,
                    relevance,
                    module: moduleName
                });
            }

            // Search methods
            if (moduleDoc.methods && Array.isArray(moduleDoc.methods)) {
                moduleDoc.methods.forEach(method => {
                    const methodRelevance = this.calculateRelevance(searchTerms, [
                        method.name,
                        method.description,
                        moduleName
                    ]);

                    if (methodRelevance > 0) {
                        results.push({
                            type: 'method',
                            title: `${moduleName}.${method.name}()`,
                            description: method.description,
                            url: `#api/${moduleName}/${method.name}`,
                            relevance: methodRelevance,
                            module: moduleName,
                            method: method.name
                        });
                    }
                });
            }
        }

        return results;
    }

    searchTutorials(searchTerms, limit) {
        const results = [];
        
        for (const [tutorialId, tutorial] of this.tutorials) {
            const relevance = this.calculateRelevance(searchTerms, [
                tutorial.title || '',
                tutorial.description || '',
                ...(tutorial.tags || []),
                ...(tutorial.steps || []).map(s => s.title || ''),
                ...(tutorial.steps || []).map(s => s.content || '')
            ]);

            if (relevance > 0) {
                results.push({
                    type: 'tutorial',
                    title: tutorial.title,
                    description: tutorial.description,
                    url: `#tutorials/${tutorialId}`,
                    relevance,
                    difficulty: tutorial.difficulty,
                    estimatedTime: tutorial.estimatedTime
                });
            }
        }

        return results;
    }

    searchExamples(searchTerms, limit) {
        const results = [];
        
        for (const [exampleId, example] of this.examples) {
            const relevance = this.calculateRelevance(searchTerms, [
                example.title || '',
                example.description || '',
                ...(example.tags || []),
                example.code || ''
            ]);

            if (relevance > 0) {
                results.push({
                    type: 'example',
                    title: example.title,
                    description: example.description,
                    url: `#examples/${exampleId}`,
                    relevance,
                    category: example.category
                });
            }
        }

        return results;
    }

    searchUserGuides(searchTerms, limit) {
        const results = [];
        
        for (const [guideId, guide] of this.userGuides) {
            const relevance = this.calculateRelevance(searchTerms, [
                guide.title || '',
                guide.description || '',
                ...(guide.sections || []).map(s => s.title || ''),
                ...(guide.sections || []).map(s => s.content || '')
            ]);

            if (relevance > 0) {
                results.push({
                    type: 'guide',
                    title: guide.title,
                    description: guide.description,
                    url: `#guides/${guideId}`,
                    relevance
                });
            }
        }

        return results;
    }

    searchFAQs(searchTerms, limit) {
        const results = [];
        
        for (const [faqId, faq] of this.faqs) {
            if (faq.questions && Array.isArray(faq.questions)) {
                faq.questions.forEach((qa, index) => {
                    const relevance = this.calculateRelevance(searchTerms, [
                        qa.question || '',
                        qa.answer || '',
                        ...(qa.tags || []),
                        faq.category || ''
                    ]);

                    if (relevance > 0) {
                        results.push({
                            type: 'faq',
                            title: qa.question,
                            description: qa.answer.substring(0, 150) + '...',
                            url: `#faq/${faqId}#${index}`,
                            relevance,
                            category: faq.category
                        });
                    }
                });
            }
        }

        return results;
    }

    calculateRelevance(searchTerms, content) {
        let relevance = 0;
        // Filter out null/undefined content and convert to strings
        const validContent = content.filter(item => item != null).map(item => String(item));
        const contentText = validContent.join(' ').toLowerCase();

        searchTerms.forEach(term => {
            const termCount = (contentText.match(new RegExp(term, 'g')) || []).length;
            relevance += termCount;
            
            // Boost relevance for exact matches in titles
            if (validContent[0] && typeof validContent[0] === 'string' && validContent[0].toLowerCase().includes(term)) {
                relevance += 5;
            }
        });

        return relevance;
    }

    // Documentation generation
    generateAPIDocumentation(moduleName) {
        const moduleDoc = this.apiDocs.get(moduleName);
        if (!moduleDoc) return null;

        return {
            html: this.generateAPIHTML(moduleDoc),
            markdown: this.generateAPIMarkdown(moduleDoc),
            json: JSON.stringify(moduleDoc, null, 2)
        };
    }

    generateAPIHTML(moduleDoc) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${moduleDoc.name} API Documentation</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { border-bottom: 2px solid #e1e5e9; padding-bottom: 20px; margin-bottom: 30px; }
        .method { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 20px 0; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 4px; font-family: 'Monaco', 'Consolas', monospace; }
        .property { background: #fff3cd; border-left: 4px solid #ffc107; padding: 10px; margin: 10px 0; }
        .event { background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${moduleDoc.name}</h1>
            <p>${moduleDoc.description}</p>
            <p><strong>Version:</strong> ${moduleDoc.version} | <strong>Last Updated:</strong> ${new Date(moduleDoc.lastUpdated).toLocaleDateString()}</p>
        </div>

        <h2>Methods</h2>
        ${moduleDoc.methods.map(method => `
            <div class="method">
                <h3>${method.name}(${method.parameters.join(', ')})</h3>
                <p>${method.description}</p>
                <p><strong>Returns:</strong> ${method.returns}</p>
            </div>
        `).join('')}

        <h2>Properties</h2>
        ${moduleDoc.properties.map(prop => `
            <div class="property">
                <h3>${prop.name}: ${prop.type}</h3>
                <p>${prop.description}</p>
            </div>
        `).join('')}

        <h2>Events</h2>
        ${moduleDoc.events.map(event => `
            <div class="event">
                <h3>${event.name}</h3>
                <p>${event.description}</p>
            </div>
        `).join('')}

        <h2>Examples</h2>
        ${moduleDoc.examples.map(example => `
            <div class="example">
                <h3>${example.title}</h3>
                <p>${example.description}</p>
                <div class="code"><pre>${example.code}</pre></div>
            </div>
        `).join('')}
    </div>
</body>
</html>
        `;
    }

    generateAPIMarkdown(moduleDoc) {
        return `
# ${moduleDoc.name}

${moduleDoc.description}

**Version:** ${moduleDoc.version}  
**Last Updated:** ${new Date(moduleDoc.lastUpdated).toLocaleDateString()}

## Methods

${moduleDoc.methods.map(method => `
### ${method.name}(${method.parameters.join(', ')})

${method.description}

**Returns:** ${method.returns}
`).join('')}

## Properties

${moduleDoc.properties.map(prop => `
### ${prop.name}: ${prop.type}

${prop.description}
`).join('')}

## Events

${moduleDoc.events.map(event => `
### ${event.name}

${event.description}
`).join('')}

## Examples

${moduleDoc.examples.map(example => `
### ${example.title}

${example.description}

\`\`\`javascript
${example.code}
\`\`\`
`).join('')}
        `;
    }

    generateUserGuide(guideId) {
        const guide = this.userGuides.get(guideId);
        if (!guide) return null;

        return {
            html: this.generateGuideHTML(guide),
            markdown: this.generateGuideMarkdown(guide),
            pdf: this.generateGuidePDF(guide)
        };
    }

    generateGuideHTML(guide) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${guide.title}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 30px 0; }
        .subsection { margin: 20px 0; padding-left: 20px; border-left: 3px solid #e1e5e9; }
        h1 { color: #2c3e50; }
        h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 10px; }
        h3 { color: #7f8c8d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>${guide.title}</h1>
        <p>${guide.description}</p>
        
        ${guide.sections.map(section => `
            <div class="section">
                <h2>${section.title}</h2>
                <p>${section.content}</p>
                
                ${section.subsections ? section.subsections.map(subsection => `
                    <div class="subsection">
                        <h3>${subsection.title}</h3>
                        <p>${subsection.content}</p>
                    </div>
                `).join('') : ''}
            </div>
        `).join('')}
    </div>
</body>
</html>
        `;
    }

    generateGuideMarkdown(guide) {
        return `
# ${guide.title}

${guide.description}

${guide.sections.map(section => `
## ${section.title}

${section.content}

${section.subsections ? section.subsections.map(subsection => `
### ${subsection.title}

${subsection.content}
`).join('') : ''}
`).join('')}
        `;
    }

    generateTutorial(tutorialId) {
        const tutorial = this.tutorials.get(tutorialId);
        if (!tutorial) return null;

        return {
            html: this.generateTutorialHTML(tutorial),
            interactive: this.generateInteractiveTutorial(tutorial)
        };
    }

    generateTutorialHTML(tutorial) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${tutorial.title}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .tutorial-header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .step { background: white; border: 1px solid #e1e5e9; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .step-number { background: #007bff; color: white; width: 30px; height: 30px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-right: 15px; }
        .code-block { background: #f1f3f4; padding: 15px; border-radius: 4px; font-family: 'Monaco', 'Consolas', monospace; margin: 10px 0; }
        .difficulty { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .beginner { background: #d4edda; color: #155724; }
        .intermediate { background: #fff3cd; color: #856404; }
        .advanced { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="tutorial-header">
            <h1>${tutorial.title}</h1>
            <p>${tutorial.description}</p>
            <p>
                <span class="difficulty ${tutorial.difficulty}">${tutorial.difficulty.toUpperCase()}</span>
                <strong>Estimated Time:</strong> ${tutorial.estimatedTime}
            </p>
        </div>

        ${tutorial.steps.map((step, index) => `
            <div class="step">
                <h2><span class="step-number">${index + 1}</span>${step.title}</h2>
                <p>${step.content}</p>
                ${step.code ? `<div class="code-block"><pre>${step.code}</pre></div>` : ''}
            </div>
        `).join('')}
    </div>
</body>
</html>
        `;
    }

    generateInteractiveTutorial(tutorial) {
        // This would generate an interactive tutorial with step-by-step guidance
        return {
            type: 'interactive',
            tutorial: tutorial,
            currentStep: 0,
            completed: false,
            progress: 0
        };
    }

    // Analytics and tracking
    trackPageView(page) {
        if (!this.settings.enableAnalytics) return;

        const views = this.analytics.pageViews.get(page) || 0;
        this.analytics.pageViews.set(page, views + 1);
        this.analytics.sessionData.pageViews++;

        console.log(`📊 Page view tracked: ${page}`);
    }

    trackSearch(query) {
        if (!this.settings.enableAnalytics) return;

        this.analytics.searchQueries.push({
            query,
            timestamp: Date.now(),
            sessionId: this.analytics.sessionData.startTime
        });
        this.analytics.sessionData.searchCount++;

        console.log(`🔍 Search tracked: ${query}`);
    }

    trackFeedback(feedback) {
        if (!this.settings.enableAnalytics) return;

        this.analytics.userFeedback.push({
            ...feedback,
            timestamp: Date.now(),
            sessionId: this.analytics.sessionData.startTime
        });
        this.analytics.sessionData.feedbackCount++;

        console.log('💬 Feedback tracked:', feedback);
    }

    // Export functionality
    exportDocumentation(format = 'html', options = {}) {
        const {
            includeAPI = true,
            includeTutorials = true,
            includeExamples = true,
            includeGuides = true,
            includeFAQ = true,
            theme = 'default'
        } = options;

        const documentation = {
            overview: this.documentationData.get('overview'),
            api: includeAPI ? Object.fromEntries(this.apiDocs) : null,
            tutorials: includeTutorials ? Object.fromEntries(this.tutorials) : null,
            examples: includeExamples ? Object.fromEntries(this.examples) : null,
            guides: includeGuides ? Object.fromEntries(this.userGuides) : null,
            faq: includeFAQ ? Object.fromEntries(this.faqs) : null,
            generatedAt: new Date().toISOString()
        };

        switch (format) {
            case 'html':
                return this.generateCompleteHTML(documentation, theme);
            case 'markdown':
                return this.generateCompleteMarkdown(documentation);
            case 'json':
                return JSON.stringify(documentation, null, 2);
            case 'pdf':
                return this.generateCompletePDF(documentation);
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }

    generateCompleteHTML(documentation, theme) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Recreation App - Complete Documentation</title>
    <style>
        /* Complete documentation styles */
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 0; padding: 0; }
        .sidebar { position: fixed; left: 0; top: 0; width: 250px; height: 100vh; background: #f8f9fa; border-right: 1px solid #e1e5e9; overflow-y: auto; }
        .content { margin-left: 250px; padding: 20px; }
        .nav-item { padding: 8px 16px; cursor: pointer; border-bottom: 1px solid #e1e5e9; }
        .nav-item:hover { background: #e9ecef; }
        .section { margin: 30px 0; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 4px; font-family: 'Monaco', 'Consolas', monospace; }
        h1 { color: #2c3e50; }
        h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 10px; }
        h3 { color: #7f8c8d; }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="nav-item" onclick="showSection('overview')">Overview</div>
        ${documentation.api ? '<div class="nav-item" onclick="showSection(\'api\')">API Reference</div>' : ''}
        ${documentation.tutorials ? '<div class="nav-item" onclick="showSection(\'tutorials\')">Tutorials</div>' : ''}
        ${documentation.examples ? '<div class="nav-item" onclick="showSection(\'examples\')">Examples</div>' : ''}
        ${documentation.guides ? '<div class="nav-item" onclick="showSection(\'guides\')">User Guides</div>' : ''}
        ${documentation.faq ? '<div class="nav-item" onclick="showSection(\'faq\')">FAQ</div>' : ''}
    </div>
    
    <div class="content">
        <div id="overview" class="section">
            <h1>${documentation.overview.title}</h1>
            <p>${documentation.overview.description}</p>
        </div>
        
        ${documentation.api ? this.generateAPISection(documentation.api) : ''}
        ${documentation.tutorials ? this.generateTutorialsSection(documentation.tutorials) : ''}
        ${documentation.examples ? this.generateExamplesSection(documentation.examples) : ''}
        ${documentation.guides ? this.generateGuidesSection(documentation.guides) : ''}
        ${documentation.faq ? this.generateFAQSection(documentation.faq) : ''}
    </div>

    <script>
        function showSection(sectionId) {
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });
            document.getElementById(sectionId).style.display = 'block';
        }
        
        // Show overview by default
        showSection('overview');
    </script>
</body>
</html>
        `;
    }

    generateAPISection(apiDocs) {
        return `
        <div id="api" class="section" style="display: none;">
            <h1>API Reference</h1>
            ${Object.entries(apiDocs).map(([moduleName, moduleDoc]) => {
                if (moduleName === 'api-reference') return '';
                return `
                <div class="module">
                    <h2>${moduleDoc.name}</h2>
                    <p>${moduleDoc.description}</p>
                    <!-- Methods, properties, events, examples would be rendered here -->
                </div>
                `;
            }).join('')}
        </div>
        `;
    }

    generateTutorialsSection(tutorials) {
        return `
        <div id="tutorials" class="section" style="display: none;">
            <h1>Tutorials</h1>
            ${Object.entries(tutorials).map(([tutorialId, tutorial]) => `
                <div class="tutorial">
                    <h2>${tutorial.title}</h2>
                    <p>${tutorial.description}</p>
                    <p><strong>Difficulty:</strong> ${tutorial.difficulty} | <strong>Time:</strong> ${tutorial.estimatedTime}</p>
                </div>
            `).join('')}
        </div>
        `;
    }

    generateExamplesSection(examples) {
        return `
        <div id="examples" class="section" style="display: none;">
            <h1>Examples</h1>
            ${Object.entries(examples).map(([exampleId, example]) => `
                <div class="example">
                    <h2>${example.title}</h2>
                    <p>${example.description}</p>
                    <div class="code"><pre>${example.code}</pre></div>
                </div>
            `).join('')}
        </div>
        `;
    }

    generateGuidesSection(guides) {
        return `
        <div id="guides" class="section" style="display: none;">
            <h1>User Guides</h1>
            ${Object.entries(guides).map(([guideId, guide]) => `
                <div class="guide">
                    <h2>${guide.title}</h2>
                    <p>${guide.description}</p>
                </div>
            `).join('')}
        </div>
        `;
    }

    generateFAQSection(faqs) {
        return `
        <div id="faq" class="section" style="display: none;">
            <h1>Frequently Asked Questions</h1>
            ${Object.entries(faqs).map(([faqId, faq]) => `
                <div class="faq-category">
                    <h2>${faq.category}</h2>
                    ${faq.questions.map(qa => `
                        <div class="faq-item">
                            <h3>${qa.question}</h3>
                            <p>${qa.answer}</p>
                        </div>
                    `).join('')}
                </div>
            `).join('')}
        </div>
        `;
    }

    // Utility methods
    getAvailableModules() {
        console.log('getAvailableModules called');
        console.log('this.apiDocs:', this.apiDocs);
        console.log('this.apiDocs instanceof Map:', this.apiDocs instanceof Map);
        
        if (!this.apiDocs || !(this.apiDocs instanceof Map)) {
            console.warn('apiDocs not properly initialized');
            return [];
        }
        
        console.log('Getting keys from apiDocs...');
        const keys = Array.from(this.apiDocs.keys());
        console.log('Keys:', keys);
        
        console.log('Filtering keys...');
        const filteredKeys = keys.filter(key => {
            console.log('Processing key:', key, 'type:', typeof key);
            if (key === undefined || key === null || typeof key !== 'string') {
                console.warn('Found invalid key:', key);
                return false;
            }
            const result = key !== 'api-reference';
            console.log('Key filter result:', result);
            return result;
        });
        
        console.log('Filtered keys:', filteredKeys);
        return filteredKeys;
    }

    getDocumentationStats() {
        return {
            modules: this.apiDocs.size - 1, // Exclude api-reference
            tutorials: this.tutorials.size,
            examples: this.examples.size,
            userGuides: this.userGuides.size,
            faqCategories: this.faqs.size,
            totalFAQs: Array.from(this.faqs.values()).reduce((total, faq) => total + faq.questions.length, 0),
            lastUpdated: new Date().toISOString()
        };
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        console.log('📝 Documentation settings updated:', newSettings);
    }

    // Missing methods that are called by recreationApp.js
    enableAnalytics() {
        this.settings.enableAnalytics = true;
        this.setupAnalytics();
        console.log('📊 Documentation analytics enabled');
    }

    updateAnalytics(data) {
        if (!this.settings.enableAnalytics || !this.analytics) {
            return;
        }
        
        try {
            // Update analytics data based on the provided data
            if (data.pageView) {
                this.trackPageView(data.pageView);
            }
            
            if (data.search) {
                this.trackSearch(data.search);
            }
            
            if (data.feedback) {
                this.trackFeedback(data.feedback);
            }
            
            if (data.popularContent) {
                const current = this.analytics.popularContent.get(data.popularContent) || 0;
                this.analytics.popularContent.set(data.popularContent, current + 1);
            }
            
            console.log('📊 Analytics updated:', data);
        } catch (error) {
            console.warn('Analytics update failed:', error);
        }
    }

    // Add initialize method that calls init for compatibility with recreationApp.js
    async initialize() {
        try {
            console.log('DocumentationSuite.initialize() called');
            console.log('this in initialize:', this);
            console.log('this.init:', this.init);
            
            // Ensure proper context binding
            const boundInit = this.init.bind(this);
            return await boundInit();
        } catch (error) {
            console.error('Error in DocumentationSuite.initialize():', error);
            console.error('Initialize error stack:', error.stack);
            throw error;
        }
    }

    async getContent(section = 'overview') {
        try {
            if (section === 'overview') {
                const overviewData = this.documentationData.get('overview');
                if (overviewData) {
                    return `
                        <div class="documentation-overview">
                            <h1>${overviewData.title}</h1>
                            <p>${overviewData.description}</p>
                            <div class="sections-list">
                                <h3>Available Sections:</h3>
                                <ul>
                                    ${overviewData.sections.map(section => `<li><a href="#${section}">${section.replace('-', ' ').toUpperCase()}</a></li>`).join('')}
                                </ul>
                            </div>
                            <div class="quick-stats">
                                <h3>Documentation Stats:</h3>
                                <p>Modules: ${this.getAvailableModules().length}</p>
                                <p>Tutorials: ${this.tutorials.size}</p>
                                <p>Examples: ${this.examples.size}</p>
                                <p>Last Updated: ${overviewData.lastUpdated}</p>
                            </div>
                        </div>
                    `;
                }
            }
            
            // Handle other sections
            if (this.apiDocs.has(section)) {
                const moduleDoc = this.apiDocs.get(section);
                return this.generateModuleDocumentation(moduleDoc);
            }
            
            if (this.tutorials.has(section)) {
                const tutorial = this.tutorials.get(section);
                return this.generateTutorialContent(tutorial);
            }
            
            if (this.examples.has(section)) {
                const example = this.examples.get(section);
                return this.generateExampleContent(example);
            }
            
            // Default fallback content
            return `
                <div class="documentation-section">
                    <h2>Documentation Section: ${section}</h2>
                    <p>Content for this section is being loaded...</p>
                </div>
            `;
            
        } catch (error) {
            console.error('Failed to get documentation content:', error);
            return `
                <div class="documentation-error">
                    <h2>Error Loading Content</h2>
                    <p>Failed to load documentation for section: ${section}</p>
                </div>
            `;
        }
    }

    // Event system
    addEventListener(eventType, callback) {
        if (!this.eventListeners) {
            this.eventListeners = new Map();
        }
        
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, []);
        }
        
        this.eventListeners.get(eventType).push(callback);
    }

    dispatchEvent(eventType, data) {
        if (!this.eventListeners || !this.eventListeners.has(eventType)) {
            return;
        }
        
        this.eventListeners.get(eventType).forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`Error in event listener for ${eventType}:`, error);
            }
        });
    }
}

// Export the class
export default DocumentationSuite;