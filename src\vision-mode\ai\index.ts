/**
 * AI Module Index
 * Exports all AI services and provides unified interface for Vision Mode system
 */

export {
  OpenRouterService,
  openRouterService,
  type OpenRouterConfig,
  type AIRequest,
  type AIResponse,
  type AIAnalysisResult
} from './openrouter-service';

export {
  AIServiceManager,
  aiServiceManager,
  type AIServiceConfig,
  type AICapability,
  type AITask,
  type AIResult
} from './ai-service-manager';

// Re-export commonly used types
export type {
  AIRequest,
  AIResponse
} from './openrouter-service';

// Convenience exports for quick access
export const AI = {
  service: aiServiceManager,
  openrouter: openRouterService
};

// Default export for easy importing
export default aiServiceManager;