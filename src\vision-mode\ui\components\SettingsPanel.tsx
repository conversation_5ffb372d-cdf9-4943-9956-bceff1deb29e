/**
 * Settings Panel Component
 * Comprehensive interface for configuring MCP agents and automation parameters
 */

import React, { useState, useEffect } from 'react';
import {
  Settings,
  Save,
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Users,
  Zap,
  Sliders,
  Clock,
  Activity,
  Shield,
  Database,
  Cpu,
  HardDrive
} from 'lucide-react';
// Temporarily commented out to fix compilation issues
// import { VisionModeAgent } from '../../VisionModeAgent';

// Mock type definition
interface VisionModeAgent {
  initialize(): Promise<void>;
  getStatus(): any;
}

/**
 * Agent configuration interface
 */
export interface AgentConfig {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  priority: number;
  timeout: number;
  retryAttempts: number;
  memoryLimit: number;
  customSettings: Record<string, any>;
}

/**
 * Automation configuration interface
 */
export interface AutomationConfig {
  enabled: boolean;
  interval: number;
  maxConcurrentTasks: number;
  errorThreshold: number;
  performanceMonitoring: boolean;
  debugMode: boolean;
  autoRestart: boolean;
  resourceLimits: {
    maxMemory: number;
    maxCpu: number;
    maxDisk: number;
  };
}

/**
 * Settings panel props
 */
export interface SettingsPanelProps {
  visionAgent: VisionModeAgent | null;
  systemStatus: any;
  onStatusUpdate?: (status: any) => void;
  className?: string;
}

/**
 * Settings Panel Component
 */
export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  visionAgent,
  systemStatus,
  onStatusUpdate,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'agents' | 'automation' | 'advanced'>('agents');
  const [agentConfigs, setAgentConfigs] = useState<AgentConfig[]>([]);
  const [automationConfig, setAutomationConfig] = useState<AutomationConfig>({
    enabled: true,
    interval: 1000,
    maxConcurrentTasks: 5,
    errorThreshold: 3,
    performanceMonitoring: true,
    debugMode: false,
    autoRestart: true,
    resourceLimits: {
      maxMemory: 512,
      maxCpu: 80,
      maxDisk: 1024
    }
  });
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Initialize mock agent configurations
  useEffect(() => {
    const mockAgents: AgentConfig[] = [
      {
        id: 'semantic-analyzer',
        name: 'Semantic Analyzer',
        type: 'analysis',
        enabled: true,
        priority: 1,
        timeout: 30000,
        retryAttempts: 3,
        memoryLimit: 256,
        customSettings: {
          confidenceThreshold: 0.8,
          maxTokens: 4096,
          enableCaching: true
        }
      },
      {
        id: 'knowledge-extractor',
        name: 'Knowledge Extractor',
        type: 'extraction',
        enabled: true,
        priority: 2,
        timeout: 45000,
        retryAttempts: 2,
        memoryLimit: 512,
        customSettings: {
          extractionDepth: 3,
          relationshipMapping: true,
          entityRecognition: true
        }
      },
      {
        id: 'behavioral-tracker',
        name: 'Behavioral Tracker',
        type: 'monitoring',
        enabled: false,
        priority: 3,
        timeout: 15000,
        retryAttempts: 5,
        memoryLimit: 128,
        customSettings: {
          trackingInterval: 500,
          privacyMode: true,
          dataRetention: 30
        }
      },
      {
        id: 'automation-controller',
        name: 'Automation Controller',
        type: 'automation',
        enabled: true,
        priority: 1,
        timeout: 60000,
        retryAttempts: 1,
        memoryLimit: 384,
        customSettings: {
          workflowOptimization: true,
          parallelExecution: true,
          errorRecovery: true
        }
      }
    ];

    setAgentConfigs(mockAgents);
  }, []);

  const handleAgentConfigChange = (agentId: string, field: keyof AgentConfig, value: any) => {
    setAgentConfigs(prev => prev.map(config => 
      config.id === agentId ? { ...config, [field]: value } : config
    ));
    setHasUnsavedChanges(true);
  };

  const handleAgentCustomSettingChange = (agentId: string, setting: string, value: any) => {
    setAgentConfigs(prev => prev.map(config => 
      config.id === agentId 
        ? { ...config, customSettings: { ...config.customSettings, [setting]: value } }
        : config
    ));
    setHasUnsavedChanges(true);
  };

  const handleAutomationConfigChange = (field: keyof AutomationConfig, value: any) => {
    setAutomationConfig(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
  };

  const handleResourceLimitChange = (resource: keyof AutomationConfig['resourceLimits'], value: number) => {
    setAutomationConfig(prev => ({
      ...prev,
      resourceLimits: {
        ...prev.resourceLimits,
        [resource]: value
      }
    }));
    setHasUnsavedChanges(true);
  };

  const handleSaveConfiguration = async () => {
    setSaveStatus('saving');
    try {
      // Simulate API call to save configurations
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, this would save to the backend
      console.log('Saving agent configurations:', agentConfigs);
      console.log('Saving automation configuration:', automationConfig);
      
      setSaveStatus('saved');
      setHasUnsavedChanges(false);
      onStatusUpdate?.({ 
        agents: agentConfigs, 
        automation: automationConfig,
        lastSaved: new Date()
      });
      
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      console.error('Failed to save configuration:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  const handleResetToDefaults = () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      // Reset to default configurations
      setAutomationConfig({
        enabled: true,
        interval: 1000,
        maxConcurrentTasks: 5,
        errorThreshold: 3,
        performanceMonitoring: true,
        debugMode: false,
        autoRestart: true,
        resourceLimits: {
          maxMemory: 512,
          maxCpu: 80,
          maxDisk: 1024
        }
      });
      setHasUnsavedChanges(true);
    }
  };

  const getAgentTypeIcon = (type: string) => {
    switch (type) {
      case 'analysis': return <Activity className="w-4 h-4" />;
      case 'extraction': return <Database className="w-4 h-4" />;
      case 'monitoring': return <Shield className="w-4 h-4" />;
      case 'automation': return <Zap className="w-4 h-4" />;
      default: return <Settings className="w-4 h-4" />;
    }
  };

  const getAgentTypeColor = (type: string) => {
    switch (type) {
      case 'analysis': return 'text-blue-500';
      case 'extraction': return 'text-green-500';
      case 'monitoring': return 'text-yellow-500';
      case 'automation': return 'text-purple-500';
      default: return 'text-gray-500';
    }
  };

  const getPriorityLabel = (priority: number) => {
    switch (priority) {
      case 1: return 'High';
      case 2: return 'Medium';
      case 3: return 'Low';
      default: return 'Normal';
    }
  };

  const filteredAgents = agentConfigs.filter(agent => 
    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderAgentSettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-white">Agent Configuration</h3>
          <p className="text-gray-400 text-sm mt-1">
            Configure individual MCP agents and their behavior
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <input
            type="text"
            placeholder="Search agents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
          />
          <span className="text-sm text-gray-400">
            {agentConfigs.filter(a => a.enabled).length} of {agentConfigs.length} enabled
          </span>
        </div>
      </div>
      
      <div className="space-y-4">
        {filteredAgents.map(agent => (
          <div key={agent.id} className="bg-slate-800 rounded-lg border border-slate-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`${getAgentTypeColor(agent.type)}`}>
                  {getAgentTypeIcon(agent.type)}
                </div>
                <div>
                  <h4 className="font-medium text-white">{agent.name}</h4>
                  <div className="flex items-center space-x-2 text-sm text-gray-400">
                    <span className="capitalize">{agent.type}</span>
                    <span>•</span>
                    <span>ID: {agent.id}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className={`w-3 h-3 rounded-full ${
                  agent.enabled ? 'bg-green-500' : 'bg-gray-500'
                }`} />
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={agent.enabled}
                    onChange={(e) => handleAgentConfigChange(agent.id, 'enabled', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Priority
                </label>
                <select
                  value={agent.priority}
                  onChange={(e) => handleAgentConfigChange(agent.id, 'priority', parseInt(e.target.value))}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                >
                  <option value={1}>High (1)</option>
                  <option value={2}>Medium (2)</option>
                  <option value={3}>Low (3)</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Timeout (ms)
                </label>
                <input
                  type="number"
                  value={agent.timeout}
                  onChange={(e) => handleAgentConfigChange(agent.id, 'timeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                  min="1000"
                  max="300000"
                  step="1000"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Retry Attempts
                </label>
                <input
                  type="number"
                  value={agent.retryAttempts}
                  onChange={(e) => handleAgentConfigChange(agent.id, 'retryAttempts', parseInt(e.target.value))}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                  min="0"
                  max="10"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Memory Limit (MB)
                </label>
                <input
                  type="number"
                  value={agent.memoryLimit}
                  onChange={(e) => handleAgentConfigChange(agent.id, 'memoryLimit', parseInt(e.target.value))}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                  min="64"
                  max="2048"
                  step="64"
                />
              </div>
            </div>

            {/* Custom Settings */}
            <div className="border-t border-slate-700 pt-4">
              <h5 className="text-sm font-medium text-gray-300 mb-3">Custom Settings</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(agent.customSettings).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm text-gray-400 mb-1 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                    </label>
                    {typeof value === 'boolean' ? (
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={(e) => handleAgentCustomSettingChange(agent.id, key, e.target.checked)}
                          className="rounded border-slate-600 bg-slate-700 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-300">{value ? 'Enabled' : 'Disabled'}</span>
                      </label>
                    ) : typeof value === 'number' ? (
                      <input
                        type="number"
                        value={value}
                        onChange={(e) => handleAgentCustomSettingChange(agent.id, key, parseFloat(e.target.value))}
                        className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:border-blue-500"
                        step={key.includes('threshold') || key.includes('confidence') ? '0.1' : '1'}
                      />
                    ) : (
                      <input
                        type="text"
                        value={value.toString()}
                        onChange={(e) => handleAgentCustomSettingChange(agent.id, key, e.target.value)}
                        className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:border-blue-500"
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAutomationSettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-white">Automation Configuration</h3>
          <p className="text-gray-400 text-sm mt-1">
            Configure system-wide automation and performance settings
          </p>
        </div>
        <div className={`px-3 py-1 rounded-full text-sm ${
          automationConfig.enabled 
            ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}>
          {automationConfig.enabled ? 'Active' : 'Inactive'}
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Core Settings */}
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
          <h4 className="text-md font-semibold text-white mb-4 flex items-center space-x-2">
            <Zap className="w-5 h-5 text-blue-500" />
            <span>Core Settings</span>
          </h4>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Enable Automation</label>
                <p className="text-xs text-gray-500">Master switch for all automation features</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={automationConfig.enabled}
                  onChange={(e) => handleAutomationConfigChange('enabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Update Interval (ms)
              </label>
              <input
                type="number"
                value={automationConfig.interval}
                onChange={(e) => handleAutomationConfigChange('interval', parseInt(e.target.value))}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                min="100"
                max="10000"
                step="100"
              />
              <p className="text-xs text-gray-500 mt-1">How often the system checks for new tasks</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Max Concurrent Tasks
              </label>
              <input
                type="number"
                value={automationConfig.maxConcurrentTasks}
                onChange={(e) => handleAutomationConfigChange('maxConcurrentTasks', parseInt(e.target.value))}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                min="1"
                max="20"
              />
              <p className="text-xs text-gray-500 mt-1">Maximum number of parallel operations</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Error Threshold
              </label>
              <input
                type="number"
                value={automationConfig.errorThreshold}
                onChange={(e) => handleAutomationConfigChange('errorThreshold', parseInt(e.target.value))}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                min="1"
                max="10"
              />
              <p className="text-xs text-gray-500 mt-1">Number of errors before system intervention</p>
            </div>
          </div>
        </div>

        {/* Monitoring & Debug */}
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
          <h4 className="text-md font-semibold text-white mb-4 flex items-center space-x-2">
            <Activity className="w-5 h-5 text-green-500" />
            <span>Monitoring & Debug</span>
          </h4>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Performance Monitoring</label>
                <p className="text-xs text-gray-500">Track system performance metrics</p>
              </div>
              <input
                type="checkbox"
                checked={automationConfig.performanceMonitoring}
                onChange={(e) => handleAutomationConfigChange('performanceMonitoring', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 bg-slate-700 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Debug Mode</label>
                <p className="text-xs text-gray-500">Enable detailed logging and debugging</p>
              </div>
              <input
                type="checkbox"
                checked={automationConfig.debugMode}
                onChange={(e) => handleAutomationConfigChange('debugMode', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 bg-slate-700 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Auto Restart</label>
                <p className="text-xs text-gray-500">Automatically restart failed agents</p>
              </div>
              <input
                type="checkbox"
                checked={automationConfig.autoRestart}
                onChange={(e) => handleAutomationConfigChange('autoRestart', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 bg-slate-700 rounded"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Resource Limits */}
      <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
        <h4 className="text-md font-semibold text-white mb-4 flex items-center space-x-2">
          <Sliders className="w-5 h-5 text-purple-500" />
          <span>Resource Limits</span>
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
              <HardDrive className="w-4 h-4 text-blue-500" />
              <span>Max Memory (MB)</span>
            </label>
            <input
              type="number"
              value={automationConfig.resourceLimits.maxMemory}
              onChange={(e) => handleResourceLimitChange('maxMemory', parseInt(e.target.value))}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              min="128"
              max="4096"
              step="128"
            />
            <div className="mt-2 bg-slate-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(automationConfig.resourceLimits.maxMemory / 4096) * 100}%` }}
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
              <Cpu className="w-4 h-4 text-green-500" />
              <span>Max CPU (%)</span>
            </label>
            <input
              type="number"
              value={automationConfig.resourceLimits.maxCpu}
              onChange={(e) => handleResourceLimitChange('maxCpu', parseInt(e.target.value))}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              min="10"
              max="100"
              step="5"
            />
            <div className="mt-2 bg-slate-700 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${automationConfig.resourceLimits.maxCpu}%` }}
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
              <HardDrive className="w-4 h-4 text-yellow-500" />
              <span>Max Disk (MB)</span>
            </label>
            <input
              type="number"
              value={automationConfig.resourceLimits.maxDisk}
              onChange={(e) => handleResourceLimitChange('maxDisk', parseInt(e.target.value))}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              min="256"
              max="8192"
              step="256"
            />
            <div className="mt-2 bg-slate-700 rounded-full h-2">
              <div 
                className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(automationConfig.resourceLimits.maxDisk / 8192) * 100}%` }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAdvancedSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white">Advanced Configuration</h3>
        <p className="text-gray-400 text-sm mt-1">
          Advanced system settings and experimental features
        </p>
      </div>
      
      <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-yellow-500 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-yellow-400">Warning</h4>
            <p className="text-sm text-yellow-300 mt-1">
              Advanced settings can significantly impact system performance and stability. 
              Only modify these if you understand the implications.
            </p>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Configuration */}
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
          <h4 className="text-md font-semibold text-white mb-4">System Configuration</h4>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Global Memory Limit (MB)
              </label>
              <input
                type="number"
                defaultValue={2048}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                min="512"
                max="8192"
                step="256"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Cache Size (MB)
              </label>
              <input
                type="number"
                defaultValue={256}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                min="64"
                max="1024"
                step="64"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Log Level
              </label>
              <select className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500">
                <option value="error">Error</option>
                <option value="warn">Warning</option>
                <option value="info">Info</option>
                <option value="debug">Debug</option>
                <option value="trace">Trace</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Connection Timeout (ms)
              </label>
              <input
                type="number"
                defaultValue={30000}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                min="5000"
                max="120000"
                step="5000"
              />
            </div>
          </div>
        </div>

        {/* Experimental Features */}
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
          <h4 className="text-md font-semibold text-white mb-4">Experimental Features</h4>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Predictive Caching</label>
                <p className="text-xs text-gray-500">Use AI to predict and cache likely requests</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 bg-slate-700 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Adaptive Scaling</label>
                <p className="text-xs text-gray-500">Automatically adjust resources based on load</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 bg-slate-700 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Neural Optimization</label>
                <p className="text-xs text-gray-500">Use neural networks for workflow optimization</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 bg-slate-700 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Quantum Processing</label>
                <p className="text-xs text-gray-500">Enable quantum-enhanced computations (experimental)</p>
              </div>
              <input
                type="checkbox"
                disabled
                className="h-4 w-4 text-gray-400 border-gray-600 bg-slate-700 rounded opacity-50"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Vision Mode Settings</h2>
          <p className="text-gray-400 mt-1">Configure agents, automation, and system parameters</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {hasUnsavedChanges && (
            <span className="text-sm text-orange-400 font-medium flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>Unsaved changes</span>
            </span>
          )}
          
          <button
            onClick={handleResetToDefaults}
            className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-400 hover:text-white hover:bg-slate-700 rounded-lg transition-colors"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Reset</span>
          </button>
          
          <button
            onClick={handleSaveConfiguration}
            disabled={saveStatus === 'saving' || !hasUnsavedChanges}
            className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              saveStatus === 'saving' || !hasUnsavedChanges
                ? 'bg-slate-700 text-gray-400 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {saveStatus === 'saving' ? (
              <div className="w-4 h-4 border-2 border-gray-400 border-t-blue-600 rounded-full animate-spin" />
            ) : saveStatus === 'saved' ? (
              <CheckCircle className="w-4 h-4" />
            ) : saveStatus === 'error' ? (
              <AlertCircle className="w-4 h-4" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>
              {saveStatus === 'saving' ? 'Saving...' : 
               saveStatus === 'saved' ? 'Saved' :
               saveStatus === 'error' ? 'Error' : 'Save Changes'}
            </span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-slate-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('agents')}
          className={`flex items-center space-x-2 flex-1 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'agents'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <Users className="w-4 h-4" />
          <span>Agents</span>
          <span className={`px-2 py-1 text-xs rounded-full ${
            activeTab === 'agents'
              ? 'bg-blue-500 text-white'
              : 'bg-slate-600 text-gray-300'
          }`}>
            {agentConfigs.length}
          </span>
        </button>
        <button
          onClick={() => setActiveTab('automation')}
          className={`flex items-center space-x-2 flex-1 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'automation'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <Zap className="w-4 h-4" />
          <span>Automation</span>
        </button>
        <button
          onClick={() => setActiveTab('advanced')}
          className={`flex items-center space-x-2 flex-1 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'advanced'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <Sliders className="w-4 h-4" />
          <span>Advanced</span>
        </button>
      </div>

      {/* Content */}
      <div>
        {activeTab === 'agents' && renderAgentSettings()}
        {activeTab === 'automation' && renderAutomationSettings()}
        {activeTab === 'advanced' && renderAdvancedSettings()}
      </div>
    </div>
  );
};

export default SettingsPanel;