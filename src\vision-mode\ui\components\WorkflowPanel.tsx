/**
 * Workflow Panel Component
 * Interface for designing and managing automation workflows
 */

import React, { useState } from 'react';
import {
  Workflow,
  Play,
  Pause,
  Square,
  Plus,
  Settings,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

export interface WorkflowPanelProps {
  visionAgent?: any;
  systemStatus?: any;
  onStatusUpdate?: (status: any) => void;
}

export const WorkflowPanel: React.FC<WorkflowPanelProps> = ({
  visionAgent,
  systemStatus,
  onStatusUpdate
}) => {
  const [workflows] = useState([
    {
      id: 'doc-analysis',
      name: 'Document Analysis',
      description: 'Automated document processing and analysis',
      status: 'active',
      progress: 75
    },
    {
      id: 'semantic-extraction',
      name: 'Semantic Extraction',
      description: 'Extract semantic meaning and relationships',
      status: 'idle',
      progress: 0
    }
  ]);

  const handleWorkflowAction = (workflowId: string, action: 'start' | 'pause' | 'stop') => {
    console.log(`Workflow ${workflowId}: ${action}`);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="w-4 h-4 text-green-500" />;
      case 'paused': return <Pause className="w-4 h-4 text-yellow-500" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case 'error': return <AlertCircle className="w-4 h-4 text-red-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="p-6 h-full overflow-auto">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center space-x-2">
            <Workflow className="w-6 h-6 text-blue-500" />
            <span>Workflows</span>
          </h2>
          <p className="text-gray-400 mt-1">Design and manage automation workflows</p>
        </div>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
          <Plus className="w-4 h-4" />
          <span>Create Workflow</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {workflows.map((workflow) => (
          <div key={workflow.id} className="bg-slate-800 rounded-lg border border-slate-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                {getStatusIcon(workflow.status)}
                <div>
                  <h3 className="text-lg font-semibold text-white">{workflow.name}</h3>
                  <p className="text-sm text-gray-400">{workflow.description}</p>
                </div>
              </div>
              <button className="p-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded-lg transition-colors">
                <Settings className="w-4 h-4" />
              </button>
            </div>

            {workflow.status === 'active' && (
              <div className="mb-4">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-400">Progress</span>
                  <span className="text-white">{workflow.progress}%</span>
                </div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${workflow.progress}%` }}
                  />
                </div>
              </div>
            )}

            <div className="flex space-x-2">
              <button
                onClick={() => handleWorkflowAction(workflow.id, 'start')}
                className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Start</span>
              </button>
              <button
                onClick={() => handleWorkflowAction(workflow.id, 'pause')}
                className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg transition-colors"
              >
                <Pause className="w-4 h-4" />
                <span>Pause</span>
              </button>
              <button
                onClick={() => handleWorkflowAction(workflow.id, 'stop')}
                className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
              >
                <Square className="w-4 h-4" />
                <span>Stop</span>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WorkflowPanel;