/**
 * Image Recreation App - Main Application
 */
import ImageAnalyzer from './modules/imageAnalysis.js';
import RecreationEngine from './modules/recreationEngine.js';
import BlueprintGenerator from './modules/blueprintGenerator.js';
import InteractiveEditor from './modules/interactiveEditor.js';
import PerformanceOptimizer from './modules/performanceOptimizer.js';
import { errorHandler } from './modules/errorHandler.js';
import { aiEnhancement } from './modules/aiEnhancement.js';
import { templateLibrary } from './modules/templateLibrary.js';
import { advancedExport } from './modules/advancedExport.js';
import { testingSuite } from './modules/testingSuite.js';
import { securityValidator } from './modules/securityValidator.js';
import DocumentationSuite from './modules/documentationSuite.js';
import DeploymentManager from './modules/deploymentManager.js';


class ImageRecreationApp {
    constructor() {
        this.analyzer = new ImageAnalyzer();
        this.engine = new RecreationEngine();
        this.blueprintGenerator = new BlueprintGenerator();
        this.interactiveEditor = null;
        this.performanceOptimizer = null;
        this.errorHandler = errorHandler;
        this.aiEnhancement = aiEnhancement;
        this.templateLibrary = templateLibrary;
        this.advancedExport = advancedExport;
        this.testingSuite = testingSuite;
        this.securityValidator = securityValidator;
        this.documentationSuite = new DocumentationSuite();
        this.deploymentManager = new DeploymentManager();

        this.currentBlueprint = null;
        this.currentStep = 1;
        this.analysisData = null;
        this.recreationData = null;
        
        this.initializeApp();
    }

    async initializeApp() {
        try {
            // Initialize error handler first
            await this.initializeErrorHandler();
            
            // Initialize security validator
            await this.initializeSecurityValidator();
            
            // Initialize AI enhancement
            await this.initializeAI();
            
            // Initialize template library
            await this.initializeTemplateLibrary();
            
            // Initialize advanced export
            await this.initializeAdvancedExport();
            
            // Initialize testing suite
            await this.initializeTestingSuite();
            
            // Initialize documentation suite
            await this.initializeDocumentationSuite();
            
            // Initialize deployment manager
            await this.initializeDeploymentManager();
            
            // Initialize performance optimizer
            await this.initializePerformanceOptimizer();
            
            // Setup UI and workflows
            this.initializeUI();
            this.setupWorkflow();
            this.setupPerformanceMonitoring();
            this.setupAIFeatures();
            this.setupTemplateFeatures();
            this.setupExportFeatures();
            this.setupTestingFeatures();
            this.setupSecurityFeatures();
            this.setupDocumentationFeatures();
            this.setupDeploymentFeatures();
            
            console.log('✅ Image Recreation App initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize app:', error);
            this.handleCriticalError(error);
        }
    }

    async initializeErrorHandler() {
        try {
            await this.errorHandler.initialize();
            
            // Make error handler globally available
            window.errorHandler = this.errorHandler;
            
            // Setup error event listeners
            this.setupErrorHandling();
            
            console.log('✅ Error handling system initialized');
        } catch (error) {
            console.error('❌ Failed to initialize error handler:', error);
        }
    }

    async initializeAI() {
        try {
            await this.aiEnhancement.initialize();
            
            // Make AI enhancement globally available
            window.aiEnhancement = this.aiEnhancement;
            
            console.log('✅ AI enhancement system initialized');
        } catch (error) {
            console.warn('⚠️ AI enhancement failed to initialize:', error);
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.WORKER,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: 'AI enhancement initialization failed',
                context: 'ai_initialization',
                recoverable: false
            });
        }
    }

    setupErrorHandling() {
        // Setup error recovery mechanisms - with safe checking
        if (this.errorHandler && typeof this.errorHandler.addEventListener === 'function') {
            this.errorHandler.addEventListener('network-retry', (data) => {
                console.log('🔄 Retrying network operation...');
                this.retryNetworkOperation(data.errorId);
            });

            this.errorHandler.addEventListener('memory-cleanup', (data) => {
                console.log('🧹 Performing memory cleanup...');
                this.performMemoryCleanup();
            });

            this.errorHandler.addEventListener('worker-restart', (data) => {
                console.log('🔄 Restarting workers...');
                this.restartWorkers();
            });

            this.errorHandler.addEventListener('render-reset', (data) => {
                console.log('🔄 Resetting render system...');
                this.resetRenderSystem();
            });
        } else {
            console.warn('⚠️ Error handler event listeners not available - addEventListener method not found');
        }

        // Wrap critical operations with error handling (with safe binding)
        if (this.analyzer && typeof this.analyzer.analyze === 'function') {
            this.safeAnalyze = this.errorHandler.wrapAsync(
                this.analyzer.analyze.bind(this.analyzer),
                'image_analysis'
            );
        }

        if (this.engine && typeof this.engine.recreateFromAnalysis === 'function') {
            this.safeRecreate = this.errorHandler.wrapAsync(
                this.engine.recreateFromAnalysis.bind(this.engine),
                'recreation'
            );
        }

        if (this.blueprintGenerator && typeof this.blueprintGenerator.generateBlueprint === 'function') {
            this.safeGenerateBlueprint = this.errorHandler.wrapAsync(
                this.blueprintGenerator.generateBlueprint.bind(this.blueprintGenerator),
                'blueprint_generation'
            );
        }
    }

    async initializeTemplateLibrary() {
        try {
            await this.templateLibrary.initialize();
            
            // Make template library globally available
            window.templateLibrary = this.templateLibrary;
            
            console.log('✅ Template library system initialized');
        } catch (error) {
            console.warn('⚠️ Template library failed to initialize:', error);
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.SYSTEM,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: 'Template library initialization failed',
                context: 'template_initialization',
                recoverable: false
            });
        }
    }

    async initializeAdvancedExport() {
        try {
            await this.advancedExport.initialize();
            
            // Make advanced export globally available
            window.advancedExport = this.advancedExport;
            
            console.log('✅ Advanced export system initialized');
        } catch (error) {
            console.warn('⚠️ Advanced export failed to initialize:', error);
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.SYSTEM,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: 'Advanced export initialization failed',
                context: 'export_initialization',
                recoverable: false
            });
        }
    }

    async initializeTestingSuite() {
        try {
            await this.testingSuite.initialize();
            
            // Make testing suite globally available
            window.testingSuite = this.testingSuite;
            
            console.log('✅ Testing suite system initialized');
        } catch (error) {
            console.warn('⚠️ Testing suite failed to initialize:', error);
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.SYSTEM,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: 'Testing suite initialization failed',
                context: 'testing_initialization',
                recoverable: false
            });
        }
    }

    async initializeSecurityValidator() {
        try {
            await this.securityValidator.initialize();
            
            // Make security validator globally available
            window.securityValidator = this.securityValidator;
            
            console.log('✅ Security validation system initialized');
        } catch (error) {
            console.warn('⚠️ Security validator failed to initialize:', error);
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.SYSTEM,
                severity: this.errorHandler.severityLevels.HIGH,
                message: 'Security validator initialization failed',
                context: 'security_initialization',
                recoverable: false
            });
        }
    }

    async initializeDocumentationSuite() {
        try {
            await this.documentationSuite.initialize();
            
            // Make documentation suite globally available
            window.documentationSuite = this.documentationSuite;
            
            console.log('✅ Documentation suite system initialized');
        } catch (error) {
            console.warn('⚠️ Documentation suite failed to initialize:', error);
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.SYSTEM,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: 'Documentation suite initialization failed',
                context: 'documentation_initialization',
                recoverable: false
            });
        }
    }

    async initializeDeploymentManager() {
        try {
            await this.deploymentManager.init();
            
            // Make deployment manager globally available
            window.deploymentManager = this.deploymentManager;
            
            console.log('✅ Deployment manager system initialized');
        } catch (error) {
            console.warn('⚠️ Deployment manager failed to initialize:', error);
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.SYSTEM,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: 'Deployment manager initialization failed',
                context: 'deployment_initialization',
                recoverable: false
            });
        }
    }

    setupDocumentationFeatures() {
        if (!this.documentationSuite.isInitialized) {
            console.warn('⚠️ Documentation features not available - Documentation suite not initialized');
            return;
        }

        // Add documentation controls to the UI
        this.addDocumentationControlsToUI();
        
        // Setup documentation event listeners
        this.setupDocumentationEventListeners();
        
        // Enable documentation features
        this.enableDocumentationFeatures();
    }

    addDocumentationControlsToUI() {
        // Documentation button is already in the navigation
        // Just ensure the panel is properly initialized
        const documentationPanel = document.getElementById('documentationPanel');
        if (documentationPanel) {
            // Initialize the documentation content
            this.loadDocumentationContent();
        }
    }

    createDocumentationPanel() {
        const documentationPanel = document.createElement('div');
        documentationPanel.className = 'documentation-panel-modal';
        documentationPanel.id = 'documentationPanelModal';
        documentationPanel.innerHTML = `
            <div class="documentation-panel-content">
                <div class="documentation-panel-header">
                    <h3>📚 Documentation Center</h3>
                    <button class="documentation-close" id="closeDocumentationPanel">×</button>
                </div>
                
                <div class="documentation-panel-body">
                    <div class="documentation-tabs">
                        <button class="documentation-tab active" data-tab="overview">Overview</button>
                        <button class="documentation-tab" data-tab="api">API Reference</button>
                        <button class="documentation-tab" data-tab="guides">User Guides</button>
                        <button class="documentation-tab" data-tab="tutorials">Tutorials</button>
                        <button class="documentation-tab" data-tab="examples">Examples</button>
                        <button class="documentation-tab" data-tab="settings">Settings</button>
                    </div>
                    
                    <div class="documentation-content">
                        <div class="documentation-tab-content active" id="overviewTab">
                            <div class="documentation-overview">
                                <div class="documentation-summary" id="documentationSummary">
                                    <div class="summary-card">
                                        <h4>Documentation Status</h4>
                                        <div class="summary-stats">
                                            <div class="stat">
                                                <span class="stat-value" id="totalPages">0</span>
                                                <span class="stat-label">Total Pages</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value" id="apiEndpoints">0</span>
                                                <span class="stat-label">API Endpoints</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value" id="codeExamples">0</span>
                                                <span class="stat-label">Code Examples</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value" id="lastUpdated">Never</span>
                                                <span class="stat-label">Last Updated</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="documentation-actions">
                                    <button class="documentation-action-btn primary" id="generateFullDocumentation">
                                        📚 Generate Full Documentation
                                    </button>
                                    <button class="documentation-action-btn" id="updateDocumentation">
                                        🔄 Update Documentation
                                    </button>
                                    <button class="documentation-action-btn" id="validateDocumentation">
                                        ✅ Validate Docs
                                    </button>
                                    <button class="documentation-action-btn" id="publishDocumentation">
                                        🚀 Publish
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="documentation-tab-content" id="apiTab">
                            <div class="documentation-category">
                                <h4>API Reference</h4>
                                <div class="api-documentation" id="apiDocumentation"></div>
                            </div>
                        </div>
                        
                        <div class="documentation-tab-content" id="guidesTab">
                            <div class="documentation-category">
                                <h4>User Guides</h4>
                                <div class="guides-list" id="guidesList"></div>
                            </div>
                        </div>
                        
                        <div class="documentation-tab-content" id="tutorialsTab">
                            <div class="documentation-category">
                                <h4>Tutorials</h4>
                                <div class="tutorials-list" id="tutorialsList"></div>
                            </div>
                        </div>
                        
                        <div class="documentation-tab-content" id="examplesTab">
                            <div class="documentation-category">
                                <h4>Code Examples</h4>
                                <div class="examples-list" id="examplesList"></div>
                            </div>
                        </div>
                        
                        <div class="documentation-tab-content" id="settingsTab">
                            <div class="documentation-category">
                                <h4>Documentation Settings</h4>
                                <div class="documentation-settings" id="documentationSettings">
                                    <div class="setting-group">
                                        <label class="setting-label">Auto-generate on changes:</label>
                                        <input type="checkbox" id="autoGenerate" checked>
                                    </div>
                                    <div class="setting-group">
                                        <label class="setting-label">Include code examples:</label>
                                        <input type="checkbox" id="includeExamples" checked>
                                    </div>
                                    <div class="setting-group">
                                        <label class="setting-label">Generate API docs:</label>
                                        <input type="checkbox" id="generateAPI" checked>
                                    </div>
                                    <div class="setting-group">
                                        <label class="setting-label">Include screenshots:</label>
                                        <input type="checkbox" id="includeScreenshots" checked>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(documentationPanel);
    }

    setupDocumentationEventListeners() {
        // Open documentation panel
        document.getElementById('openDocumentation')?.addEventListener('click', () => {
            this.openDocumentationPanel();
        });

        // Close documentation panel
        document.getElementById('closeDocumentation')?.addEventListener('click', () => {
            this.closeDocumentationPanel();
        });

        // Navigation items
        document.querySelectorAll('.documentation-nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.handleDocumentationNavigation(e.target.dataset.section);
            });
        });

        // Search functionality
        document.getElementById('documentationSearch')?.addEventListener('input', (e) => {
            this.handleDocumentationSearch(e.target.value);
        });

        // Export buttons
        document.getElementById('exportDocHTML')?.addEventListener('click', () => {
            this.exportDocumentation('html');
        });

        document.getElementById('exportDocMarkdown')?.addEventListener('click', () => {
            this.exportDocumentation('markdown');
        });

        document.getElementById('exportDocPDF')?.addEventListener('click', () => {
            this.exportDocumentation('pdf');
        });

        // Documentation suite events
        this.documentationSuite.addEventListener('content-loaded', (event) => {
            this.handleDocumentationContentLoaded(event.detail);
        });

        this.documentationSuite.addEventListener('search-results', (event) => {
            this.handleDocumentationSearchResults(event.detail);
        });

        this.documentationSuite.addEventListener('export-completed', (event) => {
            this.handleDocumentationExportCompleted(event.detail);
        });
    }

    enableDocumentationFeatures() {
        // Enable documentation analytics
        this.documentationSuite.enableAnalytics();
        
        // Setup documentation tracking
        this.setupDocumentationTracking();
        
        // Load initial documentation content
        this.loadInitialDocumentationContent();
    }

    openDocumentationPanel() {
        const panel = document.getElementById('documentationPanel');
        if (panel) {
            panel.classList.add('active');
            this.documentationSuite.trackPageView('overview');
            this.loadDocumentationContent('overview');
        }
    }

    closeDocumentationPanel() {
        const panel = document.getElementById('documentationPanel');
        if (panel) {
            panel.classList.remove('active');
        }
    }

    handleDocumentationNavigation(section) {
        // Update active navigation item
        document.querySelectorAll('.documentation-nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`)?.classList.add('active');

        // Load section content
        this.loadDocumentationContent(section);
        
        // Track navigation
        this.documentationSuite.trackPageView(section);
    }

    async loadDocumentationContent(section = 'overview') {
        try {
            const content = await this.documentationSuite.getContent(section);
            const contentContainer = document.getElementById('documentationContent');
            
            if (contentContainer && content) {
                contentContainer.innerHTML = content;
                
                // Setup interactive elements in the content
                this.setupDocumentationInteractivity(contentContainer);
            }
        } catch (error) {
            console.error('Failed to load documentation content:', error);
            this.showDocumentationError('Failed to load content');
        }
    }

    async handleDocumentationSearch(query) {
        if (!query.trim()) {
            this.hideDocumentationSearchResults();
            return;
        }

        try {
            const results = await this.documentationSuite.search(query);
            this.showDocumentationSearchResults(results);
            this.documentationSuite.trackSearch(query, results.length);
        } catch (error) {
            console.error('Documentation search failed:', error);
        }
    }

    showDocumentationSearchResults(results) {
        const resultsContainer = document.getElementById('documentationSearchResults');
        if (!resultsContainer) return;

        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="documentation-search-result">No results found</div>';
        } else {
            resultsContainer.innerHTML = results.map(result => `
                <div class="documentation-search-result" data-section="${result.section}">
                    <div class="documentation-search-result-title">${result.title}</div>
                    <div class="documentation-search-result-snippet">${result.snippet}</div>
                </div>
            `).join('');

            // Add click handlers to search results
            resultsContainer.querySelectorAll('.documentation-search-result').forEach(item => {
                item.addEventListener('click', () => {
                    const section = item.dataset.section;
                    this.handleDocumentationNavigation(section);
                    this.hideDocumentationSearchResults();
                });
            });
        }

        resultsContainer.classList.add('active');
    }

    hideDocumentationSearchResults() {
        const resultsContainer = document.getElementById('documentationSearchResults');
        if (resultsContainer) {
            resultsContainer.classList.remove('active');
        }
    }

    async generateDocumentation() {
        try {
            this.updateDocumentationStatus('generating', 'Generating documentation...');
            const result = await this.documentationSuite.generateDocumentation();
            this.updateDocumentationResults(result);
            this.updateDocumentationStatus('completed', 'Documentation generated');
        } catch (error) {
            this.updateDocumentationStatus('error', 'Documentation generation failed');
            console.error('Failed to generate documentation:', error);
        }
    }

    async generateAPIDocumentation() {
        try {
            this.updateDocumentationStatus('generating', 'Generating API documentation...');
            const result = await this.documentationSuite.generateAPIDocumentation();
            this.updateAPIDocumentation(result);
            this.updateDocumentationStatus('completed', 'API documentation generated');
        } catch (error) {
            this.updateDocumentationStatus('error', 'API documentation generation failed');
            console.error('Failed to generate API documentation:', error);
        }
    }

    async generateUserGuide() {
        try {
            this.updateDocumentationStatus('generating', 'Generating user guide...');
            const result = await this.documentationSuite.generateUserGuide();
            this.updateUserGuides(result);
            this.updateDocumentationStatus('completed', 'User guide generated');
        } catch (error) {
            this.updateDocumentationStatus('error', 'User guide generation failed');
            console.error('Failed to generate user guide:', error);
        }
    }

    async generateTutorial() {
        try {
            this.updateDocumentationStatus('generating', 'Generating tutorial...');
            const result = await this.documentationSuite.generateTutorial();
            this.updateTutorials(result);
            this.updateDocumentationStatus('completed', 'Tutorial generated');
        } catch (error) {
            this.updateDocumentationStatus('error', 'Tutorial generation failed');
            console.error('Failed to generate tutorial:', error);
        }
    }

    async generateFullDocumentation() {
        try {
            this.updateDocumentationStatus('generating', 'Generating full documentation...');
            const result = await this.documentationSuite.generateFullDocumentation();
            this.updateDocumentationResults(result);
            this.updateDocumentationStatus('completed', 'Full documentation generated');
        } catch (error) {
            this.updateDocumentationStatus('error', 'Full documentation generation failed');
            console.error('Failed to generate full documentation:', error);
        }
    }

    async updateDocumentation() {
        try {
            this.updateDocumentationStatus('updating', 'Updating documentation...');
            const result = await this.documentationSuite.updateDocumentation();
            this.updateDocumentationResults(result);
            this.updateDocumentationStatus('completed', 'Documentation updated');
        } catch (error) {
            this.updateDocumentationStatus('error', 'Documentation update failed');
            console.error('Failed to update documentation:', error);
        }
    }

    async validateDocumentation() {
        try {
            this.updateDocumentationStatus('validating', 'Validating documentation...');
            const result = await this.documentationSuite.validateDocumentation();
            this.displayValidationResults(result);
            this.updateDocumentationStatus('completed', 'Documentation validated');
        } catch (error) {
            this.updateDocumentationStatus('error', 'Documentation validation failed');
            console.error('Failed to validate documentation:', error);
        }
    }

    async publishDocumentation() {
        try {
            this.updateDocumentationStatus('publishing', 'Publishing documentation...');
            const result = await this.documentationSuite.publishDocumentation();
            this.handleDocumentationPublished(result);
            this.updateDocumentationStatus('completed', 'Documentation published');
        } catch (error) {
            this.updateDocumentationStatus('error', 'Documentation publishing failed');
            console.error('Failed to publish documentation:', error);
        }
    }

    async exportDocumentation(format) {
        try {
            const options = {
                format: format,
                includeAPI: true,
                includeTutorials: true,
                includeExamples: true,
                includeUserGuides: true,
                includeFAQ: true
            };

            const result = await this.documentationSuite.exportDocumentation(options);
            
            if (result.success) {
                // Create download link
                const blob = new Blob([result.content], { 
                    type: this.getContentType(format) 
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `documentation.${format}`;
                a.click();
                URL.revokeObjectURL(url);

                this.showDocumentationSuccess(`Documentation exported as ${format.toUpperCase()}`);
            } else {
                this.showDocumentationError('Export failed');
            }
        } catch (error) {
            console.error('Documentation export failed:', error);
            this.showDocumentationError('Export failed');
        }
    }

    getContentType(format) {
        const types = {
            'html': 'text/html',
            'markdown': 'text/markdown',
            'pdf': 'application/pdf',
            'json': 'application/json'
        };
        return types[format] || 'text/plain';
    }

    setupDocumentationInteractivity(container) {
        // Setup code block copy buttons
        container.querySelectorAll('.documentation-code-block').forEach(block => {
            const copyBtn = document.createElement('button');
            copyBtn.className = 'code-copy-btn';
            copyBtn.textContent = '📋';
            copyBtn.title = 'Copy code';
            copyBtn.addEventListener('click', () => {
                const code = block.querySelector('pre').textContent;
                navigator.clipboard.writeText(code).then(() => {
                    copyBtn.textContent = '✅';
                    setTimeout(() => copyBtn.textContent = '📋', 2000);
                });
            });
            block.appendChild(copyBtn);
        });

        // Setup FAQ toggles
        container.querySelectorAll('.documentation-faq-question').forEach(question => {
            question.addEventListener('click', () => {
                question.classList.toggle('active');
                const answer = question.nextElementSibling;
                if (answer) {
                    answer.classList.toggle('active');
                }
            });
        });

        // Setup tutorial step navigation
        container.querySelectorAll('.documentation-tutorial-step').forEach((step, index) => {
            step.addEventListener('click', () => {
                this.documentationSuite.trackTutorialStep(index + 1);
            });
        });
    }

    setupDocumentationTracking() {
        // Track documentation usage patterns
        setInterval(() => {
            this.documentationSuite.updateAnalytics();
        }, 60000); // Every minute
    }

    async loadInitialDocumentationContent() {
        try {
            // Pre-load commonly accessed content
            await this.documentationSuite.preloadContent(['overview', 'quick-start', 'api-overview']);
            
            // Load documentation statistics
            const stats = await this.documentationSuite.getStatistics();
            this.updateDocumentationStats(stats);
        } catch (error) {
            console.warn('Failed to load initial documentation content:', error);
        }
    }

    updateDocumentationStats(stats) {
        // Update any documentation statistics displays
        console.log('Documentation statistics:', stats);
    }

    handleDocumentationContentLoaded(data) {
        console.log('Documentation content loaded:', data.section);
    }

    handleDocumentationSearchResults(data) {
        console.log('Documentation search completed:', data.query, data.results.length);
    }

    handleDocumentationExportCompleted(data) {
        console.log('Documentation export completed:', data.format);
        this.showDocumentationSuccess(`Documentation exported successfully as ${data.format.toUpperCase()}`);
    }

    showDocumentationSuccess(message) {
        // Show success message (you can implement a toast notification system)
        console.log('✅ Documentation:', message);
    }

    showDocumentationError(message) {
        // Show error message (you can implement a toast notification system)
        console.error('❌ Documentation:', message);
    }

    updateDocumentationStatus(status, message) {
        const indicator = document.getElementById('documentationIndicator');
        const statusText = indicator?.querySelector('.status-text');
        const statusDot = indicator?.querySelector('.status-dot');

        if (statusText) statusText.textContent = message;
        if (statusDot) {
            statusDot.className = `status-dot ${status}`;
        }
    }

    updateDocumentationResults(results) {
        // Update summary stats
        document.getElementById('totalPages').textContent = results.totalPages || 0;
        document.getElementById('apiEndpoints').textContent = results.apiEndpoints || 0;
        document.getElementById('codeExamples').textContent = results.codeExamples || 0;
        document.getElementById('lastUpdated').textContent = new Date().toLocaleString();

        // Update detailed results in each tab
        this.updateDocumentationTabs(results);
    }

    updateDocumentationTabs(results) {
        // Update API documentation
        this.updateAPIDocumentation(results.api || {});
        
        // Update user guides
        this.updateUserGuides(results.guides || []);
        
        // Update tutorials
        this.updateTutorials(results.tutorials || []);
        
        // Update examples
        this.updateExamples(results.examples || []);
    }

    updateAPIDocumentation(apiData) {
        const apiContainer = document.getElementById('apiDocumentation');
        if (!apiContainer) return;

        apiContainer.innerHTML = `
            <div class="api-overview">
                <h5>API Overview</h5>
                <p>Total Endpoints: ${apiData.endpoints?.length || 0}</p>
                <p>Authentication: ${apiData.authentication || 'None'}</p>
                <p>Base URL: ${apiData.baseUrl || 'N/A'}</p>
            </div>
            <div class="api-endpoints">
                <h5>Endpoints</h5>
                ${(apiData.endpoints || []).map(endpoint => `
                    <div class="api-endpoint">
                        <div class="endpoint-header">
                            <span class="method ${endpoint.method.toLowerCase()}">${endpoint.method}</span>
                            <span class="path">${endpoint.path}</span>
                        </div>
                        <div class="endpoint-description">${endpoint.description}</div>
                        <div class="endpoint-parameters">
                            <h6>Parameters:</h6>
                            ${(endpoint.parameters || []).map(param => `
                                <div class="parameter">
                                    <span class="param-name">${param.name}</span>
                                    <span class="param-type">${param.type}</span>
                                    <span class="param-description">${param.description}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    updateUserGuides(guides) {
        const guidesContainer = document.getElementById('guidesList');
        if (!guidesContainer) return;

        guidesContainer.innerHTML = guides.map(guide => `
            <div class="guide-item">
                <div class="guide-header">
                    <h5 class="guide-title">${guide.title}</h5>
                    <span class="guide-status ${guide.status}">${guide.status}</span>
                </div>
                <div class="guide-description">${guide.description}</div>
                <div class="guide-actions">
                    <button class="guide-btn" onclick="window.open('${guide.url}', '_blank')">View Guide</button>
                    <button class="guide-btn" onclick="this.downloadGuide('${guide.id}')">Download</button>
                </div>
            </div>
        `).join('');
    }

    updateTutorials(tutorials) {
        const tutorialsContainer = document.getElementById('tutorialsList');
        if (!tutorialsContainer) return;

        tutorialsContainer.innerHTML = tutorials.map(tutorial => `
            <div class="tutorial-item">
                <div class="tutorial-header">
                    <h5 class="tutorial-title">${tutorial.title}</h5>
                    <span class="tutorial-difficulty ${tutorial.difficulty}">${tutorial.difficulty}</span>
                </div>
                <div class="tutorial-description">${tutorial.description}</div>
                <div class="tutorial-meta">
                    <span class="tutorial-duration">Duration: ${tutorial.duration}</span>
                    <span class="tutorial-steps">Steps: ${tutorial.steps}</span>
                </div>
                <div class="tutorial-actions">
                    <button class="tutorial-btn" onclick="window.open('${tutorial.url}', '_blank')">Start Tutorial</button>
                    <button class="tutorial-btn" onclick="this.downloadTutorial('${tutorial.id}')">Download</button>
                </div>
            </div>
        `).join('');
    }

    updateExamples(examples) {
        const examplesContainer = document.getElementById('examplesList');
        if (!examplesContainer) return;

        examplesContainer.innerHTML = examples.map(example => `
            <div class="example-item">
                <div class="example-header">
                    <h5 class="example-title">${example.title}</h5>
                    <span class="example-language ${example.language}">${example.language}</span>
                </div>
                <div class="example-description">${example.description}</div>
                <div class="example-code">
                    <pre><code class="language-${example.language}">${example.code}</code></pre>
                </div>
                <div class="example-actions">
                    <button class="example-btn" onclick="this.copyCode('${example.id}')">Copy Code</button>
                    <button class="example-btn" onclick="this.runExample('${example.id}')">Run Example</button>
                </div>
            </div>
        `).join('');
    }

    displayValidationResults(results) {
        const validationResults = `
            <div class="validation-results">
                <h5>Validation Results</h5>
                <div class="validation-summary">
                    <div class="validation-stat">
                        <span class="stat-value ${results.errors === 0 ? 'good' : 'error'}">${results.errors}</span>
                        <span class="stat-label">Errors</span>
                    </div>
                    <div class="validation-stat">
                        <span class="stat-value ${results.warnings === 0 ? 'good' : 'warning'}">${results.warnings}</span>
                        <span class="stat-label">Warnings</span>
                    </div>
                    <div class="validation-stat">
                        <span class="stat-value good">${results.passed}</span>
                        <span class="stat-label">Passed</span>
                    </div>
                </div>
                <div class="validation-details">
                    ${(results.issues || []).map(issue => `
                        <div class="validation-issue ${issue.type}">
                            <span class="issue-type">${issue.type}</span>
                            <span class="issue-message">${issue.message}</span>
                            <span class="issue-location">${issue.location}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        // Show validation results in a modal or panel
        this.showValidationModal(validationResults);
    }

    showValidationModal(content) {
        const modal = document.createElement('div');
        modal.className = 'validation-modal';
        modal.innerHTML = `
            <div class="validation-modal-content">
                <div class="validation-modal-header">
                    <h3>Documentation Validation Results</h3>
                    <button class="validation-close">×</button>
                </div>
                <div class="validation-modal-body">
                    ${content}
                </div>
            </div>
        `;

        modal.querySelector('.validation-close').addEventListener('click', () => {
            modal.remove();
        });

        document.body.appendChild(modal);
    }

    switchDocumentationTab(tabName) {
        // Remove active class from all tabs and contents
        document.querySelectorAll('.documentation-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.documentation-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Add active class to selected tab and content
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}Tab`).classList.add('active');
    }

    async loadDocumentationData() {
        try {
            const documentationData = await this.documentationSuite.getDocumentationData();
            if (documentationData) {
                this.updateDocumentationResults(documentationData);
            }
        } catch (error) {
            console.error('Failed to load documentation data:', error);
        }
    }

    setupDocumentationMonitoring() {
        // Setup periodic documentation updates
        setInterval(() => {
            this.performPeriodicDocumentationCheck();
        }, 300000); // Every 5 minutes

        // Setup real-time documentation monitoring
        this.documentationSuite.enableRealTimeMonitoring();
    }

    async performPeriodicDocumentationCheck() {
        try {
            const needsUpdate = await this.documentationSuite.checkForUpdates();
            if (needsUpdate) {
                this.updateDocumentationStatus('outdated', 'Documentation needs update');
            }
        } catch (error) {
            console.warn('Periodic documentation check failed:', error);
        }
    }

    downloadDocumentation(documentation) {
        const blob = new Blob([documentation], { type: 'application/zip' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `documentation-${Date.now()}.zip`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    downloadGuide(guideId) {
        this.documentationSuite.downloadGuide(guideId);
    }

    downloadTutorial(tutorialId) {
        this.documentationSuite.downloadTutorial(tutorialId);
    }

    copyCode(exampleId) {
        this.documentationSuite.copyExampleCode(exampleId);
        this.showNotification('Code copied to clipboard', 'success');
    }

    runExample(exampleId) {
        this.documentationSuite.runExample(exampleId);
    }

    handleDocumentationGenerated(documentationInfo) {
        console.log('Documentation generated:', documentationInfo);
        this.updateDocumentationResults(documentationInfo.results);
        this.showNotification(`Documentation generated: ${documentationInfo.type}`, 'success');
    }

    handleDocumentationUpdated(documentationInfo) {
        console.log('Documentation updated:', documentationInfo);
        this.updateDocumentationResults(documentationInfo.results);
        this.showNotification('Documentation updated successfully', 'success');
    }

    handleDocumentationValidated(validationInfo) {
        console.log('Documentation validated:', validationInfo);
        this.displayValidationResults(validationInfo.results);
        
        if (validationInfo.results.errors === 0) {
            this.showNotification('Documentation validation passed', 'success');
        } else {
            this.showNotification(`Documentation validation failed: ${validationInfo.results.errors} errors`, 'warning');
        }
    }

    handleDocumentationPublished(publishInfo) {
        console.log('Documentation published:', publishInfo);
        this.showNotification(`Documentation published to: ${publishInfo.url}`, 'success');
    }

    setupDeploymentFeatures() {
        console.log('🚀 Setting up deployment features...');
        
        try {
            // Add deployment controls to UI
            this.addDeploymentControls();
            
            // Setup deployment event listeners
            this.setupDeploymentEventListeners();
            
            // Enable deployment monitoring
            this.enableDeploymentMonitoring();
            
            console.log('✅ Deployment features configured');
        } catch (error) {
            console.error('❌ Failed to setup deployment features:', error);
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.UI,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: 'Deployment features setup failed',
                context: 'deployment_setup',
                recoverable: true
            });
        }
    }

    addDeploymentControls() {
        // Add deployment button to main navigation
        const nav = document.querySelector('.main-nav');
        if (nav) {
            const deployBtn = document.createElement('button');
            deployBtn.className = 'nav-btn deployment-btn';
            deployBtn.innerHTML = '🚀 Deploy';
            deployBtn.onclick = () => this.openDeploymentPanel();
            nav.appendChild(deployBtn);
        }

        // Add deployment status indicator
        const statusBar = document.querySelector('.status-bar');
        if (statusBar) {
            const deployStatus = document.createElement('div');
            deployStatus.className = 'deploy-status';
            deployStatus.innerHTML = `
                <span class="status-indicator"></span>
                <span class="status-text">Ready to Deploy</span>
            `;
            statusBar.appendChild(deployStatus);
        }
    }

    setupDeploymentEventListeners() {
        // Listen for deployment events
        document.addEventListener('click', (e) => {
            if (e.target.matches('.deploy-btn')) {
                this.handleDeployment(e.target.dataset.target);
            }
            
            if (e.target.matches('.build-btn')) {
                this.handleBuild();
            }
            
            // Handle data-action buttons
            if (e.target.dataset.action) {
                this.handleDeploymentAction(e.target.dataset.action);
            }
        });

        // Listen for deployment manager events
        if (this.deploymentManager) {
            this.deploymentManager.addEventListener?.('deployment-complete', (data) => {
                this.updateDeploymentStatus('deployed', data);
                this.showDeploymentSuccess(data);
            });

            this.deploymentManager.addEventListener?.('deployment-failed', (data) => {
                this.updateDeploymentStatus('failed', data);
                this.showDeploymentError(data);
            });

            this.deploymentManager.addEventListener?.('build-complete', (data) => {
                this.updateBuildStatus('complete', data);
                this.showBuildSuccess(data);
            });
        }
    }

    handleDeploymentAction(action) {
        switch (action) {
            case 'optimize':
                if (this.deploymentManager && this.deploymentManager.optimizeAssets) {
                    this.deploymentManager.optimizeAssets();
                } else {
                    this.showNotification('Asset optimization not available', 'warning');
                }
                break;
            case 'test':
                if (this.deploymentManager && this.deploymentManager.runTests) {
                    this.deploymentManager.runTests();
                } else {
                    this.showNotification('Testing not available', 'warning');
                }
                break;
            case 'analytics':
                this.showAnalytics();
                break;
            case 'performance':
                this.showPerformanceMetrics();
                break;
            case 'errors':
                this.showErrorLogs();
                break;
            case 'check-updates':
                if (this.deploymentManager && this.deploymentManager.checkForUpdates) {
                    this.deploymentManager.checkForUpdates();
                } else {
                    this.showNotification('Update checking not available', 'warning');
                }
                break;
            default:
                console.warn('Unknown deployment action:', action);
        }
    }

    enableDeploymentMonitoring() {
        // Monitor deployment status
        const deploymentMonitor = setInterval(() => {
            try {
                if (this.deploymentManager && typeof this.updateDeploymentUI === 'function') {
                    const status = this.deploymentManager.getStatus();
                    if (status) {
                        this.updateDeploymentUI.call(this, status);
                    }
                }
            } catch (error) {
                console.error('Error in deployment monitoring:', error);
            }
        }, 5000); // Check every 5 seconds

        // Monitor performance metrics
        if (this.deploymentManager) {
            this.deploymentManager.trackEvent('app_initialized', {
                timestamp: Date.now(),
                version: this.deploymentManager.config?.version
            });
        }
    }

    async openDeploymentPanel() {
        console.log('🚀 Opening deployment panel...');
        
        try {
            // Create deployment panel if it doesn't exist
            let panel = document.querySelector('.deployment-panel');
            if (!panel) {
                panel = this.createDeploymentPanel();
                document.body.appendChild(panel);
            }
            
            // Show panel
            panel.classList.add('active');
            
            // Update deployment status
            await this.updateDeploymentPanelData(panel);
            
        } catch (error) {
            console.error('❌ Failed to open deployment panel:', error);
            this.showError('Failed to open deployment panel');
        }
    }

    createDeploymentPanel() {
        const panel = document.createElement('div');
        panel.className = 'deployment-panel';
        panel.innerHTML = `
            <div class="deployment-content">
                <div class="deployment-header">
                    <h3>🚀 Deployment & Distribution</h3>
                    <button class="close-btn" onclick="this.closest('.deployment-panel').classList.remove('active')">×</button>
                </div>
                
                <div class="deployment-body">
                    <div class="deployment-section">
                        <h4>📊 Current Status</h4>
                        <div class="status-grid">
                            <div class="status-item">
                                <span class="label">Version:</span>
                                <span class="value version-value">-</span>
                            </div>
                            <div class="status-item">
                                <span class="label">Build:</span>
                                <span class="value build-value">-</span>
                            </div>
                            <div class="status-item">
                                <span class="label">Environment:</span>
                                <span class="value env-value">-</span>
                            </div>
                            <div class="status-item">
                                <span class="label">CDN:</span>
                                <span class="value cdn-value">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="deployment-section">
                        <h4>🏗️ Build Actions</h4>
                        <div class="action-buttons">
                            <button class="action-btn build-btn">
                                📦 Create Build
                            </button>
                            <button class="action-btn optimize-btn" data-action="optimize">
                                ⚡ Optimize Assets
                            </button>
                            <button class="action-btn test-btn" data-action="test">
                                🧪 Run Tests
                            </button>
                        </div>
                    </div>
                    
                    <div class="deployment-section">
                        <h4>🎯 Deployment Targets</h4>
                        <div class="target-buttons">
                            <button class="deploy-btn static-deploy" data-target="static">
                                📁 Static Hosting
                            </button>
                            <button class="deploy-btn docker-deploy" data-target="docker">
                                🐳 Docker Container
                            </button>
                            <button class="deploy-btn cdn-deploy" data-target="cdn">
                                🌐 CDN Distribution
                            </button>
                        </div>
                    </div>
                    
                    <div class="deployment-section">
                        <h4>📈 Analytics & Monitoring</h4>
                        <div class="analytics-buttons">
                            <button class="analytics-btn" data-action="analytics">
                                📊 View Analytics
                            </button>
                            <button class="performance-btn" data-action="performance">
                                ⚡ Performance Metrics
                            </button>
                            <button class="errors-btn" data-action="errors">
                                🚨 Error Logs
                            </button>
                        </div>
                    </div>
                    
                    <div class="deployment-section">
                        <h4>🔄 Update System</h4>
                        <div class="update-info">
                            <div class="update-status">
                                <span class="update-indicator"></span>
                                <span class="update-text">Checking for updates...</span>
                            </div>
                            <button class="update-btn" data-action="check-updates">
                                🔍 Check Updates
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return panel;
    }

    async updateDeploymentPanelData(panel) {
        if (!this.deploymentManager) return;
        
        try {
            const status = this.deploymentManager.getStatus();
            
            // Update status values
            panel.querySelector('.version-value').textContent = status.version || '-';
            panel.querySelector('.build-value').textContent = status.buildNumber || '-';
            panel.querySelector('.env-value').textContent = status.environment || '-';
            panel.querySelector('.cdn-value').textContent = status.cdn ? 'Enabled' : 'Disabled';
            
            // Update update status
            const updateText = panel.querySelector('.update-text');
            const updateIndicator = panel.querySelector('.update-indicator');
            
            if (status.updateAvailable) {
                updateText.textContent = 'Update available';
                updateIndicator.className = 'update-indicator available';
            } else {
                updateText.textContent = 'Up to date';
                updateIndicator.className = 'update-indicator current';
            }
            
        } catch (error) {
            console.error('❌ Failed to update deployment panel data:', error);
        }
    }

    async handleBuild() {
        console.log('🏗️ Starting build process...');
        
        try {
            this.updateBuildStatus('building');
            
            if (this.deploymentManager) {
                const buildResult = await this.deploymentManager.createProductionBuild();
                
                this.updateBuildStatus('complete', buildResult);
                this.showBuildSuccess(buildResult);
                
                // Track build event
                this.deploymentManager.trackEvent('build_complete', {
                    size: buildResult.size,
                    time: buildResult.time,
                    assets: buildResult.assets.size
                });
            }
            
        } catch (error) {
            console.error('❌ Build failed:', error);
            this.updateBuildStatus('failed', error);
            this.showBuildError(error);
        }
    }

    async handleDeployment(target) {
        console.log(`🚀 Starting deployment to ${target}...`);
        
        try {
            this.updateDeploymentStatus('deploying', { target });
            
            if (this.deploymentManager) {
                const deployResult = await this.deploymentManager.deploy([target]);
                
                this.updateDeploymentStatus('deployed', deployResult);
                this.showDeploymentSuccess(deployResult);
                
                // Track deployment event
                this.deploymentManager.trackEvent('deployment_complete', {
                    target: target,
                    success: deployResult.success,
                    time: deployResult.deployments[0]?.time
                });
            }
            
        } catch (error) {
            console.error('❌ Deployment failed:', error);
            this.updateDeploymentStatus('failed', { target, error });
            this.showDeploymentError(error);
        }
    }

    updateBuildStatus(status, data = {}) {
        const statusElements = document.querySelectorAll('.build-status');
        statusElements.forEach(element => {
            element.className = `build-status ${status}`;
            
            switch (status) {
                case 'building':
                    element.textContent = '🏗️ Building...';
                    break;
                case 'complete':
                    element.textContent = `✅ Build Complete (${this.formatBytes(data.size || 0)})`;
                    break;
                case 'failed':
                    element.textContent = '❌ Build Failed';
                    break;
                default:
                    element.textContent = '📦 Ready to Build';
            }
        });
    }

    updateDeploymentStatus(status, data = {}) {
        const statusElements = document.querySelectorAll('.deploy-status');
        statusElements.forEach(element => {
            const indicator = element.querySelector('.status-indicator');
            const text = element.querySelector('.status-text');
            
            indicator.className = `status-indicator ${status}`;
            
            switch (status) {
                case 'deploying':
                    text.textContent = `🚀 Deploying to ${data.target}...`;
                    break;
                case 'deployed':
                    text.textContent = `✅ Deployed Successfully`;
                    break;
                case 'failed':
                    text.textContent = `❌ Deployment Failed`;
                    break;
                default:
                    text.textContent = 'Ready to Deploy';
            }
        });
    }

    updateDeploymentUI(status) {
        // Update deployment UI elements based on status
        const deploymentPanel = document.querySelector('.deployment-panel');
        if (!deploymentPanel) return;
        
        const statusIndicator = deploymentPanel.querySelector('.deployment-status');
        const progressBar = deploymentPanel.querySelector('.progress-bar');
        const statusText = deploymentPanel.querySelector('.status-text');
        
        if (statusIndicator) {
            statusIndicator.className = `deployment-status ${status}`;
        }
        
        if (statusText) {
            switch (status) {
                case 'idle':
                    statusText.textContent = 'Ready for deployment';
                    break;
                case 'building':
                    statusText.textContent = 'Building application...';
                    break;
                case 'deploying':
                    statusText.textContent = 'Deploying to server...';
                    break;
                case 'success':
                    statusText.textContent = 'Deployment successful!';
                    break;
                case 'error':
                    statusText.textContent = 'Deployment failed';
                    break;
                default:
                    statusText.textContent = 'Unknown status';
            }
        }
        
        if (progressBar) {
            switch (status) {
                case 'building':
                    progressBar.style.width = '30%';
                    break;
                case 'deploying':
                    progressBar.style.width = '70%';
                    break;
                case 'success':
                    progressBar.style.width = '100%';
                    break;
                case 'error':
                    progressBar.style.width = '0%';
                    break;
                default:
                    progressBar.style.width = '0%';
            }
        }
    }

    showBuildSuccess(buildResult) {
        this.showNotification({
            type: 'success',
            title: '🏗️ Build Successful',
            message: `Build completed in ${buildResult.time?.toFixed(2)}ms. Total size: ${this.formatBytes(buildResult.size || 0)}`,
            duration: 5000
        });
    }

    showBuildError(error) {
        this.showNotification({
            type: 'error',
            title: '❌ Build Failed',
            message: error.message || 'Unknown build error',
            duration: 8000
        });
    }

    showDeploymentSuccess(deployResult) {
        const deployment = deployResult.deployments?.[0];
        this.showNotification({
            type: 'success',
            title: '🚀 Deployment Successful',
            message: `Deployed to ${deployment?.target} in ${deployment?.time?.toFixed(2)}ms`,
            duration: 5000,
            actions: deployment?.url ? [{
                text: 'Open',
                action: () => window.open(deployment.url, '_blank')
            }] : []
        });
    }

    showDeploymentError(error) {
        this.showNotification({
            type: 'error',
            title: '❌ Deployment Failed',
            message: error.message || 'Unknown deployment error',
            duration: 8000
        });
    }

    showAnalytics() {
        if (!this.deploymentManager) return;
        
        const analytics = this.deploymentManager.getAnalytics();
        
        // Create analytics modal
        const modal = document.createElement('div');
        modal.className = 'analytics-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>📊 Analytics Dashboard</h3>
                    <button class="close-btn" onclick="this.closest('.analytics-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="analytics-section">
                        <h4>📈 Events</h4>
                        <div class="analytics-data">
                            ${this.formatAnalyticsData(analytics.events)}
                        </div>
                    </div>
                    <div class="analytics-section">
                        <h4>⚡ Performance</h4>
                        <div class="analytics-data">
                            ${this.formatAnalyticsData(analytics.performance)}
                        </div>
                    </div>
                    <div class="analytics-section">
                        <h4>🚨 Errors</h4>
                        <div class="analytics-data">
                            ${this.formatAnalyticsData(analytics.errors)}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    formatAnalyticsData(data) {
        if (!data || typeof data !== 'object') return '<p>No data available</p>';
        
        return Object.entries(data).map(([key, value]) => {
            const count = Array.isArray(value) ? value.length : 0;
            return `<div class="analytics-item">
                <span class="key">${key}:</span>
                <span class="value">${count} events</span>
            </div>`;
        }).join('');
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    setupTemplateFeatures() {
        if (!this.templateLibrary.isInitialized) {
            console.warn('⚠️ Template features not available - Template library not initialized');
            return;
        }

        // Add template controls to the UI
        this.addTemplateControlsToUI();
        
        // Setup template event listeners
        this.setupTemplateEventListeners();
        
        // Enable template-based recreation
        this.enableTemplateRecreation();
    }

    addTemplateControlsToUI() {
        // Find the blueprint section to add template controls
        const blueprintSection = document.getElementById('blueprintSection');
        if (!blueprintSection) return;

        // Create template controls container
        const templateControls = document.createElement('div');
        templateControls.className = 'template-controls';
        templateControls.innerHTML = `
            <h4>Template Library</h4>
            <div class="template-actions">
                <button class="template-btn primary" id="browseTemplates">
                    📚 Browse Templates
                </button>
                <button class="template-btn" id="saveAsTemplate">
                    💾 Save as Template
                </button>
                <button class="template-btn" id="applyTemplate">
                    🎨 Apply Template
                </button>
            </div>
            <div class="template-quick-access" id="templateQuickAccess">
                <h5>Quick Access</h5>
                <div class="template-favorites" id="templateFavorites"></div>
                <div class="template-recent" id="templateRecent"></div>
            </div>
        `;

        // Insert template controls after blueprint options
        const blueprintOptions = blueprintSection.querySelector('.blueprint-options');
        if (blueprintOptions) {
            blueprintOptions.parentNode.insertBefore(templateControls, blueprintOptions.nextSibling);
        }

        // Load quick access templates
        this.loadQuickAccessTemplates();
    }

    setupTemplateEventListeners() {
        // Browse Templates
        document.getElementById('browseTemplates')?.addEventListener('click', () => {
            this.openTemplateLibrary();
        });

        // Save as Template
        document.getElementById('saveAsTemplate')?.addEventListener('click', () => {
            this.saveCurrentAsTemplate();
        });

        // Apply Template
        document.getElementById('applyTemplate')?.addEventListener('click', () => {
            this.showTemplateSelector();
        });

        // Template library events – safe check
        if (this.templateLibrary && typeof this.templateLibrary.addEventListener === 'function') {
            this.templateLibrary.addEventListener('template-selected', (event) => {
                this.handleTemplateSelection(event.detail);
            });

            this.templateLibrary.addEventListener('template-applied', (event) => {
                this.handleTemplateApplication(event.detail);
            });

            this.templateLibrary.addEventListener('template-saved', (event) => {
                this.handleTemplateSaved(event.detail);
            });
        } else {
            console.warn('⚠️ Template library event listeners not available – addEventListener method not found');
        }
    }

    async loadQuickAccessTemplates() {
        try {
            // Load favorite templates
            const favorites = await this.templateLibrary.getFavorites();
            this.displayFavoriteTemplates(favorites);

            // Load recent templates
            const recent = await this.templateLibrary.getRecentTemplates();
            this.displayRecentTemplates(recent);
        } catch (error) {
            console.warn('Failed to load quick access templates:', error);
        }
    }

    displayFavoriteTemplates(favorites) {
        const favoritesContainer = document.getElementById('templateFavorites');
        if (!favoritesContainer) return;

        if (favorites.length === 0) {
            favoritesContainer.innerHTML = '<p class="template-empty">No favorite templates</p>';
            return;
        }

        favoritesContainer.innerHTML = favorites.slice(0, 3).map(template => `
            <div class="template-quick-item" data-template-id="${template.id}">
                <div class="template-quick-thumbnail">
                    ${template.thumbnail ? 
                        `<img src="${template.thumbnail}" alt="${template.name}">` : 
                        '<div class="template-placeholder">📄</div>'
                    }
                </div>
                <div class="template-quick-info">
                    <span class="template-quick-name">${template.name}</span>
                    <span class="template-quick-category">${template.category}</span>
                </div>
            </div>
        `).join('');

        // Add click listeners
        favoritesContainer.querySelectorAll('.template-quick-item').forEach(item => {
            item.addEventListener('click', () => {
                const templateId = item.dataset.templateId;
                this.applyTemplateById(templateId);
            });
        });
    }

    displayRecentTemplates(recent) {
        const recentContainer = document.getElementById('templateRecent');
        if (!recentContainer) return;

        if (recent.length === 0) {
            recentContainer.innerHTML = '<p class="template-empty">No recent templates</p>';
            return;
        }

        recentContainer.innerHTML = recent.slice(0, 3).map(template => `
            <div class="template-quick-item" data-template-id="${template.id}">
                <div class="template-quick-thumbnail">
                    ${template.thumbnail ? 
                        `<img src="${template.thumbnail}" alt="${template.name}">` : 
                        '<div class="template-placeholder">📄</div>'
                    }
                </div>
                <div class="template-quick-info">
                    <span class="template-quick-name">${template.name}</span>
                    <span class="template-quick-category">${template.category}</span>
                </div>
            </div>
        `).join('');

        // Add click listeners
        recentContainer.querySelectorAll('.template-quick-item').forEach(item => {
            item.addEventListener('click', () => {
                const templateId = item.dataset.templateId;
                this.applyTemplateById(templateId);
            });
        });
    }

    openTemplateLibrary() {
        this.templateLibrary.showLibrary();
    }

    async saveCurrentAsTemplate() {
        if (!this.currentBlueprint) {
            this.errorHandler.showNotification('No blueprint available to save as template', 'warning');
            return;
        }

        try {
            // Create template data from current blueprint
            const templateData = {
                name: `Template ${Date.now()}`,
                description: 'Generated from current recreation',
                category: 'user',
                blueprint: this.currentBlueprint,
                analysisData: this.analysisData,
                metadata: {
                    elements: this.analysisData?.elements?.length || 0,
                    colors: this.analysisData?.colors?.length || 0,
                    complexity: this.calculateComplexity(),
                    createdAt: new Date().toISOString()
                }
            };

            // Show save dialog
            const result = await this.templateLibrary.showSaveDialog(templateData);
            
            if (result.success) {
                this.errorHandler.showNotification('Template saved successfully!', 'success');
                this.loadQuickAccessTemplates(); // Refresh quick access
            }
        } catch (error) {
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.SYSTEM,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: 'Failed to save template',
                context: 'template_save',
                originalError: error
            });
        }
    }

    showTemplateSelector() {
        this.templateLibrary.showQuickSelector();
    }

    async applyTemplateById(templateId) {
        try {
            const template = await this.templateLibrary.getTemplate(templateId);
            if (template) {
                await this.applyTemplate(template);
            }
        } catch (error) {
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.SYSTEM,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: 'Failed to apply template',
                context: 'template_apply',
                originalError: error
            });
        }
    }

    async applyTemplate(template) {
        try {
            // Show loading
            this.showLoading('Applying template...');

            // Apply template blueprint
            if (template.blueprint) {
                this.currentBlueprint = template.blueprint;
                await this.engine.recreateFromAnalysis(template.blueprint);
                this.updateBlueprintDisplay();
            }

            // Apply template analysis data if available
            if (template.analysisData) {
                this.analysisData = template.analysisData;
                this.updateAnalysisDisplay();
            }

            // Update UI to reflect template application
            this.updateStepProgress(3); // Move to recreation step
            
            // Add to recent templates
            await this.templateLibrary.addToRecent(template.id);
            
            this.hideLoading();
            this.errorHandler.showNotification(`Template "${template.name}" applied successfully!`, 'success');
            
        } catch (error) {
            this.hideLoading();
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.RECREATION,
                severity: this.errorHandler.severityLevels.HIGH,
                message: 'Failed to apply template',
                context: 'template_application',
                originalError: error
            });
        }
    }

    handleTemplateSelection(template) {
        console.log('Template selected:', template.name);
        this.applyTemplate(template);
    }

    handleTemplateApplication(template) {
        console.log('Template applied:', template.name);
        this.loadQuickAccessTemplates(); // Refresh quick access
    }

    handleTemplateSaved(template) {
        console.log('Template saved:', template.name);
        this.loadQuickAccessTemplates(); // Refresh quick access
    }

    calculateComplexity() {
        if (!this.analysisData) return 'low';
        
        const elementCount = this.analysisData.elements?.length || 0;
        const colorCount = this.analysisData.colors?.length || 0;
        const textCount = this.analysisData.textRegions?.length || 0;
        
        const totalComplexity = elementCount + colorCount + textCount;
        
        if (totalComplexity > 50) return 'high';
        if (totalComplexity > 20) return 'medium';
        return 'low';
    }

    enableTemplateRecreation() {
        // Add template-based recreation options to the recreation engine
        if (this.engine && this.engine.addTemplateSupport) {
            this.engine.addTemplateSupport(this.templateLibrary);
        }
    }

    setupExportFeatures() {
        if (!this.advancedExport.isInitialized) {
            console.warn('⚠️ Export features not available - Advanced export not initialized');
            return;
        }

        // Add export controls to the UI
        this.addExportControlsToUI();
        
        // Setup export event listeners
        this.setupExportEventListeners();
        
        // Enable advanced export options
        this.enableAdvancedExport();
    }

    addExportControlsToUI() {
        // Find the blueprint section to add export controls
        const blueprintSection = document.getElementById('blueprintSection');
        if (!blueprintSection) return;

        // Create export controls container
        const exportControls = document.createElement('div');
        exportControls.className = 'export-controls';
        exportControls.innerHTML = `
            <h4>Advanced Export</h4>
            <div class="export-actions">
                <button class="export-btn primary" id="openAdvancedExport">
                    🚀 Advanced Export
                </button>
                <button class="export-btn" id="quickExportReact">
                    ⚛️ Quick React
                </button>
                <button class="export-btn" id="quickExportVue">
                    🟢 Quick Vue
                </button>
                <button class="export-btn" id="quickExportFigma">
                    🎨 Export to Figma
                </button>
            </div>
            <div class="export-quick-formats" id="exportQuickFormats">
                <h5>Quick Export</h5>
                <div class="format-buttons" id="formatButtons"></div>
            </div>
        `;

        // Insert export controls after template controls or blueprint options
        const templateControls = blueprintSection.querySelector('.template-controls');
        const blueprintOptions = blueprintSection.querySelector('.blueprint-options');
        const insertAfter = templateControls || blueprintOptions;
        
        if (insertAfter) {
            insertAfter.parentNode.insertBefore(exportControls, insertAfter.nextSibling);
        }

        // Load quick format buttons
        this.loadQuickFormatButtons();
    }

    setupExportEventListeners() {
        // Advanced Export Panel
        document.getElementById('openAdvancedExport')?.addEventListener('click', () => {
            this.openAdvancedExportPanel();
        });

        // Quick Export Buttons
        document.getElementById('quickExportReact')?.addEventListener('click', () => {
            this.quickExport('react');
        });

        document.getElementById('quickExportVue')?.addEventListener('click', () => {
            this.quickExport('vue');
        });

        document.getElementById('quickExportFigma')?.addEventListener('click', () => {
            this.quickExport('figma');
        });

        // Advanced export events
        this.advancedExport.addEventListener?.('export-completed', (event) => {
            this.handleExportCompleted(event.detail);
        });

        this.advancedExport.addEventListener?.('export-progress', (event) => {
            this.handleExportProgress(event.detail);
        });

        this.advancedExport.addEventListener?.('export-error', (event) => {
            this.handleExportError(event.detail);
        });
    }

    loadQuickFormatButtons() {
        const formatButtons = document.getElementById('formatButtons');
        if (!formatButtons) return;

        const quickFormats = [
            { id: 'html', name: 'HTML', icon: '🌐' },
            { id: 'tailwind', name: 'Tailwind', icon: '🌊' },
            { id: 'angular', name: 'Angular', icon: '🔺' },
            { id: 'svelte', name: 'Svelte', icon: '🧡' }
        ];

        quickFormats.forEach(format => {
            const button = document.createElement('button');
            button.className = 'format-btn';
            button.innerHTML = `${format.icon} ${format.name}`;
            button.addEventListener('click', () => this.quickExport(format.id));
            formatButtons.appendChild(button);
        });
    }

    async openAdvancedExportPanel() {
        if (!this.recreationData) {
            this.showNotification('Please complete recreation first', 'warning');
            return;
        }

        try {
            // Create export panel modal
            const exportPanel = this.createExportPanel();
            document.body.appendChild(exportPanel);
            
            // Load available formats
            await this.loadExportFormats();
            
            // Show the panel
            exportPanel.style.display = 'flex';
            
        } catch (error) {
            console.error('Failed to open advanced export panel:', error);
            this.showNotification('Failed to open export panel', 'error');
        }
    }

    createExportPanel() {
        const panel = document.createElement('div');
        panel.className = 'modal export-modal';
        panel.id = 'exportModal';
        panel.innerHTML = `
            <div class="export-panel">
                <div class="export-header">
                    <h3>🚀 Advanced Export Options</h3>
                    <button class="export-close" id="closeExportPanel">&times;</button>
                </div>
                <div class="export-content">
                    <div class="export-tabs">
                        <button class="export-tab active" data-tab="frameworks">Frameworks</button>
                        <button class="export-tab" data-tab="design-tools">Design Tools</button>
                        <button class="export-tab" data-tab="css-frameworks">CSS Frameworks</button>
                        <button class="export-tab" data-tab="custom">Custom</button>
                    </div>
                    
                    <div class="export-tab-content active" id="frameworks-tab">
                        <div class="export-formats-grid" id="frameworksGrid"></div>
                        <div class="export-options" id="frameworkOptions"></div>
                        <div class="export-preview" id="frameworkPreview"></div>
                    </div>
                    
                    <div class="export-tab-content" id="design-tools-tab">
                        <div class="export-formats-grid" id="designToolsGrid"></div>
                        <div class="export-options" id="designToolOptions"></div>
                        <div class="export-preview" id="designToolPreview"></div>
                    </div>
                    
                    <div class="export-tab-content" id="css-frameworks-tab">
                        <div class="export-formats-grid" id="cssFrameworksGrid"></div>
                        <div class="export-options" id="cssFrameworkOptions"></div>
                        <div class="export-preview" id="cssFrameworkPreview"></div>
                    </div>
                    
                    <div class="export-tab-content" id="custom-tab">
                        <div class="export-custom-options">
                            <h4>Custom Export Configuration</h4>
                            <div class="export-option-group">
                                <label class="export-option-label">Template Engine:</label>
                                <select class="export-option-select" id="customTemplate">
                                    <option value="handlebars">Handlebars</option>
                                    <option value="mustache">Mustache</option>
                                    <option value="ejs">EJS</option>
                                    <option value="pug">Pug</option>
                                </select>
                            </div>
                            <div class="export-option-group">
                                <label class="export-option-label">Output Format:</label>
                                <select class="export-option-select" id="customFormat">
                                    <option value="zip">ZIP Archive</option>
                                    <option value="tar">TAR Archive</option>
                                    <option value="folder">Folder Structure</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="export-actions">
                    <div class="export-actions-left">
                        <div class="export-progress" id="exportProgress">
                            <span>Exporting...</span>
                            <div class="export-progress-bar">
                                <div class="export-progress-fill" id="exportProgressFill"></div>
                            </div>
                        </div>
                    </div>
                    <div class="export-actions-right">
                        <button class="export-btn" id="previewExport">👁️ Preview</button>
                        <button class="export-btn primary" id="startExport">🚀 Export</button>
                        <button class="export-btn" id="cancelExport">Cancel</button>
                    </div>
                </div>
            </div>
        `;

        // Setup panel event listeners
        this.setupExportPanelEvents(panel);
        
        return panel;
    }

    setupExportPanelEvents(panel) {
        // Close panel
        panel.querySelector('#closeExportPanel').addEventListener('click', () => {
            this.closeExportPanel();
        });

        // Tab switching
        panel.querySelectorAll('.export-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                this.switchExportTab(tab.dataset.tab);
            });
        });

        // Export actions
        panel.querySelector('#previewExport').addEventListener('click', () => {
            this.previewExport();
        });

        panel.querySelector('#startExport').addEventListener('click', () => {
            this.startAdvancedExport();
        });

        panel.querySelector('#cancelExport').addEventListener('click', () => {
            this.closeExportPanel();
        });

        // Click outside to close
        panel.addEventListener('click', (e) => {
            if (e.target === panel) {
                this.closeExportPanel();
            }
        });
    }

    async loadExportFormats() {
        try {
            const formats = this.advancedExport.getAvailableFormats();
            
            // Group formats by category
            const frameworkFormats = formats.filter(f => f.category === 'framework');
            const designToolFormats = formats.filter(f => f.category === 'design-tool');
            const cssFrameworkFormats = formats.filter(f => f.category === 'css-framework');

            // Load each category
            this.loadFormatGrid('frameworksGrid', frameworkFormats);
            this.loadFormatGrid('designToolsGrid', designToolFormats);
            this.loadFormatGrid('cssFrameworksGrid', cssFrameworkFormats);
            
        } catch (error) {
            console.error('Failed to load export formats:', error);
        }
    }

    loadFormatGrid(gridId, formats) {
        const grid = document.getElementById(gridId);
        if (!grid) return;

        grid.innerHTML = '';
        
        formats.forEach(format => {
            const card = document.createElement('div');
            card.className = 'export-format-card';
            card.dataset.format = format.id;
            card.innerHTML = `
                <span class="export-format-icon">${format.icon}</span>
                <h4 class="export-format-name">${format.name}</h4>
                <p class="export-format-description">${format.description}</p>
                <ul class="export-format-features">
                    <li>Component-based architecture</li>
                    <li>TypeScript support</li>
                    <li>Modern tooling</li>
                    <li>Production ready</li>
                </ul>
            `;

            card.addEventListener('click', () => {
                this.selectExportFormat(format.id, card);
            });

            grid.appendChild(card);
        });
    }

    selectExportFormat(formatId, cardElement) {
        // Remove previous selection
        document.querySelectorAll('.export-format-card.selected').forEach(card => {
            card.classList.remove('selected');
        });

        // Select current format
        cardElement.classList.add('selected');
        this.selectedExportFormat = formatId;

        // Load format-specific options
        this.loadFormatOptions(formatId);
    }

    async loadFormatOptions(formatId) {
        // This would load specific options for each format
        // For now, we'll show generic options
        const optionsContainer = document.querySelector('.export-options');
        if (!optionsContainer) return;

        optionsContainer.innerHTML = `
            <h4>Export Options</h4>
            <div class="export-option-group">
                <label class="export-option-label">Component Name:</label>
                <input type="text" class="export-option-input" id="componentName" value="RecreatedComponent" />
            </div>
            <div class="export-option-group">
                <div class="export-option-checkbox">
                    <input type="checkbox" id="includeStyles" checked />
                    <label for="includeStyles">Include Styles</label>
                </div>
                <div class="export-option-checkbox">
                    <input type="checkbox" id="includeTests" />
                    <label for="includeTests">Include Tests</label>
                </div>
                <div class="export-option-checkbox">
                    <input type="checkbox" id="includeStorybook" />
                    <label for="includeStorybook">Include Storybook</label>
                </div>
                <div class="export-option-checkbox">
                    <input type="checkbox" id="includeDocumentation" checked />
                    <label for="includeDocumentation">Include Documentation</label>
                </div>
            </div>
        `;
    }

    async quickExport(format) {
        if (!this.recreationData) {
            this.showNotification('Please complete recreation first', 'warning');
            return;
        }

        try {
            this.showNotification(`Exporting to ${format}...`, 'info');
            
            const options = {
                componentName: 'QuickExport',
                includeStyles: true,
                includeTests: false,
                includeDocumentation: true
            };

            const result = await this.advancedExport.exportComponent(
                this.recreationData, 
                format, 
                options
            );

            if (result.success) {
                this.downloadExportFiles(result.files, `${format}-export`);
                this.showNotification(`Successfully exported to ${format}!`, 'success');
            } else {
                throw new Error(result.error || 'Export failed');
            }
            
        } catch (error) {
            console.error(`Quick export to ${format} failed:`, error);
            this.showNotification(`Export to ${format} failed`, 'error');
        }
    }

    async startAdvancedExport() {
        if (!this.selectedExportFormat) {
            this.showNotification('Please select an export format', 'warning');
            return;
        }

        if (!this.recreationData) {
            this.showNotification('No recreation data available', 'error');
            return;
        }

        try {
            // Show progress
            this.showExportProgress(true);
            
            // Get export options
            const options = this.getExportOptions();
            
            // Start export
            const result = await this.advancedExport.exportComponent(
                this.recreationData,
                this.selectedExportFormat,
                options
            );

            if (result.success) {
                // Download files
                this.downloadExportFiles(result.files, `${this.selectedExportFormat}-export`);
                
                // Show success
                this.showNotification('Export completed successfully!', 'success');
                this.showExportProgress(false);
                
                // Close panel after delay
                setTimeout(() => {
                    this.closeExportPanel();
                }, 2000);
                
            } else {
                throw new Error(result.error || 'Export failed');
            }
            
        } catch (error) {
            console.error('Advanced export failed:', error);
            this.showNotification('Export failed', 'error');
            this.showExportProgress(false);
        }
    }

    getExportOptions() {
        return {
            componentName: document.getElementById('componentName')?.value || 'RecreatedComponent',
            includeStyles: document.getElementById('includeStyles')?.checked || true,
            includeTests: document.getElementById('includeTests')?.checked || false,
            includeStorybook: document.getElementById('includeStorybook')?.checked || false,
            includeDocumentation: document.getElementById('includeDocumentation')?.checked || true
        };
    }

    downloadExportFiles(files, baseName) {
        // Create a ZIP file with all the exported files
        const zip = new JSZip();
        
        Object.entries(files).forEach(([filename, content]) => {
            zip.file(filename, content);
        });

        zip.generateAsync({ type: 'blob' }).then(blob => {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${baseName}.zip`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
    }

    showExportProgress(show) {
        const progress = document.getElementById('exportProgress');
        if (progress) {
            progress.classList.toggle('active', show);
        }
    }

    switchExportTab(tabName) {
        // Remove active class from all tabs and contents
        document.querySelectorAll('.export-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.export-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Add active class to selected tab and content
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    closeExportPanel() {
        const panel = document.getElementById('exportModal');
        if (panel) {
            panel.remove();
        }
        this.selectedExportFormat = null;
    }

    handleExportCompleted(data) {
        console.log('Export completed:', data);
        this.showNotification('Export completed successfully!', 'success');
    }

    handleExportProgress(data) {
        console.log('Export progress:', data);
        const progressFill = document.getElementById('exportProgressFill');
        if (progressFill) {
            progressFill.style.width = `${data.progress}%`;
        }
    }

    handleExportError(data) {
        console.error('Export error:', data);
        this.showNotification(`Export failed: ${data.message}`, 'error');
        this.showExportProgress(false);
    }

    enableAdvancedExport() {
        // Enable advanced export features
        if (this.advancedExport.isInitialized) {
            console.log('✅ Advanced export features enabled');
        }
    }

    setupTestingFeatures() {
        if (!this.testingSuite.isInitialized) {
            console.warn('⚠️ Testing features not available - Testing suite not initialized');
            return;
        }

        // Add testing controls to the UI
        this.addTestingControlsToUI();
        
        // Setup testing event listeners
        this.setupTestingEventListeners();
        
        // Enable automated testing
        this.enableAutomatedTesting();
    }

    addTestingControlsToUI() {
        // Find the main controls section to add testing controls
        const controlsSection = document.querySelector('.controls-section') || document.querySelector('.main-controls');
        if (!controlsSection) return;

        // Create testing controls container
        const testingControls = document.createElement('div');
        testingControls.className = 'testing-controls';
        testingControls.innerHTML = `
            <div class="testing-header">
                <h4>🧪 Testing Suite</h4>
                <button class="testing-toggle" id="toggleTestingPanel">
                    <span class="testing-icon">🔬</span>
                    <span class="testing-text">Open Testing</span>
                </button>
            </div>
            
            <div class="testing-quick-actions" id="testingQuickActions">
                <button class="testing-btn primary" id="runAllTests">
                    ▶️ Run All Tests
                </button>
                <button class="testing-btn" id="runUnitTests">
                    🔧 Unit Tests
                </button>
                <button class="testing-btn" id="runIntegrationTests">
                    🔗 Integration
                </button>
                <button class="testing-btn" id="runPerformanceTests">
                    ⚡ Performance
                </button>
                <button class="testing-btn" id="generateTestReport">
                    📊 Report
                </button>
            </div>

            <div class="testing-status" id="testingStatus">
                <div class="testing-indicator" id="testingIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text">Ready</span>
                </div>
                <div class="testing-progress" id="testingProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="testingProgressFill"></div>
                    </div>
                    <span class="progress-text" id="testingProgressText">0%</span>
                </div>
            </div>
        `;

        // Insert testing controls at the end of controls section
        controlsSection.appendChild(testingControls);

        // Create testing panel modal
        this.createTestingPanel();
    }

    createTestingPanel() {
        const testingPanel = document.createElement('div');
        testingPanel.className = 'testing-panel-modal';
        testingPanel.id = 'testingPanelModal';
        testingPanel.innerHTML = `
            <div class="testing-panel-content">
                <div class="testing-panel-header">
                    <h3>🧪 Comprehensive Testing Suite</h3>
                    <button class="testing-close" id="closeTestingPanel">×</button>
                </div>
                
                <div class="testing-panel-body">
                    <div class="testing-tabs">
                        <button class="testing-tab active" data-tab="overview">Overview</button>
                        <button class="testing-tab" data-tab="unit">Unit Tests</button>
                        <button class="testing-tab" data-tab="integration">Integration</button>
                        <button class="testing-tab" data-tab="performance">Performance</button>
                        <button class="testing-tab" data-tab="visual">Visual</button>
                        <button class="testing-tab" data-tab="coverage">Coverage</button>
                    </div>
                    
                    <div class="testing-content">
                        <div class="testing-tab-content active" id="overviewTab">
                            <div class="testing-overview">
                                <div class="testing-summary" id="testingSummary">
                                    <div class="summary-card">
                                        <h4>Test Results</h4>
                                        <div class="summary-stats">
                                            <div class="stat">
                                                <span class="stat-value" id="totalTests">0</span>
                                                <span class="stat-label">Total Tests</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value passed" id="passedTests">0</span>
                                                <span class="stat-label">Passed</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value failed" id="failedTests">0</span>
                                                <span class="stat-label">Failed</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value" id="testCoverage">0%</span>
                                                <span class="stat-label">Coverage</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="testing-actions">
                                    <button class="testing-action-btn primary" id="runFullSuite">
                                        🚀 Run Full Test Suite
                                    </button>
                                    <button class="testing-action-btn" id="runQuickTests">
                                        ⚡ Quick Tests
                                    </button>
                                    <button class="testing-action-btn" id="runCriticalTests">
                                        🎯 Critical Tests
                                    </button>
                                    <button class="testing-action-btn" id="exportTestResults">
                                        📤 Export Results
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="testing-tab-content" id="unitTab">
                            <div class="testing-category">
                                <h4>Unit Tests</h4>
                                <div class="test-list" id="unitTestList"></div>
                            </div>
                        </div>
                        
                        <div class="testing-tab-content" id="integrationTab">
                            <div class="testing-category">
                                <h4>Integration Tests</h4>
                                <div class="test-list" id="integrationTestList"></div>
                            </div>
                        </div>
                        
                        <div class="testing-tab-content" id="performanceTab">
                            <div class="testing-category">
                                <h4>Performance Tests</h4>
                                <div class="test-list" id="performanceTestList"></div>
                                <div class="performance-benchmarks" id="performanceBenchmarks"></div>
                            </div>
                        </div>
                        
                        <div class="testing-tab-content" id="visualTab">
                            <div class="testing-category">
                                <h4>Visual Regression Tests</h4>
                                <div class="test-list" id="visualTestList"></div>
                            </div>
                        </div>
                        
                        <div class="testing-tab-content" id="coverageTab">
                            <div class="testing-category">
                                <h4>Code Coverage</h4>
                                <div class="coverage-report" id="coverageReport"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(testingPanel);
    }

    setupTestingEventListeners() {
        // Toggle testing panel
        document.getElementById('toggleTestingPanel')?.addEventListener('click', () => {
            this.toggleTestingPanel();
        });

        // Close testing panel
        document.getElementById('closeTestingPanel')?.addEventListener('click', () => {
            this.closeTestingPanel();
        });

        // Quick action buttons
        document.getElementById('runAllTests')?.addEventListener('click', () => {
            this.runAllTests();
        });

        document.getElementById('runUnitTests')?.addEventListener('click', () => {
            this.runTestCategory('unit');
        });

        document.getElementById('runIntegrationTests')?.addEventListener('click', () => {
            this.runTestCategory('integration');
        });

        document.getElementById('runPerformanceTests')?.addEventListener('click', () => {
            this.runTestCategory('performance');
        });

        document.getElementById('generateTestReport')?.addEventListener('click', () => {
            this.generateTestReport();
        });

        // Panel action buttons
        document.getElementById('runFullSuite')?.addEventListener('click', () => {
            this.runFullTestSuite();
        });

        document.getElementById('runQuickTests')?.addEventListener('click', () => {
            this.runQuickTests();
        });

        document.getElementById('runCriticalTests')?.addEventListener('click', () => {
            this.runCriticalTests();
        });

        document.getElementById('exportTestResults')?.addEventListener('click', () => {
            this.exportTestResults();
        });

        // Tab switching
        document.querySelectorAll('.testing-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTestingTab(e.target.dataset.tab);
            });
        });

        // Testing suite events
        this.testingSuite.addEventListener('test-started', (event) => {
            this.handleTestStarted(event.detail);
        });

        this.testingSuite.addEventListener('test-completed', (event) => {
            this.handleTestCompleted(event.detail);
        });

        this.testingSuite.addEventListener('test-failed', (event) => {
            this.handleTestFailed(event.detail);
        });

        this.testingSuite.addEventListener('suite-completed', (event) => {
            this.handleSuiteCompleted(event.detail);
        });
    }

    enableAutomatedTesting() {
        // Enable automatic testing on critical operations
        this.testingSuite.enableAutomaticTesting({
            onImageAnalysis: true,
            onRecreation: true,
            onBlueprintGeneration: true,
            onExport: true,
            performanceThreshold: 5000 // 5 seconds
        });

        // Setup continuous integration hooks
        this.setupContinuousIntegration();
    }

    toggleTestingPanel() {
        const panel = document.getElementById('testingPanelModal');
        if (panel) {
            panel.classList.toggle('active');
            if (panel.classList.contains('active')) {
                this.loadTestingData();
            }
        }
    }

    closeTestingPanel() {
        const panel = document.getElementById('testingPanelModal');
        if (panel) {
            panel.classList.remove('active');
        }
    }

    async runAllTests() {
        try {
            this.updateTestingStatus('running', 'Running all tests...');
            const results = await this.testingSuite.runAllTests();
            this.updateTestingResults(results);
            this.updateTestingStatus('completed', 'All tests completed');
        } catch (error) {
            this.updateTestingStatus('error', 'Test execution failed');
            console.error('Failed to run tests:', error);
        }
    }

    async runTestCategory(category) {
        try {
            this.updateTestingStatus('running', `Running ${category} tests...`);
            const results = await this.testingSuite.runTestCategory(category);
            this.updateTestingResults(results);
            this.updateTestingStatus('completed', `${category} tests completed`);
        } catch (error) {
            this.updateTestingStatus('error', `${category} tests failed`);
            console.error(`Failed to run ${category} tests:`, error);
        }
    }

    async runFullTestSuite() {
        try {
            this.updateTestingStatus('running', 'Running full test suite...');
            const results = await this.testingSuite.runFullSuite();
            this.updateTestingResults(results);
            this.updateTestingStatus('completed', 'Full test suite completed');
        } catch (error) {
            this.updateTestingStatus('error', 'Full test suite failed');
            console.error('Failed to run full test suite:', error);
        }
    }

    async runQuickTests() {
        try {
            this.updateTestingStatus('running', 'Running quick tests...');
            const results = await this.testingSuite.runQuickTests();
            this.updateTestingResults(results);
            this.updateTestingStatus('completed', 'Quick tests completed');
        } catch (error) {
            this.updateTestingStatus('error', 'Quick tests failed');
            console.error('Failed to run quick tests:', error);
        }
    }

    async runCriticalTests() {
        try {
            this.updateTestingStatus('running', 'Running critical tests...');
            const results = await this.testingSuite.runCriticalTests();
            this.updateTestingResults(results);
            this.updateTestingStatus('completed', 'Critical tests completed');
        } catch (error) {
            this.updateTestingStatus('error', 'Critical tests failed');
            console.error('Failed to run critical tests:', error);
        }
    }

    async generateTestReport() {
        try {
            this.updateTestingStatus('running', 'Generating test report...');
            const report = await this.testingSuite.generateReport();
            this.downloadTestReport(report);
            this.updateTestingStatus('completed', 'Test report generated');
        } catch (error) {
            this.updateTestingStatus('error', 'Report generation failed');
            console.error('Failed to generate test report:', error);
        }
    }

    async exportTestResults() {
        try {
            const results = await this.testingSuite.getLatestResults();
            const exportData = JSON.stringify(results, null, 2);
            this.downloadFile(exportData, `test-results-${Date.now()}.json`, 'application/json');
            this.showNotification('Test results exported successfully', 'success');
        } catch (error) {
            console.error('Failed to export test results:', error);
            this.showNotification('Failed to export test results', 'error');
        }
    }

    updateTestingStatus(status, message) {
        const indicator = document.getElementById('testingIndicator');
        const statusText = indicator?.querySelector('.status-text');
        const statusDot = indicator?.querySelector('.status-dot');

        if (statusText) statusText.textContent = message;
        if (statusDot) {
            statusDot.className = `status-dot ${status}`;
        }
    }

    // Duplicate updateDeploymentUI method removed - using the one at line 1480

    updateTestingResults(results) {
        // Update summary stats
        document.getElementById('totalTests').textContent = results.total || 0;
        document.getElementById('passedTests').textContent = results.passed || 0;
        document.getElementById('failedTests').textContent = results.failed || 0;
        document.getElementById('testCoverage').textContent = `${results.coverage || 0}%`;

        // Update detailed results in each tab
        this.updateTestLists(results);
    }

    updateTestLists(results) {
        // Update unit tests
        this.updateTestList('unitTestList', results.unit || []);
        
        // Update integration tests
        this.updateTestList('integrationTestList', results.integration || []);
        
        // Update performance tests
        this.updateTestList('performanceTestList', results.performance || []);
        
        // Update visual tests
        this.updateTestList('visualTestList', results.visual || []);
        
        // Update coverage report
        this.updateCoverageReport(results.coverage || {});
    }

    updateTestList(listId, tests) {
        const list = document.getElementById(listId);
        if (!list) return;

        list.innerHTML = tests.map(test => `
            <div class="test-item ${test.status}">
                <div class="test-header">
                    <span class="test-name">${test.name}</span>
                    <span class="test-status ${test.status}">${test.status}</span>
                </div>
                <div class="test-details">
                    <span class="test-duration">${test.duration}ms</span>
                    ${test.error ? `<span class="test-error">${test.error}</span>` : ''}
                </div>
            </div>
        `).join('');
    }

    updateCoverageReport(coverage) {
        const report = document.getElementById('coverageReport');
        if (!report) return;

        report.innerHTML = `
            <div class="coverage-summary">
                <div class="coverage-metric">
                    <span class="metric-label">Lines</span>
                    <div class="metric-bar">
                        <div class="metric-fill" style="width: ${coverage.lines || 0}%"></div>
                    </div>
                    <span class="metric-value">${coverage.lines || 0}%</span>
                </div>
                <div class="coverage-metric">
                    <span class="metric-label">Functions</span>
                    <div class="metric-bar">
                        <div class="metric-fill" style="width: ${coverage.functions || 0}%"></div>
                    </div>
                    <span class="metric-value">${coverage.functions || 0}%</span>
                </div>
                <div class="coverage-metric">
                    <span class="metric-label">Branches</span>
                    <div class="metric-bar">
                        <div class="metric-fill" style="width: ${coverage.branches || 0}%"></div>
                    </div>
                    <span class="metric-value">${coverage.branches || 0}%</span>
                </div>
            </div>
        `;
    }

    switchTestingTab(tabName) {
        // Remove active class from all tabs and contents
        document.querySelectorAll('.testing-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.testing-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Add active class to selected tab and content
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}Tab`).classList.add('active');
    }

    async loadTestingData() {
        try {
            const results = await this.testingSuite.getLatestResults();
            if (results) {
                this.updateTestingResults(results);
            }
        } catch (error) {
            console.error('Failed to load testing data:', error);
        }
    }

    setupContinuousIntegration() {
        // Setup hooks for continuous integration
        this.testingSuite.setupCIHooks({
            onCommit: true,
            onPush: true,
            onPullRequest: true,
            webhookUrl: process.env.CI_WEBHOOK_URL
        });
    }

    downloadTestReport(report) {
        const blob = new Blob([report], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `test-report-${Date.now()}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    handleTestStarted(testInfo) {
        console.log('Test started:', testInfo.name);
        this.updateTestingStatus('running', `Running: ${testInfo.name}`);
    }

    handleTestCompleted(testInfo) {
        console.log('Test completed:', testInfo.name, testInfo.status);
    }

    handleTestFailed(testInfo) {
        console.error('Test failed:', testInfo.name, testInfo.error);
    }

    handleSuiteCompleted(suiteInfo) {
        console.log('Test suite completed:', suiteInfo);
        this.updateTestingResults(suiteInfo.results);
    }

    setupSecurityFeatures() {
        if (!this.securityValidator.isInitialized) {
            console.warn('⚠️ Security features not available - Security validator not initialized');
            return;
        }

        // Add security controls to the UI
        this.addSecurityControlsToUI();
        
        // Setup security event listeners
        this.setupSecurityEventListeners();
        
        // Enable security monitoring
        this.enableSecurityMonitoring();
    }

    addSecurityControlsToUI() {
        // Find the main controls section to add security controls
        const controlsSection = document.querySelector('.controls-section') || document.querySelector('.main-controls');
        if (!controlsSection) return;

        // Create security controls container
        const securityControls = document.createElement('div');
        securityControls.className = 'security-controls';
        securityControls.innerHTML = `
            <div class="security-header">
                <h4>🔒 Security Monitor</h4>
                <button class="security-toggle" id="toggleSecurityPanel">
                    <span class="security-icon">🛡️</span>
                    <span class="security-text">Security Panel</span>
                </button>
            </div>
            
            <div class="security-quick-actions" id="securityQuickActions">
                <button class="security-btn primary" id="runSecurityScan">
                    🔍 Security Scan
                </button>
                <button class="security-btn" id="validateInputs">
                    ✅ Validate Inputs
                </button>
                <button class="security-btn" id="checkPermissions">
                    🔐 Check Permissions
                </button>
                <button class="security-btn" id="auditLogs">
                    📋 Audit Logs
                </button>
            </div>

            <div class="security-status" id="securityStatus">
                <div class="security-indicator" id="securityIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text">Secure</span>
                </div>
                <div class="security-level" id="securityLevel">
                    <span class="level-text">Security Level: </span>
                    <span class="level-value" id="securityLevelValue">High</span>
                </div>
            </div>
        `;

        // Insert security controls at the end of controls section
        controlsSection.appendChild(securityControls);

        // Create security panel modal
        this.createSecurityPanel();
    }

    createSecurityPanel() {
        const securityPanel = document.createElement('div');
        securityPanel.className = 'security-panel-modal';
        securityPanel.id = 'securityPanelModal';
        securityPanel.innerHTML = `
            <div class="security-panel-content">
                <div class="security-panel-header">
                    <h3>🛡️ Security Dashboard</h3>
                    <button class="security-close" id="closeSecurityPanel">×</button>
                </div>
                
                <div class="security-panel-body">
                    <div class="security-tabs">
                        <button class="security-tab active" data-tab="overview">Overview</button>
                        <button class="security-tab" data-tab="validation">Input Validation</button>
                        <button class="security-tab" data-tab="permissions">Permissions</button>
                        <button class="security-tab" data-tab="audit">Audit Trail</button>
                        <button class="security-tab" data-tab="threats">Threat Detection</button>
                    </div>
                    
                    <div class="security-content">
                        <div class="security-tab-content active" id="overviewTab">
                            <div class="security-overview">
                                <div class="security-summary" id="securitySummary">
                                    <div class="summary-card">
                                        <h4>Security Status</h4>
                                        <div class="summary-stats">
                                            <div class="stat">
                                                <span class="stat-value" id="securityScore">95</span>
                                                <span class="stat-label">Security Score</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value" id="threatsBlocked">0</span>
                                                <span class="stat-label">Threats Blocked</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value" id="validationsPassed">100%</span>
                                                <span class="stat-label">Validations Passed</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value" id="lastScan">Just now</span>
                                                <span class="stat-label">Last Scan</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="security-actions">
                                    <button class="security-action-btn primary" id="runFullSecurityScan">
                                        🔍 Full Security Scan
                                    </button>
                                    <button class="security-action-btn" id="validateAllInputs">
                                        ✅ Validate All Inputs
                                    </button>
                                    <button class="security-action-btn" id="checkAllPermissions">
                                        🔐 Check All Permissions
                                    </button>
                                    <button class="security-action-btn" id="exportSecurityReport">
                                        📤 Export Report
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="security-tab-content" id="validationTab">
                            <div class="security-category">
                                <h4>Input Validation</h4>
                                <div class="validation-list" id="validationList"></div>
                            </div>
                        </div>
                        
                        <div class="security-tab-content" id="permissionsTab">
                            <div class="security-category">
                                <h4>Permissions & Access Control</h4>
                                <div class="permissions-list" id="permissionsList"></div>
                            </div>
                        </div>
                        
                        <div class="security-tab-content" id="auditTab">
                            <div class="security-category">
                                <h4>Audit Trail</h4>
                                <div class="audit-list" id="auditList"></div>
                            </div>
                        </div>
                        
                        <div class="security-tab-content" id="threatsTab">
                            <div class="security-category">
                                <h4>Threat Detection</h4>
                                <div class="threats-list" id="threatsList"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(securityPanel);
    }

    setupSecurityEventListeners() {
        // Toggle security panel
        document.getElementById('toggleSecurityPanel')?.addEventListener('click', () => {
            this.toggleSecurityPanel();
        });

        // Close security panel
        document.getElementById('closeSecurityPanel')?.addEventListener('click', () => {
            this.closeSecurityPanel();
        });

        // Quick action buttons
        document.getElementById('runSecurityScan')?.addEventListener('click', () => {
            this.runSecurityScan();
        });

        document.getElementById('validateInputs')?.addEventListener('click', () => {
            this.validateAllInputs();
        });

        document.getElementById('checkPermissions')?.addEventListener('click', () => {
            this.checkAllPermissions();
        });

        document.getElementById('auditLogs')?.addEventListener('click', () => {
            this.showAuditLogs();
        });

        // Panel action buttons
        document.getElementById('runFullSecurityScan')?.addEventListener('click', () => {
            this.runFullSecurityScan();
        });

        document.getElementById('validateAllInputs')?.addEventListener('click', () => {
            this.validateAllInputs();
        });

        document.getElementById('checkAllPermissions')?.addEventListener('click', () => {
            this.checkAllPermissions();
        });

        document.getElementById('exportSecurityReport')?.addEventListener('click', () => {
            this.exportSecurityReport();
        });

        // Tab switching
        document.querySelectorAll('.security-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchSecurityTab(e.target.dataset.tab);
            });
        });

        // Security validator events
        this.securityValidator.addEventListener('threat-detected', (event) => {
            this.handleThreatDetected(event.detail);
        });

        this.securityValidator.addEventListener('validation-failed', (event) => {
            this.handleValidationFailed(event.detail);
        });

        this.securityValidator.addEventListener('permission-denied', (event) => {
            this.handlePermissionDenied(event.detail);
        });

        this.securityValidator.addEventListener('security-scan-completed', (event) => {
            this.handleSecurityScanCompleted(event.detail);
        });
    }

    enableSecurityMonitoring() {
        // Enable real-time security monitoring
        this.securityValidator.enableRealTimeMonitoring({
            inputValidation: true,
            permissionChecks: true,
            threatDetection: true,
            auditLogging: true
        });

        // Setup continuous security monitoring
        this.setupContinuousSecurityMonitoring();
    }

    toggleSecurityPanel() {
        const panel = document.getElementById('securityPanelModal');
        if (panel) {
            panel.classList.toggle('active');
            if (panel.classList.contains('active')) {
                this.loadSecurityData();
            }
        }
    }

    closeSecurityPanel() {
        const panel = document.getElementById('securityPanelModal');
        if (panel) {
            panel.classList.remove('active');
        }
    }

    async runSecurityScan() {
        try {
            this.updateSecurityStatus('scanning', 'Running security scan...');
            const results = await this.securityValidator.runSecurityScan();
            this.updateSecurityResults(results);
            this.updateSecurityStatus('secure', 'Security scan completed');
        } catch (error) {
            this.updateSecurityStatus('warning', 'Security scan failed');
            console.error('Failed to run security scan:', error);
        }
    }

    async validateAllInputs() {
        try {
            this.updateSecurityStatus('validating', 'Validating all inputs...');
            const results = await this.securityValidator.validateAllInputs();
            this.updateValidationResults(results);
            this.updateSecurityStatus('secure', 'Input validation completed');
        } catch (error) {
            this.updateSecurityStatus('warning', 'Input validation failed');
            console.error('Failed to validate inputs:', error);
        }
    }

    async checkAllPermissions() {
        try {
            this.updateSecurityStatus('checking', 'Checking permissions...');
            const results = await this.securityValidator.checkAllPermissions();
            this.updatePermissionResults(results);
            this.updateSecurityStatus('secure', 'Permission check completed');
        } catch (error) {
            this.updateSecurityStatus('warning', 'Permission check failed');
            console.error('Failed to check permissions:', error);
        }
    }

    async showAuditLogs() {
        try {
            const logs = await this.securityValidator.getAuditLogs();
            this.displayAuditLogs(logs);
            this.toggleSecurityPanel();
            this.switchSecurityTab('audit');
        } catch (error) {
            console.error('Failed to load audit logs:', error);
            this.showNotification('Failed to load audit logs', 'error');
        }
    }

    async runFullSecurityScan() {
        try {
            this.updateSecurityStatus('scanning', 'Running full security scan...');
            const results = await this.securityValidator.runFullSecurityScan();
            this.updateSecurityResults(results);
            this.updateSecurityStatus('secure', 'Full security scan completed');
        } catch (error) {
            this.updateSecurityStatus('error', 'Full security scan failed');
            console.error('Failed to run full security scan:', error);
        }
    }

    async exportSecurityReport() {
        try {
            const report = await this.securityValidator.generateSecurityReport();
            this.downloadSecurityReport(report);
            this.showNotification('Security report exported successfully', 'success');
        } catch (error) {
            console.error('Failed to export security report:', error);
            this.showNotification('Failed to export security report', 'error');
        }
    }

    updateSecurityStatus(status, message) {
        const indicator = document.getElementById('securityIndicator');
        const statusText = indicator?.querySelector('.status-text');
        const statusDot = indicator?.querySelector('.status-dot');

        if (statusText) statusText.textContent = message;
        if (statusDot) {
            statusDot.className = `status-dot ${status}`;
        }
    }

    updateSecurityResults(results) {
        // Update summary stats
        document.getElementById('securityScore').textContent = results.score || 95;
        document.getElementById('threatsBlocked').textContent = results.threatsBlocked || 0;
        document.getElementById('validationsPassed').textContent = `${results.validationsPassed || 100}%`;
        document.getElementById('lastScan').textContent = new Date().toLocaleTimeString();

        // Update detailed results in each tab
        this.updateSecurityLists(results);
    }

    updateValidationResults(results) {
        this.updateValidationList(results.validations || []);
    }

    updatePermissionResults(results) {
        this.updatePermissionsList(results.permissions || []);
    }

    updateSecurityLists(results) {
        // Update validation list
        this.updateValidationList(results.validations || []);
        
        // Update permissions list
        this.updatePermissionsList(results.permissions || []);
        
        // Update threats list
        this.updateThreatsList(results.threats || []);
    }

    updateValidationList(validations) {
        const list = document.getElementById('validationList');
        if (!list) return;

        list.innerHTML = validations.map(validation => `
            <div class="validation-item ${validation.status}">
                <div class="validation-header">
                    <span class="validation-name">${validation.name}</span>
                    <span class="validation-status ${validation.status}">${validation.status}</span>
                </div>
                <div class="validation-details">
                    <span class="validation-type">${validation.type}</span>
                    ${validation.error ? `<span class="validation-error">${validation.error}</span>` : ''}
                </div>
            </div>
        `).join('');
    }

    updatePermissionsList(permissions) {
        const list = document.getElementById('permissionsList');
        if (!list) return;

        list.innerHTML = permissions.map(permission => `
            <div class="permission-item ${permission.status}">
                <div class="permission-header">
                    <span class="permission-name">${permission.name}</span>
                    <span class="permission-status ${permission.status}">${permission.status}</span>
                </div>
                <div class="permission-details">
                    <span class="permission-resource">${permission.resource}</span>
                    <span class="permission-level">${permission.level}</span>
                </div>
            </div>
        `).join('');
    }

    updateThreatsList(threats) {
        const list = document.getElementById('threatsList');
        if (!list) return;

        list.innerHTML = threats.map(threat => `
            <div class="threat-item ${threat.severity}">
                <div class="threat-header">
                    <span class="threat-name">${threat.name}</span>
                    <span class="threat-severity ${threat.severity}">${threat.severity}</span>
                </div>
                <div class="threat-details">
                    <span class="threat-description">${threat.description}</span>
                    <span class="threat-timestamp">${new Date(threat.timestamp).toLocaleString()}</span>
                </div>
            </div>
        `).join('');
    }

    displayAuditLogs(logs) {
        const list = document.getElementById('auditList');
        if (!list) return;

        list.innerHTML = logs.map(log => `
            <div class="audit-item">
                <div class="audit-header">
                    <span class="audit-action">${log.action}</span>
                    <span class="audit-timestamp">${new Date(log.timestamp).toLocaleString()}</span>
                </div>
                <div class="audit-details">
                    <span class="audit-user">${log.user || 'System'}</span>
                    <span class="audit-resource">${log.resource}</span>
                    ${log.details ? `<span class="audit-description">${log.details}</span>` : ''}
                </div>
            </div>
        `).join('');
    }

    switchSecurityTab(tabName) {
        // Remove active class from all tabs and contents
        document.querySelectorAll('.security-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.security-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Add active class to selected tab and content
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}Tab`).classList.add('active');
    }

    async loadSecurityData() {
        try {
            const securityData = await this.securityValidator.getSecurityData();
            if (securityData) {
                this.updateSecurityResults(securityData);
            }
        } catch (error) {
            console.error('Failed to load security data:', error);
        }
    }

    setupContinuousSecurityMonitoring() {
        // Setup periodic security checks
        setInterval(() => {
            this.performPeriodicSecurityCheck();
        }, 30000); // Every 30 seconds

        // Setup real-time threat monitoring
        this.securityValidator.enableThreatMonitoring();
    }

    async performPeriodicSecurityCheck() {
        try {
            const quickScan = await this.securityValidator.runQuickSecurityCheck();
            if (quickScan.threatsDetected > 0) {
                this.updateSecurityStatus('warning', `${quickScan.threatsDetected} threats detected`);
            }
        } catch (error) {
            console.warn('Periodic security check failed:', error);
        }
    }

    downloadSecurityReport(report) {
        const blob = new Blob([report], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `security-report-${Date.now()}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    handleThreatDetected(threatInfo) {
        console.warn('Security threat detected:', threatInfo);
        this.updateSecurityStatus('warning', `Threat detected: ${threatInfo.type}`);
        
        // Show notification
        this.showNotification(`Security threat detected: ${threatInfo.description}`, 'warning');
        
        // Log the threat
        this.securityValidator.logSecurityEvent({
            type: 'threat_detected',
            severity: threatInfo.severity,
            description: threatInfo.description,
            timestamp: new Date().toISOString()
        });
    }

    handleValidationFailed(validationInfo) {
        console.warn('Input validation failed:', validationInfo);
        this.updateSecurityStatus('warning', `Validation failed: ${validationInfo.field}`);
        
        // Show notification
        this.showNotification(`Input validation failed: ${validationInfo.message}`, 'error');
    }

    handlePermissionDenied(permissionInfo) {
        console.warn('Permission denied:', permissionInfo);
        this.updateSecurityStatus('warning', `Access denied: ${permissionInfo.resource}`);
        
        // Show notification
        this.showNotification(`Access denied: ${permissionInfo.message}`, 'error');
    }

    handleSecurityScanCompleted(scanInfo) {
        console.log('Security scan completed:', scanInfo);
        this.updateSecurityResults(scanInfo.results);
        
        if (scanInfo.results.threatsDetected > 0) {
            this.showNotification(`Security scan completed: ${scanInfo.results.threatsDetected} threats found`, 'warning');
        } else {
            this.showNotification('Security scan completed: No threats detected', 'success');
        }
    }



    setupAIFeatures() {
        if (!this.aiEnhancement.isInitialized) {
            console.warn('⚠️ AI features not available - AI enhancement not initialized');
            return;
        }

        // Add AI controls to the UI
        this.addAIControlsToUI();
        
        // Setup AI analytics display
        this.setupAIAnalytics();
        
        // Enable AI-powered suggestions
        this.enableAISuggestions();
    }

    addAIControlsToUI() {
        // Find the analysis section to add AI controls
        const analysisSection = document.getElementById('analysisSection');
        if (!analysisSection) return;

        // Create AI controls container
        const aiControls = document.createElement('div');
        aiControls.className = 'ai-controls';
        aiControls.innerHTML = `
            <h4>AI-Powered Features</h4>
            <div class="ai-feature-grid">
                <button class="ai-feature-btn" id="aiGrouping" data-feature="grouping">
                    Smart Grouping
                </button>
                <button class="ai-feature-btn" id="aiColors" data-feature="colors">
                    Color Suggestions
                </button>
                <button class="ai-feature-btn" id="aiLayout" data-feature="layout">
                    Layout Analysis
                </button>
                <button class="ai-feature-btn" id="aiPredict" data-feature="predict">
                    Predict Elements
                </button>
            </div>
            <div class="ai-insights" id="aiInsights" style="display: none;">
                <h5>AI Insights</h5>
                <div id="aiInsightsList"></div>
            </div>
            <div class="ai-suggestions" id="aiSuggestions" style="display: none;">
                <div id="aiSuggestionsList"></div>
            </div>
            <div class="ai-status">
                <div class="ai-status-dot" id="aiStatusDot"></div>
                <span id="aiStatusText">AI Ready</span>
            </div>
        `;

        // Insert AI controls after analysis options
        const analysisOptions = analysisSection.querySelector('.analysis-options');
        if (analysisOptions) {
            analysisOptions.parentNode.insertBefore(aiControls, analysisOptions.nextSibling);
        }

        // Setup AI feature event listeners
        this.setupAIEventListeners();
    }

    setupAIEventListeners() {
        // AI Grouping
        document.getElementById('aiGrouping')?.addEventListener('click', () => {
            this.handleAIGroupingSuggestions();
        });

        // AI Color Suggestions
        document.getElementById('aiColors')?.addEventListener('click', () => {
            this.handleAIColorSuggestions();
        });

        // AI Layout Analysis
        document.getElementById('aiLayout')?.addEventListener('click', () => {
            this.handleAILayoutAnalysis();
        });

        // AI Element Prediction
        document.getElementById('aiPredict')?.addEventListener('click', () => {
            this.handleAIPredictElements();
        });
    }

    async handleAIGroupingSuggestions() {
        if (!this.analysisData || !this.analysisData.elements) {
            this.showAIError('No elements available for grouping analysis');
            return;
        }

        try {
            this.setAIProcessing('grouping', true);
            
            const groupingResult = await this.aiEnhancement.groupElements(this.analysisData.elements);
            
            if (groupingResult) {
                this.displayAIInsights('Element Grouping', [
                    { label: 'Groups Found', value: groupingResult.groups.length, confidence: groupingResult.confidence },
                    { label: 'Processing Time', value: `${groupingResult.processingTime.toFixed(1)}ms`, confidence: 'high' }
                ]);
                
                this.displayAISuggestions(groupingResult.suggestions);
                this.highlightElementGroups(groupingResult.groups);
            }
        } catch (error) {
            this.handleAIError('grouping', error);
        } finally {
            this.setAIProcessing('grouping', false);
        }
    }

    async handleAIColorSuggestions() {
        if (!this.analysisData || !this.analysisData.colors) {
            this.showAIError('No colors available for analysis');
            return;
        }

        try {
            this.setAIProcessing('colors', true);
            
            const colorResult = await this.aiEnhancement.suggestColors(this.analysisData.colors);
            
            if (colorResult) {
                this.displayColorSuggestionModal(colorResult);
            }
        } catch (error) {
            this.handleAIError('colors', error);
        } finally {
            this.setAIProcessing('colors', false);
        }
    }

    async handleAILayoutAnalysis() {
        if (!this.analysisData || !this.analysisData.elements) {
            this.showAIError('No elements available for layout analysis');
            return;
        }

        try {
            this.setAIProcessing('layout', true);
            
            const layoutResult = await this.aiEnhancement.analyzeLayout(this.analysisData.elements);
            
            if (layoutResult) {
                this.displayAIInsights('Layout Analysis', [
                    { label: 'Pattern', value: layoutResult.pattern, confidence: layoutResult.confidence },
                    { label: 'Processing Time', value: `${layoutResult.processingTime.toFixed(1)}ms`, confidence: 'high' }
                ]);
                
                this.displayAISuggestions(layoutResult.suggestions);
            }
        } catch (error) {
            this.handleAIError('layout', error);
        } finally {
            this.setAIProcessing('layout', false);
        }
    }

    async handleAIPredictElements() {
        if (!this.recreationData) {
            this.showAIError('No recreation data available for prediction');
            return;
        }

        try {
            this.setAIProcessing('predict', true);
            
            const predictionResult = await this.aiEnhancement.predictElements(this.recreationData);
            
            if (predictionResult) {
                this.displayAIInsights('Element Prediction', [
                    { label: 'Predicted Elements', value: predictionResult.predictedElements.length, confidence: predictionResult.confidence },
                    { label: 'Processing Time', value: `${predictionResult.processingTime.toFixed(1)}ms`, confidence: 'high' }
                ]);
                
                this.displayAISuggestions(predictionResult.suggestions);
                this.showPredictedElements(predictionResult.predictedElements);
            }
        } catch (error) {
            this.handleAIError('predict', error);
        } finally {
            this.setAIProcessing('predict', false);
        }
    }

    displayAIInsights(title, insights) {
        const insightsContainer = document.getElementById('aiInsights');
        const insightsList = document.getElementById('aiInsightsList');
        
        if (!insightsContainer || !insightsList) return;

        insightsList.innerHTML = `<h6>${title}</h6>`;
        
        insights.forEach(insight => {
            const insightItem = document.createElement('div');
            insightItem.className = 'ai-insight-item';
            
            const confidenceClass = this.getConfidenceClass(insight.confidence);
            
            insightItem.innerHTML = `
                <span>${insight.label}:</span>
                <div>
                    <span>${insight.value}</span>
                    <span class="ai-confidence ${confidenceClass}">${this.formatConfidence(insight.confidence)}</span>
                </div>
            `;
            
            insightsList.appendChild(insightItem);
        });
        
        insightsContainer.style.display = 'block';
    }

    displayAISuggestions(suggestions) {
        const suggestionsContainer = document.getElementById('aiSuggestions');
        const suggestionsList = document.getElementById('aiSuggestionsList');
        
        if (!suggestionsContainer || !suggestionsList) return;

        suggestionsList.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const suggestionItem = document.createElement('div');
            suggestionItem.className = 'ai-suggestion';
            suggestionItem.textContent = suggestion;
            suggestionsList.appendChild(suggestionItem);
        });
        
        suggestionsContainer.style.display = 'block';
    }

    displayColorSuggestionModal(colorResult) {
        // Create modal for color suggestions
        const modal = document.createElement('div');
        modal.className = 'ai-color-modal';
        modal.innerHTML = `
            <div class="ai-color-content">
                <div class="ai-color-header">
                    <h3>🎨 AI Color Suggestions</h3>
                    <button class="ai-color-close">&times;</button>
                </div>
                <div class="ai-color-palette" id="aiColorPalette"></div>
                <div class="ai-harmony-info">
                    <h4>Color Harmony Analysis</h4>
                    <div class="ai-harmony-score">
                        <span>Harmony Score:</span>
                        <div class="ai-score-bar">
                            <div class="ai-score-fill" style="width: ${colorResult.harmony.score * 100}%"></div>
                        </div>
                        <span>${(colorResult.harmony.score * 100).toFixed(0)}%</span>
                    </div>
                    <p>${colorResult.harmony.description}</p>
                </div>
                <div class="ai-color-actions">
                    <button class="primary-btn" id="applyAIColors">Apply Suggestions</button>
                    <button class="secondary-btn" id="dismissAIColors">Dismiss</button>
                </div>
            </div>
        `;

        // Add color swatches
        const palette = modal.querySelector('#aiColorPalette');
        colorResult.suggestedColors.forEach((color, index) => {
            const swatch = document.createElement('div');
            swatch.className = 'ai-color-swatch';
            swatch.style.backgroundColor = color;
            swatch.title = color;
            swatch.addEventListener('click', () => {
                swatch.classList.toggle('selected');
            });
            palette.appendChild(swatch);
        });

        // Setup modal event listeners
        modal.querySelector('.ai-color-close').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('#dismissAIColors').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('#applyAIColors').addEventListener('click', () => {
            this.applySelectedColors(modal);
            modal.remove();
        });

        document.body.appendChild(modal);
    }

    applySelectedColors(modal) {
        const selectedSwatches = modal.querySelectorAll('.ai-color-swatch.selected');
        const selectedColors = Array.from(selectedSwatches).map(swatch => swatch.style.backgroundColor);
        
        if (selectedColors.length > 0) {
            // Apply colors to the current recreation
            this.applyColorsToRecreation(selectedColors);
            console.log('✅ Applied AI-suggested colors:', selectedColors);
        }
    }

    applyColorsToRecreation(colors) {
        // Implementation would depend on the recreation engine
        // This is a placeholder for the actual color application logic
        if (this.recreationData && this.recreationData.elements) {
            colors.forEach((color, index) => {
                if (this.recreationData.elements[index]) {
                    this.recreationData.elements[index].fill = color;
                }
            });
            
            // Trigger recreation update
            this.updateRecreationDisplay();
        }
    }

    setupAIAnalytics() {
        // Add AI metrics to performance panel
        const performancePanel = document.getElementById('performancePanel');
        if (!performancePanel) return;

        const metricsContainer = performancePanel.querySelector('.performance-metrics');
        if (!metricsContainer) return;

        const aiMetrics = document.createElement('div');
        aiMetrics.className = 'ai-metrics';
        aiMetrics.innerHTML = `
            <h5>AI Performance</h5>
            <div class="ai-metric-item">
                <span>Predictions:</span>
                <span class="ai-metric-value" id="aiPredictions">0</span>
            </div>
            <div class="ai-metric-item">
                <span>Avg Processing:</span>
                <span class="ai-metric-value" id="aiAvgProcessing">0ms</span>
            </div>
            <div class="ai-metric-item">
                <span>Cache Hit Rate:</span>
                <span class="ai-metric-value" id="aiCacheHitRate">0%</span>
            </div>
            <div class="ai-metric-item">
                <span>Model Status:</span>
                <span class="ai-metric-value good" id="aiModelStatus">Ready</span>
            </div>
        `;

        metricsContainer.appendChild(aiMetrics);

        // Update AI metrics periodically
        setInterval(() => {
            this.updateAIMetrics();
        }, 2000);
    }

    updateAIMetrics() {
        if (!this.aiEnhancement.isInitialized) return;

        const analytics = this.aiEnhancement.getAnalytics();
        
        const predictionsEl = document.getElementById('aiPredictions');
        if (predictionsEl) predictionsEl.textContent = analytics.predictions;

        const avgProcessingEl = document.getElementById('aiAvgProcessing');
        if (avgProcessingEl) avgProcessingEl.textContent = `${analytics.averageProcessingTime.toFixed(1)}ms`;

        const cacheHitRateEl = document.getElementById('aiCacheHitRate');
        if (cacheHitRateEl) cacheHitRateEl.textContent = `${(analytics.cacheHitRate * 100).toFixed(1)}%`;

        const modelStatusEl = document.getElementById('aiModelStatus');
        if (modelStatusEl) {
            const modelStatus = this.aiEnhancement.getModelStatus();
            const loadedModels = Object.values(modelStatus).filter(status => status === 'loaded').length;
            const totalModels = Object.keys(modelStatus).length;
            modelStatusEl.textContent = `${loadedModels}/${totalModels} Loaded`;
            
            if (loadedModels === totalModels) {
                modelStatusEl.className = 'ai-metric-value good';
            } else if (loadedModels > 0) {
                modelStatusEl.className = 'ai-metric-value warning';
            } else {
                modelStatusEl.className = 'ai-metric-value error';
            }
        }
    }

    // Helper methods for AI features
    setAIProcessing(feature, isProcessing) {
        const button = document.querySelector(`[data-feature="${feature}"]`);
        const statusDot = document.getElementById('aiStatusDot');
        const statusText = document.getElementById('aiStatusText');
        
        if (button) {
            if (isProcessing) {
                button.classList.add('processing');
                button.disabled = true;
            } else {
                button.classList.remove('processing');
                button.disabled = false;
            }
        }
        
        if (statusDot && statusText) {
            if (isProcessing) {
                statusDot.className = 'ai-status-dot loading';
                statusText.textContent = `Processing ${feature}...`;
            } else {
                statusDot.className = 'ai-status-dot';
                statusText.textContent = 'AI Ready';
            }
        }
    }

    handleAIError(feature, error) {
        console.error(`AI ${feature} error:`, error);
        
        this.errorHandler.handleError({
            type: this.errorHandler.errorTypes.WORKER,
            severity: this.errorHandler.severityLevels.MEDIUM,
            message: `AI ${feature} failed: ${error.message}`,
            context: `ai_${feature}`,
            recoverable: true
        });
        
        this.showAIError(`AI ${feature} encountered an error. Please try again.`);
    }

    showAIError(message) {
        const statusText = document.getElementById('aiStatusText');
        const statusDot = document.getElementById('aiStatusDot');
        
        if (statusText) statusText.textContent = message;
        if (statusDot) statusDot.className = 'ai-status-dot error';
        
        setTimeout(() => {
            if (statusText) statusText.textContent = 'AI Ready';
            if (statusDot) statusDot.className = 'ai-status-dot';
        }, 3000);
    }

    getConfidenceClass(confidence) {
        if (typeof confidence === 'string') return confidence;
        if (confidence >= 0.8) return 'high';
        if (confidence >= 0.6) return 'medium';
        return 'low';
    }

    formatConfidence(confidence) {
        if (typeof confidence === 'string') return confidence;
        return `${(confidence * 100).toFixed(0)}%`;
    }

    highlightElementGroups(groups) {
        // Implementation would highlight grouped elements in the UI
        console.log('Highlighting element groups:', groups);
    }

    showPredictedElements(predictedElements) {
        // Implementation would show predicted elements in the recreation
        console.log('Showing predicted elements:', predictedElements);
    }

    updateRecreationDisplay() {
        // Implementation would update the recreation display
        console.log('Updating recreation display');
    }

    enableAISuggestions() {
        // Enable AI-powered suggestions during analysis
        if (this.aiEnhancement.isInitialized) {
            console.log('✅ AI-powered suggestions enabled');
        }
    }

    // Safe operation wrappers
    async safeImageUpload(file) {
        try {
            return await this.handleImageUpload(file);
        } catch (error) {
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.UPLOAD,
                severity: this.errorHandler.severityLevels.HIGH,
                message: `Image upload failed: ${error.message}`,
                context: 'image_upload',
                recoverable: true
            });
            throw error;
        }
    }

    async safeAnalysis(imageData) {
        try {
            return await this.safeAnalyze(imageData);
        } catch (error) {
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.ANALYSIS,
                severity: this.errorHandler.severityLevels.HIGH,
                message: `Analysis failed: ${error.message}`,
                context: 'image_analysis',
                recoverable: true
            });
            throw error;
        }
    }

    async safeRecreation(analysisData) {
        try {
            return await this.safeRecreate(analysisData);
        } catch (error) {
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.RECREATION,
                severity: this.errorHandler.severityLevels.HIGH,
                message: `Recreation failed: ${error.message}`,
                context: 'recreation',
                recoverable: true
            });
            throw error;
        }
    }

    async safeExport(format, data) {
        try {
            return await this.handleExport(format, data);
        } catch (error) {
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.EXPORT,
                severity: this.errorHandler.severityLevels.MEDIUM,
                message: `Export failed: ${error.message}`,
                context: 'export',
                recoverable: true
            });
            throw error;
        }
    }

    // Error recovery methods
    retryNetworkOperation(errorId) {
        // Implementation for retrying network operations
        console.log('Retrying network operation for error:', errorId);
    }

    performMemoryCleanup() {
        // Clear caches and perform garbage collection
        if (this.aiEnhancement.isInitialized) {
            this.aiEnhancement.clearCache();
        }
        
        if (this.performanceOptimizer) {
            this.performanceOptimizer.performMemoryCleanup();
        }
        
        console.log('🧹 Memory cleanup completed');
    }

    restartWorkers() {
        // Restart web workers
        if (this.performanceOptimizer) {
            this.performanceOptimizer.restartWorkers();
        }
        console.log('🔄 Workers restarted');
    }

    resetRenderSystem() {
        // Reset the rendering system
        const canvas = document.getElementById('recreationSVG');
        if (canvas) {
            // Clear and reset canvas
            canvas.innerHTML = '';
        }
        console.log('🔄 Render system reset');
    }

    handleCriticalError(error) {
        console.error('💥 Critical error in app initialization:', error);
        
        if (this.errorHandler) {
            this.errorHandler.handleError({
                type: this.errorHandler.errorTypes.RENDERING,
                severity: this.errorHandler.severityLevels.CRITICAL,
                message: `Critical initialization error: ${error.message}`,
                context: 'app_initialization',
                recoverable: false
            });
            
            this.errorHandler.showCriticalErrorPage();
        } else {
            // Fallback error display
            document.body.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100vh; flex-direction: column; font-family: Arial, sans-serif;">
                    <h1 style="color: #f44336;">💥 Critical Error</h1>
                    <p>The application failed to initialize properly.</p>
                    <button onclick="window.location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">Reload Page</button>
                </div>
            `;
        }
    }

    async initializePerformanceOptimizer() {
        try {
            this.performanceOptimizer = new PerformanceOptimizer();
            await this.performanceOptimizer.initialize();
            console.log('✅ Performance optimization enabled');
        } catch (error) {
            console.warn('⚠️ Performance optimizer failed to initialize:', error);
        }
    }

    setupPerformanceMonitoring() {
        // Listen for performance updates
        document.addEventListener('performanceUpdate', (event) => {
            this.handlePerformanceUpdate(event.detail);
        });

        // Listen for worker completion
        document.addEventListener('workerComplete', (event) => {
            this.handleWorkerComplete(event.detail);
        });

        // Setup performance display updates
        this.setupPerformanceDisplay();
    }

    setupPerformanceDisplay() {
        // Update performance indicators in the status bar
        setInterval(() => {
            if (this.performanceOptimizer) {
                const metrics = this.performanceOptimizer.getPerformanceMetrics();
                this.updatePerformanceDisplay(metrics);
            }
        }, 1000);
    }

    updatePerformanceDisplay(metrics) {
        // Update FPS display
        const fpsDisplay = document.getElementById('fpsDisplay');
        if (fpsDisplay) {
            fpsDisplay.textContent = `${metrics.currentFPS} FPS`;
        }

        // Update memory usage
        const memoryDisplay = document.getElementById('memoryUsage');
        if (memoryDisplay && metrics.memoryUsage) {
            memoryDisplay.textContent = `${metrics.memoryUsage.used}MB`;
        }

        // Update visible elements count
        const elementsDisplay = document.getElementById('visibleElements');
        if (elementsDisplay) {
            elementsDisplay.textContent = `${metrics.visibleElements} elements`;
        }
    }

    handlePerformanceUpdate(detail) {
        const { type, data } = detail;
        
        switch (type) {
            case 'fpsUpdate':
                this.onFPSUpdate(data);
                break;
            case 'memoryUpdate':
                this.onMemoryUpdate(data);
                break;
            case 'renderMetrics':
                this.onRenderMetrics(data);
                break;
            case 'performanceLog':
                this.onPerformanceLog(data);
                break;
        }
    }

    onFPSUpdate(data) {
        // Handle FPS updates
        if (data.fps < 30) {
            console.warn('⚠️ Low FPS detected:', data.fps);
            this.optimizeForLowFPS();
        }
    }

    onMemoryUpdate(data) {
        // Handle memory updates
        const usagePercent = (data.used / data.limit) * 100;
        if (usagePercent > 80) {
            console.warn('⚠️ High memory usage:', usagePercent.toFixed(1) + '%');
            this.optimizeMemoryUsage();
        }
    }

    onRenderMetrics(data) {
        // Handle render performance metrics
        if (data.averageRenderTime > 16) { // 60fps = 16.67ms per frame
            console.warn('⚠️ Slow rendering detected:', data.averageRenderTime.toFixed(2) + 'ms');
        }
    }

    onPerformanceLog(data) {
        // Log performance data for analysis
        if (window.DEBUG_PERFORMANCE) {
            console.log('📊 Performance metrics:', data);
        }
    }

    optimizeForLowFPS() {
        if (this.performanceOptimizer) {
            // Reduce render quality temporarily
            this.performanceOptimizer.updateConfig({
                virtualCanvas: {
                    ...this.performanceOptimizer.config.virtualCanvas,
                    renderBatchSize: 25, // Reduce batch size
                    maxVisibleElements: 500 // Reduce max elements
                },
                debouncing: {
                    ...this.performanceOptimizer.config.debouncing,
                    rendering: 32 // Reduce to 30fps
                }
            });
        }
    }

    optimizeMemoryUsage() {
        if (this.performanceOptimizer) {
            // Trigger aggressive memory cleanup
            this.performanceOptimizer.performMemoryCleanup();
            
            // Reduce memory limits
            this.performanceOptimizer.updateConfig({
                memory: {
                    ...this.performanceOptimizer.config.memory,
                    maxPoolSize: 250,
                    elementCacheSize: 100
                }
            });
        }
    }

    handleWorkerComplete(detail) {
        const { workerType, messageType, data, id } = detail;
        
        // Handle different worker completions
        switch (workerType) {
            case 'imageProcessor':
                this.handleImageProcessorComplete(messageType, data, id);
                break;
            case 'analyzer':
                this.handleAnalyzerComplete(messageType, data, id);
                break;
            case 'renderer':
                this.handleRendererComplete(messageType, data, id);
                break;
        }
    }

    handleImageProcessorComplete(messageType, data, id) {
        switch (messageType) {
            case 'imageProcessed':
                console.log('✅ Image processing completed in worker');
                break;
            case 'imageResized':
                console.log('✅ Image resizing completed in worker');
                break;
            case 'imageFiltered':
                console.log('✅ Image filtering completed in worker');
                break;
        }
    }

    handleAnalyzerComplete(messageType, data, id) {
        switch (messageType) {
            case 'elementsAnalyzed':
                console.log('✅ Element analysis completed in worker');
                break;
            case 'patternsDetected':
                console.log('✅ Pattern detection completed in worker');
                break;
            case 'relationshipsCalculated':
                console.log('✅ Relationship calculation completed in worker');
                break;
        }
    }

    handleRendererComplete(messageType, data, id) {
        switch (messageType) {
            case 'elementsRendered':
                console.log('✅ Element rendering completed in worker');
                break;
            case 'thumbnailGenerated':
                console.log('✅ Thumbnail generation completed in worker');
                break;
        }
    }

    initializeUI() {
        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const imageInput = document.getElementById('imageInput');
        
        if (uploadArea && imageInput) {
            uploadArea.addEventListener('click', () => {
                imageInput.click();
            });
            
            uploadArea.addEventListener('dragover', (e) => {
                this.handleDragOver(e);
            });
            
            uploadArea.addEventListener('drop', (e) => {
                this.handleDrop(e);
            });
            
            imageInput.addEventListener('change', (e) => {
                 this.handleImageUpload(e);
             });
        }

        // Analysis controls
        const startAnalysisBtn = document.getElementById('startAnalysis');
        if (startAnalysisBtn) {
            startAnalysisBtn.addEventListener('click', this.handleStartAnalysis.bind(this));
        }

        // Recreation controls
        const startRecreationBtn = document.getElementById('startRecreation');
        if (startRecreationBtn) {
            console.log('✅ Recreation button found and event listener attached');
            startRecreationBtn.addEventListener('click', (event) => {
                console.log('🖱️ Recreation button clicked!', event);
                console.log('🔍 Button state:', {
                    disabled: startRecreationBtn.disabled,
                    visible: startRecreationBtn.style.display !== 'none',
                    classList: Array.from(startRecreationBtn.classList)
                });
                this.handleStartRecreation.bind(this)();
            });
        } else {
            console.error('❌ Recreation button not found in DOM!');
        }

        // Export controls
        const exportButtons = {
            exportSVG: document.getElementById('exportSVG'),
            exportPNG: document.getElementById('exportPNG'),
            exportHTML: document.getElementById('exportHTML'),
            exportBlueprint: document.getElementById('exportBlueprint')
        };

        Object.entries(exportButtons).forEach(([type, button]) => {
            if (button) {
                button.addEventListener('click', () => this.handleExport(type));
            }
        });

        // View controls
        const viewButtons = {
            viewOriginal: document.getElementById('viewOriginal'),
            viewRecreation: document.getElementById('viewRecreation'),
            viewComparison: document.getElementById('viewComparison')
        };

        Object.entries(viewButtons).forEach(([view, button]) => {
            if (button) {
                button.addEventListener('click', () => this.switchView(view));
            }
        });

        // Zoom controls with performance optimization
        const zoomControls = {
            zoomIn: document.getElementById('zoomIn'),
            zoomOut: document.getElementById('zoomOut'),
            resetZoom: document.getElementById('resetZoom')
        };

        Object.entries(zoomControls).forEach(([action, button]) => {
            if (button) {
                button.addEventListener('click', () => this.handleZoomOptimized(action));
            }
        });

        // Accuracy level slider
        const accuracyLevel = document.getElementById('accuracyLevel');
        const accuracyValue = document.getElementById('accuracyValue');
        if (accuracyLevel && accuracyValue) {
            accuracyLevel.addEventListener('input', (e) => {
                accuracyValue.textContent = e.target.value;
            });
        }

        // Setup viewport tracking for performance optimization
        this.setupViewportTracking();
    }

    setupViewportTracking() {
        if (!this.performanceOptimizer) return;

        const updateViewport = () => {
            const svg = document.getElementById('recreationSVG');
            if (svg) {
                const rect = svg.getBoundingClientRect();
                const scale = parseFloat(svg.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);
                
                this.performanceOptimizer.updateViewport(
                    rect.left,
                    rect.top,
                    rect.width,
                    rect.height,
                    scale
                );
            }
        };

        // Debounced viewport updates
        const debouncedUpdate = this.performanceOptimizer.getDebouncedFunction(
            'viewportUpdate',
            updateViewport,
            this.performanceOptimizer.config.debouncing.rendering
        );

        window.addEventListener('scroll', debouncedUpdate);
        window.addEventListener('resize', debouncedUpdate);
        document.addEventListener('wheel', debouncedUpdate);
    }

    setupWorkflow() {
        this.updateWorkflowStep(1);
    }

    updateWorkflowStep(step) {
        this.currentStep = step;
        
        console.log(`🔄 Updating workflow to step ${step}`);
        
        // Update step indicators
        document.querySelectorAll('.step').forEach((stepEl, index) => {
            stepEl.classList.toggle('active', index + 1 === step);
            stepEl.classList.toggle('completed', index + 1 < step);
        });

        // Show/hide relevant sections
        const sections = {
            1: 'uploadSection',
            2: 'analysisSection',
            3: 'recreationSection',
            4: 'exportSection'
        };

        Object.entries(sections).forEach(([stepNum, sectionId]) => {
            const section = document.getElementById(sectionId);
            if (section) {
                const shouldShow = parseInt(stepNum) <= step;
                const currentDisplay = section.style.display;
                
                console.log(`📋 Section ${sectionId} (step ${stepNum}): shouldShow=${shouldShow}, currentDisplay='${currentDisplay}'`);
                
                section.style.display = shouldShow ? 'block' : 'none';
                
                // Special handling for recreation section
                if (sectionId === 'recreationSection' && shouldShow) {
                    console.log('✅ Recreation section should now be visible');
                    // Force visibility and remove any conflicting styles
                    section.style.visibility = 'visible';
                    section.style.opacity = '1';
                    section.removeAttribute('hidden');
                }
                
                console.log(`📋 Section ${sectionId} final display: '${section.style.display}'`);
            } else {
                console.warn(`⚠️ Section ${sectionId} not found in DOM`);
            }
        });
        
        console.log(`✅ Workflow step ${step} update complete`);
    }

    // Drag and Drop handling
    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
        event.currentTarget.classList.add('drag-over');
    }

    handleDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('drag-over');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.processImageFile(files[0]);
        }
    }

    async handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        this.processImageFile(file);
    }

    async processImageFile(file) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showStatus('Please select a valid image file', 'error');
            return;
        }

        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            this.showStatus('File size must be less than 10MB', 'error');
            return;
        }

        this.showStatus('Loading image...');
        this.showProgress(true);

        try {
            const img = new Image();
            img.onload = async () => {
                // Display original image
                const originalImage = document.getElementById('originalImage');
                if (originalImage) {
                    originalImage.src = img.src;
                    originalImage.style.display = 'block';
                }

                // Initialize analyzer with performance optimization
                if (this.performanceOptimizer) {
                    // Process image in worker if available
                    try {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0);
                        
                        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                        await this.performanceOptimizer.processImageAsync(imageData);
                    } catch (workerError) {
                        console.warn('Worker processing failed, using main thread:', workerError);
                    }
                }

                await this.analyzer.initialize(img);
                
                this.showStatus('Image loaded successfully');
                this.showProgress(false);
                this.updateWorkflowStep(2);
            };
            
            img.onerror = () => {
                this.showStatus('Failed to load image', 'error');
                this.showProgress(false);
            };
            
            img.src = URL.createObjectURL(file);
        } catch (error) {
            this.showStatus('Error loading image: ' + error.message, 'error');
            this.showProgress(false);
        }
    }

    async handleStartAnalysis() {
        if (!this.analyzer.isInitialized()) {
            this.showStatus('Please upload an image first', 'error');
            return;
        }

        this.showStatus('Analyzing image...');
        this.showProgress(true, 'Detecting patterns and elements...');

        try {
            // Get analysis options
            const options = {
                detectShapes: document.getElementById('detectShapes')?.checked ?? true,
                detectText: document.getElementById('detectText')?.checked ?? true,
                detectColors: document.getElementById('detectColors')?.checked ?? true,
                detectConnections: document.getElementById('detectConnections')?.checked ?? true
            };

            // Perform analysis with performance optimization
            console.log('🔍 Starting image analysis...');
            this.analysisData = await this.analyzer.analyzeImage(options);
            
            console.log('📊 Analysis completed, data received:', {
                hasData: !!this.analysisData,
                elements: this.analysisData?.elements?.length || 0,
                colors: this.analysisData?.colors?.length || 0,
                textRegions: this.analysisData?.textRegions?.length || 0,
                connections: this.analysisData?.connections?.length || 0,
                dataKeys: this.analysisData ? Object.keys(this.analysisData) : []
            });
            
            // Analyze elements in worker if available
            if (this.performanceOptimizer && this.analysisData.elements) {
                try {
                    console.log('⚡ Enhancing elements with worker...');
                    const enhancedElements = await this.performanceOptimizer.analyzeElementsAsync(
                        this.analysisData.elements
                    );
                    this.analysisData.elements = enhancedElements;
                    console.log('✅ Elements enhanced, count:', enhancedElements.length);
                } catch (workerError) {
                    console.warn('Worker analysis failed, using main thread results:', workerError);
                }
            }
            
            // Display analysis results
            console.log('🎨 Displaying analysis results...');
            this.displayAnalysisResults(this.analysisData);
            
            console.log('✅ Analysis process completed successfully');
            this.showStatus('Analysis complete');
            this.showProgress(false);
            this.updateWorkflowStep(3);
            
        } catch (error) {
            this.showStatus('Analysis failed: ' + error.message, 'error');
            this.showProgress(false);
        }
    }

    displayAnalysisResults(analysisData) {
        // Update analysis results panel
        const resultsContent = document.getElementById('resultsContent');
        if (resultsContent) {
            document.getElementById('elementCount').textContent = analysisData.elements?.length || 0;
            document.getElementById('colorCount').textContent = analysisData.colors?.length || 0;
            document.getElementById('textCount').textContent = analysisData.textRegions?.length || 0;
            document.getElementById('connectionCount').textContent = analysisData.connections?.length || 0;
        }

        // Display color palette
        const colorPalette = document.getElementById('colorPalette');
        if (colorPalette && analysisData.colors) {
            colorPalette.innerHTML = analysisData.colors.map(color => 
                `<div class="color-swatch" style="background-color: ${color.hex}" title="${color.hex}"></div>`
            ).join('');
        }

        // Draw analysis overlay on canvas with performance optimization
        this.drawAnalysisOverlayOptimized(analysisData);
    }

    drawAnalysisOverlayOptimized(analysisData) {
        const canvas = document.getElementById('analysisCanvas');
        const originalImage = document.getElementById('originalImage');
        
        if (!canvas || !originalImage) return;

        // Use performance-optimized rendering
        if (this.performanceOptimizer) {
            const operations = [
                () => this.drawAnalysisOverlay(analysisData)
            ];
            this.performanceOptimizer.batchUpdate(operations);
        } else {
            this.drawAnalysisOverlay(analysisData);
        }
    }

    drawAnalysisOverlay(analysisData) {
        const canvas = document.getElementById('analysisCanvas');
        const originalImage = document.getElementById('originalImage');
        
        if (!canvas || !originalImage) return;

        const ctx = canvas.getContext('2d');
        canvas.width = originalImage.naturalWidth;
        canvas.height = originalImage.naturalHeight;
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw detected elements
        if (analysisData.elements) {
            analysisData.elements.forEach(element => {
                ctx.strokeStyle = '#ff0000';
                ctx.lineWidth = 2;
                
                switch (element.type) {
                    case 'circle':
                        ctx.beginPath();
                        ctx.arc(element.x, element.y, element.radius, 0, 2 * Math.PI);
                        ctx.stroke();
                        break;
                    case 'rectangle':
                        ctx.strokeRect(element.x, element.y, element.width, element.height);
                        break;
                    case 'line':
                        ctx.beginPath();
                        ctx.moveTo(element.x1, element.y1);
                        ctx.lineTo(element.x2, element.y2);
                        ctx.stroke();
                        break;
                }
            });
        }
    }

    async handleStartRecreation() {
        console.log('🎯 handleStartRecreation called');
        console.log('📊 Analysis data available:', !!this.analysisData);
        
        if (this.analysisData) {
            console.log('📊 Analysis data details:', {
                elements: this.analysisData.elements?.length || 0,
                colors: this.analysisData.colors?.length || 0,
                hasData: Object.keys(this.analysisData).length > 0
            });
        }
        
        if (!this.analysisData) {
            console.log('❌ No analysis data - showing error');
            this.showStatus('Please complete image analysis first', 'error');
            return;
        }

        console.log('✅ Starting recreation process...');
        this.showStatus('Generating recreation...');
        this.showProgress(true, 'Creating interactive elements...');

        try {
            // Get recreation options
            const mode = document.getElementById('recreationMode')?.value || 'automatic';
            const accuracy = parseInt(document.getElementById('accuracyLevel')?.value || 7);
            
            console.log('⚙️ Recreation options:', { mode, accuracy });
            console.log('🔧 Engine available:', !!this.engine);
            
            if (!this.engine) {
                throw new Error('Recreation engine not initialized');
            }

            // Generate recreation
            console.log('🚀 Calling recreateFromAnalysis...');
            this.recreationData = await this.engine.recreateFromAnalysis(this.analysisData, {
                mode,
                accuracy,
                interactive: true
            });
            
            console.log('✅ Recreation data generated:', {
                hasData: !!this.recreationData,
                elements: this.recreationData?.elements?.length || 0,
                html: this.recreationData?.html?.length || 0,
                css: this.recreationData?.css?.length || 0
            });

            // Display recreation with performance optimization
            console.log('🎨 Displaying recreation...');
            this.displayRecreationOptimized(this.recreationData);
            
            // Initialize interactive editor
            console.log('🖱️ Initializing interactive editor...');
            this.initializeInteractiveEditor();
            
            console.log('🎉 Recreation process completed successfully');
            this.showStatus('Recreation complete - Interactive editing enabled');
            this.showProgress(false);
            this.updateWorkflowStep(4);
            
        } catch (error) {
            console.error('💥 Recreation failed:', error);
            console.error('Error stack:', error.stack);
            this.showStatus('Recreation failed: ' + error.message, 'error');
            this.showProgress(false);
        }
    }

    displayRecreationOptimized(recreationData) {
        if (this.performanceOptimizer) {
            // Use performance-optimized rendering
            const operations = [
                () => this.displayRecreation(recreationData)
            ];
            this.performanceOptimizer.batchUpdate(operations);
        } else {
            this.displayRecreation(recreationData);
        }
    }

    displayRecreation(recreationData) {
        console.log('🎨 displayRecreation called with data:', {
            hasData: !!recreationData,
            elements: recreationData?.elements?.length || 0
        });
        
        // Show recreation display
        const recreationDisplay = document.getElementById('recreationDisplay');
        if (recreationDisplay) {
            console.log('✅ Recreation display element found, showing it');
            recreationDisplay.style.display = 'block';
        } else {
            console.error('❌ Recreation display element not found!');
        }

        // Generate SVG elements with performance optimization
        const svg = document.getElementById('recreationSVG');
        console.log('🖼️ SVG element found:', !!svg);
        console.log('📊 Recreation data elements:', recreationData?.elements?.length || 0);
        
        if (!svg || !recreationData.elements) {
            console.warn('⚠️ Missing SVG element or recreation data elements');
            return;
        }

        // Clear existing elements
        const layers = svg.querySelectorAll('g[class$="-layer"]');
        console.log('🧹 Clearing existing layers, found:', layers.length);
        layers.forEach(layer => {
            layer.innerHTML = '';
        });

        // Add elements to appropriate layers with pooling
        console.log('🎯 Processing', recreationData.elements.length, 'elements');
        recreationData.elements.forEach((element, index) => {
            const svgElement = this.createSVGElementOptimized(element, index);
            const layerName = this.getElementLayer(element.type);
            const layer = svg.querySelector(`.${layerName}-layer`);
            
            console.log(`📍 Element ${index}:`, {
                type: element.type,
                layerName,
                layerFound: !!layer,
                svgElementCreated: !!svgElement
            });
            
            if (layer && svgElement) {
                layer.appendChild(svgElement);
            }
        });

        // Switch to recreation view
        this.switchView('viewRecreation');
    }

    createSVGElementOptimized(elementData, index) {
        if (this.performanceOptimizer) {
            // Use optimized element creation
            const elementId = `element_${index}_${Date.now()}`;
            const properties = {
                'data-element-id': elementId,
                'data-element-type': elementData.type,
                'fill': elementData.fill || '#000000',
                'stroke': elementData.stroke || 'none',
                'stroke-width': elementData.strokeWidth || 1,
                'opacity': elementData.opacity || 1
            };

            // Add type-specific properties
            switch (elementData.type) {
                case 'circle':
                    properties.cx = elementData.x || 0;
                    properties.cy = elementData.y || 0;
                    properties.r = elementData.radius || 10;
                    break;
                case 'rectangle':
                    properties.x = elementData.x || 0;
                    properties.y = elementData.y || 0;
                    properties.width = elementData.width || 50;
                    properties.height = elementData.height || 50;
                    break;
                case 'line':
                    properties.x1 = elementData.x1 || 0;
                    properties.y1 = elementData.y1 || 0;
                    properties.x2 = elementData.x2 || 50;
                    properties.y2 = elementData.y2 || 50;
                    break;
                case 'text':
                    properties.x = elementData.x || 0;
                    properties.y = elementData.y || 0;
                    properties['font-size'] = elementData.fontSize || 16;
                    properties['font-family'] = elementData.fontFamily || 'Arial';
                    break;
            }

            const element = this.performanceOptimizer.createOptimizedElement(
                elementData.type === 'rectangle' ? 'rect' : elementData.type,
                properties
            );

            if (elementData.type === 'text') {
                element.textContent = elementData.text || 'Text';
            }

            // Make interactive
            element.style.cursor = 'pointer';
            element.classList.add('interactive-element');

            return element;
        } else {
            // Fallback to regular creation
            return this.createSVGElement(elementData, index);
        }
    }

    createSVGElement(elementData, index) {
        const elementId = `element_${index}_${Date.now()}`;
        let svgElement;

        switch (elementData.type) {
            case 'circle':
                svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                svgElement.setAttribute('cx', elementData.x || 0);
                svgElement.setAttribute('cy', elementData.y || 0);
                svgElement.setAttribute('r', elementData.radius || 10);
                break;

            case 'rectangle':
                svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                svgElement.setAttribute('x', elementData.x || 0);
                svgElement.setAttribute('y', elementData.y || 0);
                svgElement.setAttribute('width', elementData.width || 50);
                svgElement.setAttribute('height', elementData.height || 50);
                break;

            case 'line':
                svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                svgElement.setAttribute('x1', elementData.x1 || 0);
                svgElement.setAttribute('y1', elementData.y1 || 0);
                svgElement.setAttribute('x2', elementData.x2 || 50);
                svgElement.setAttribute('y2', elementData.y2 || 50);
                break;

            case 'text':
                svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                svgElement.setAttribute('x', elementData.x || 0);
                svgElement.setAttribute('y', elementData.y || 0);
                svgElement.setAttribute('font-size', elementData.fontSize || 16);
                svgElement.setAttribute('font-family', elementData.fontFamily || 'Arial');
                svgElement.textContent = elementData.text || 'Text';
                break;

            default:
                return null;
        }

        if (svgElement) {
            // Set common attributes
            svgElement.setAttribute('data-element-id', elementId);
            svgElement.setAttribute('data-element-type', elementData.type);
            svgElement.setAttribute('fill', elementData.fill || '#000000');
            svgElement.setAttribute('stroke', elementData.stroke || 'none');
            svgElement.setAttribute('stroke-width', elementData.strokeWidth || 1);
            svgElement.setAttribute('opacity', elementData.opacity || 1);
            
            // Make interactive
            svgElement.style.cursor = 'pointer';
            svgElement.classList.add('interactive-element');
        }

        return svgElement;
    }

    getElementLayer(elementType) {
        const layerMap = {
            'circle': 'shapes',
            'rectangle': 'shapes',
            'line': 'connections',
            'text': 'text',
            'group': 'shapes'
        };
        return layerMap[elementType] || 'shapes';
    }

    initializeInteractiveEditor() {
        if (!this.interactiveEditor) {
            this.interactiveEditor = new InteractiveEditor();
        }
        
        // Load current elements into editor
        if (this.recreationData && this.recreationData.elements) {
            this.interactiveEditor.loadElements(this.recreationData.elements);
        }
    }

    switchView(viewType) {
        // Update view buttons
        document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById(viewType)?.classList.add('active');

        // Show/hide displays
        const displays = {
            viewOriginal: 'originalDisplay',
            viewRecreation: 'recreationDisplay',
            viewComparison: 'comparisonDisplay'
        };

        Object.entries(displays).forEach(([view, displayId]) => {
            const display = document.getElementById(displayId);
            if (display) {
                display.style.display = view === viewType ? 'block' : 'none';
            }
        });

        // Setup comparison view if selected
        if (viewType === 'viewComparison') {
            this.setupComparisonView();
        }

        // Update viewport for performance optimization
        if (this.performanceOptimizer) {
            setTimeout(() => {
                const svg = document.getElementById('recreationSVG');
                if (svg) {
                    const rect = svg.getBoundingClientRect();
                    this.performanceOptimizer.updateViewport(
                        rect.left, rect.top, rect.width, rect.height
                    );
                }
            }, 100);
        }
    }

    setupComparisonView() {
        const comparisonOriginal = document.getElementById('comparisonOriginal');
        const comparisonRecreation = document.getElementById('comparisonRecreation');
        const originalImage = document.getElementById('originalImage');
        const recreationSVG = document.getElementById('recreationSVG');

        if (comparisonOriginal && originalImage) {
            comparisonOriginal.innerHTML = `<img src="${originalImage.src}" alt="Original">`;
        }

        if (comparisonRecreation && recreationSVG) {
            comparisonRecreation.innerHTML = recreationSVG.outerHTML;
        }
    }

    handleZoomOptimized(action) {
        if (this.performanceOptimizer) {
            // Use debounced zoom for smooth performance
            const debouncedZoom = this.performanceOptimizer.getDebouncedFunction(
                'zoom',
                () => this.handleZoom(action),
                this.performanceOptimizer.config.debouncing.userInput
            );
            debouncedZoom();
        } else {
            this.handleZoom(action);
        }
    }

    handleZoom(action) {
        const svg = document.getElementById('recreationSVG');
        if (!svg) return;

        let currentZoom = parseFloat(svg.style.transform.replace(/scale\(([^)]+)\)/, '$1')) || 1;
        
        switch (action) {
            case 'zoomIn':
                currentZoom = Math.min(5, currentZoom * 1.2);
                break;
            case 'zoomOut':
                currentZoom = Math.max(0.1, currentZoom / 1.2);
                break;
            case 'resetZoom':
                currentZoom = 1;
                break;
        }

        svg.style.transform = `scale(${currentZoom})`;
        
        const zoomLevel = document.getElementById('zoomLevel');
        if (zoomLevel) {
            zoomLevel.textContent = Math.round(currentZoom * 100) + '%';
        }

        // Update viewport for performance optimization
        if (this.performanceOptimizer) {
            const rect = svg.getBoundingClientRect();
            this.performanceOptimizer.updateViewport(
                rect.left, rect.top, rect.width, rect.height, currentZoom
            );
        }
    }

    async handleExport(exportType) {
        if (!this.recreationData) {
            this.showStatus('No recreation data to export', 'error');
            return;
        }

        this.showStatus('Preparing export...');

        try {
            let exportData;
            let filename;
            let mimeType;

            switch (exportType) {
                case 'exportSVG':
                    exportData = this.exportSVG();
                    filename = `recreation_${Date.now()}.svg`;
                    mimeType = 'image/svg+xml';
                    break;

                case 'exportPNG':
                    exportData = await this.exportPNG();
                    filename = `recreation_${Date.now()}.png`;
                    mimeType = 'image/png';
                    break;

                case 'exportHTML':
                    exportData = this.exportHTML();
                    filename = `recreation_${Date.now()}.html`;
                    mimeType = 'text/html';
                    break;

                case 'exportBlueprint':
                    exportData = await this.exportBlueprint();
                    filename = `blueprint_${Date.now()}.zip`;
                    mimeType = 'application/zip';
                    break;

                default:
                    throw new Error('Unknown export type');
            }

            // Download file
            this.downloadFile(exportData, filename, mimeType);
            this.showStatus('Export completed successfully');

        } catch (error) {
            this.showStatus('Export failed: ' + error.message, 'error');
        }
    }

    exportSVG() {
        const svg = document.getElementById('recreationSVG');
        if (!svg) throw new Error('No SVG to export');
        
        return svg.outerHTML;
    }

    async exportPNG() {
        const svg = document.getElementById('recreationSVG');
        if (!svg) throw new Error('No SVG to export');

        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                canvas.toBlob(resolve, 'image/png');
            };

            img.onerror = reject;
            
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
            img.src = URL.createObjectURL(svgBlob);
        });
    }

    exportHTML() {
        const code = this.engine.generateCode(this.recreationData);
        
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Recreation</title>
    <style>${code.css}</style>
</head>
<body>
    ${code.html}
    <script>${code.js}</script>
</body>
</html>`;
    }

    async exportBlueprint() {
        const options = {
            includeInteractivity: document.getElementById('includeInteractivity')?.checked ?? true,
            includeAnimations: document.getElementById('includeAnimations')?.checked ?? false,
            includeDocumentation: document.getElementById('includeDocumentation')?.checked ?? true
        };

        const blueprint = await this.blueprintGenerator.generateBlueprint(
            this.recreationData,
            this.analysisData,
            options
        );

        return blueprint;
    }

    downloadFile(data, filename, mimeType) {
        const blob = data instanceof Blob ? data : new Blob([data], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
    }

    showStatus(message, type = 'info') {
        const statusMessage = document.getElementById('statusMessage');
        if (statusMessage) {
            statusMessage.textContent = message;
            statusMessage.className = `status-${type}`;
        }
        
        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    showNotification(message, type = 'info') {
        // Show notification using error handler if available
        if (this.errorHandler && this.errorHandler.showNotification) {
            this.errorHandler.showNotification(message, type);
        } else {
            // Fallback to status display
            this.showStatus(message, type);
        }
    }

    showProgress(show, message = 'Processing...') {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingMessage = document.getElementById('loadingMessage');
        
        if (loadingOverlay) {
            loadingOverlay.style.display = show ? 'flex' : 'none';
        }
        
        if (loadingMessage && show) {
            loadingMessage.textContent = message;
        }

        const uploadProgress = document.getElementById('uploadProgress');
        if (uploadProgress) {
            uploadProgress.style.display = show ? 'block' : 'none';
        }
    }

    // Get current application state
    getState() {
        return {
            currentStep: this.currentStep,
            analysisData: this.analysisData,
            recreationData: this.recreationData,
            interactiveEditor: this.interactiveEditor?.getState(),
            performanceMetrics: this.performanceOptimizer?.getPerformanceMetrics()
        };
    }

    // Load application state
    loadState(state) {
        if (state.currentStep) this.updateWorkflowStep(state.currentStep);
        if (state.analysisData) this.analysisData = state.analysisData;
        if (state.recreationData) {
            this.recreationData = state.recreationData;
            this.displayRecreation(state.recreationData);
        }
        if (state.interactiveEditor && this.interactiveEditor) {
            this.interactiveEditor.loadState(state.interactiveEditor);
        }
    }

    // Cleanup
    destroy() {
        if (this.performanceOptimizer) {
            this.performanceOptimizer.destroy();
        }
        
        if (this.interactiveEditor) {
            this.interactiveEditor.destroy();
        }
        
        if (this.aiEnhancement && this.aiEnhancement.isInitialized) {
            this.aiEnhancement.destroy();
        }
        
        if (this.testingSuite && this.testingSuite.isInitialized) {
            this.testingSuite.destroy();
        }
        
        if (this.securityValidator && this.securityValidator.isInitialized) {
            this.securityValidator.destroy();
        }
        
        if (this.deploymentManager && this.deploymentManager.isInitialized) {
            this.deploymentManager.destroy();
        }
        
        if (this.errorHandler) {
            this.errorHandler.destroy();
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.imageRecreationApp = new ImageRecreationApp();
});

export default ImageRecreationApp;