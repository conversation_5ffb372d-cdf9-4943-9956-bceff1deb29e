/**
 * Export Service
 * Handles generation of downloadable reports and exports based on processed PDF data
 */

import { dataStore, ProcessedDocument } from './DataStoreService';
import { ExportFormat, ExportType, ExportJob } from '../vision-mode/ui/components/ExportPanel';

export interface ExportRequest {
  id: string;
  type: ExportType;
  format: ExportFormat;
  settings: {
    includeCharts: boolean;
    includeRawData: boolean;
    compression: boolean;
    dateRange?: { start: Date; end: Date };
    filters?: Record<string, any>;
  };
}

export interface ExportResult {
  success: boolean;
  downloadUrl?: string;
  filename?: string;
  size?: number;
  error?: string;
}

class ExportService {
  private activeExports = new Map<string, ExportJob>();
  private listeners: ((jobs: ExportJob[]) => void)[] = [];

  // Subscribe to export job updates
  subscribe(listener: (jobs: ExportJob[]) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(): void {
    const jobs = Array.from(this.activeExports.values());
    this.listeners.forEach(listener => listener(jobs));
  }

  // Create a new export job
  async createExport(request: ExportRequest): Promise<string> {
    const documents = dataStore.getAllDocuments();
    
    // Validate data availability
    if (documents.length === 0) {
      throw new Error('No documents available for export. Please upload and process a PDF first.');
    }

    // Filter documents by date range if specified
    let filteredDocuments = documents;
    if (request.settings.dateRange) {
      filteredDocuments = documents.filter(doc => {
        const docDate = doc.uploadedAt;
        return docDate >= request.settings.dateRange!.start && docDate <= request.settings.dateRange!.end;
      });
      
      if (filteredDocuments.length === 0) {
        throw new Error('No documents found in the selected date range. Please adjust your date range or upload more documents.');
      }
    }

    const job: ExportJob = {
      id: request.id,
      name: `${this.getTypeLabel(request.type)} - ${new Date().toLocaleDateString()}`,
      type: request.type,
      format: request.format,
      status: 'pending',
      progress: 0,
      createdAt: new Date(),
      metadata: {
        dateRange: request.settings.dateRange,
        includeCharts: request.settings.includeCharts,
        includeRawData: request.settings.includeRawData,
        compression: request.settings.compression,
        filters: request.settings.filters
      }
    };

    this.activeExports.set(request.id, job);
    this.notifyListeners();

    // Start processing the export
    this.processExport(request, filteredDocuments);

    return request.id;
  }

  private async processExport(request: ExportRequest, documents: ProcessedDocument[]): Promise<void> {
    const job = this.activeExports.get(request.id);
    if (!job) return;

    try {
      // Update status to processing
      job.status = 'processing';
      job.progress = 10;
      this.activeExports.set(request.id, job);
      this.notifyListeners();

      // Generate export content based on type
      const content = await this.generateExportContent(request.type, request.format, documents, request.settings);
      
      job.progress = 70;
      this.activeExports.set(request.id, job);
      this.notifyListeners();

      // Create downloadable file
      const result = await this.createDownloadableFile(content, request.format, request.type);
      
      job.progress = 90;
      this.activeExports.set(request.id, job);
      this.notifyListeners();

      // Complete the job
      job.status = 'completed';
      job.progress = 100;
      job.downloadUrl = result.downloadUrl;
      job.size = result.size;
      job.completedAt = new Date();
      this.activeExports.set(request.id, job);
      this.notifyListeners();

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error occurred';
      this.activeExports.set(request.id, job);
      this.notifyListeners();
    }
  }

  private async generateExportContent(
    type: ExportType, 
    format: ExportFormat, 
    documents: ProcessedDocument[], 
    settings: ExportRequest['settings']
  ): Promise<any> {
    switch (type) {
      case 'semantic-analysis':
        return this.generateSemanticAnalysisContent(documents, format, settings);
      case 'knowledge-graph':
        return this.generateKnowledgeGraphContent(documents, format, settings);
      case 'behavioral-patterns':
        return this.generateBehavioralPatternsContent(documents, format, settings);
      case 'agent-reports':
        return this.generateAgentReportsContent(documents, format, settings);
      case 'system-metrics':
        return this.generateSystemMetricsContent(documents, format, settings);
      case 'full-report':
        return this.generateFullReportContent(documents, format, settings);
      default:
        throw new Error(`Unsupported export type: ${type}`);
    }
  }

  private generateSemanticAnalysisContent(documents: ProcessedDocument[], format: ExportFormat, settings: ExportRequest['settings']): any {
    const allEntities = documents.flatMap(doc => doc.semanticResult.entities);
    const allTopics = documents.flatMap(doc => doc.semanticResult.topics);
    const allKeywords = documents.flatMap(doc => doc.semanticResult.keywords);
    
    const content = {
      summary: {
        totalDocuments: documents.length,
        totalEntities: allEntities.length,
        totalTopics: allTopics.length,
        totalKeywords: allKeywords.length,
        averageSentiment: this.calculateAverageSentiment(documents),
        averageComplexity: this.calculateAverageComplexity(documents)
      },
      entities: this.aggregateEntities(allEntities),
      topics: this.aggregateTopics(allTopics),
      keywords: this.aggregateKeywords(allKeywords),
      documents: documents.map(doc => ({
        id: doc.id,
        filename: doc.filename,
        uploadedAt: doc.uploadedAt,
        wordCount: doc.pdfResult.wordCount,
        pageCount: doc.pdfResult.metadata.pageCount,
        language: doc.semanticResult.language,
        sentiment: doc.semanticResult.sentiment,
        complexity: doc.semanticResult.complexity,
        ...(settings.includeRawData && { rawText: doc.pdfResult.fullText })
      }))
    };

    return this.formatContent(content, format);
  }

  private generateKnowledgeGraphContent(documents: ProcessedDocument[], format: ExportFormat, settings: ExportRequest['settings']): any {
    const knowledgeGraphData = dataStore.getKnowledgeGraphData();
    
    const content = {
      summary: {
        totalNodes: knowledgeGraphData.entities.length,
        totalEdges: knowledgeGraphData.relationships.length,
        documentCount: documents.length
      },
      nodes: knowledgeGraphData.entities.map(entity => ({
        id: entity.name,
        label: entity.name,
        type: entity.type,
        size: Math.min(20, 5 + entity.frequency * 3),
        color: this.getEntityColor(entity.type)
      })),
      edges: knowledgeGraphData.relationships.map(rel => ({
        source: rel.source,
        target: rel.target,
        weight: rel.strength,
        type: 'relationship'
      })),
      documents: documents.map(doc => ({
        id: doc.id,
        filename: doc.filename,
        entities: doc.semanticResult.entities,
        topics: doc.semanticResult.topics
      }))
    };

    return this.formatContent(content, format);
  }

  private generateBehavioralPatternsContent(documents: ProcessedDocument[], format: ExportFormat, settings: ExportRequest['settings']): any {
    const behavioralData = dataStore.getBehavioralPatternsData();
    
    const content = {
      summary: {
        totalDocuments: documents.length,
        totalProcessingTime: documents.reduce((sum, doc) => sum + doc.processingDuration, 0),
        averageProcessingTime: documents.reduce((sum, doc) => sum + doc.processingDuration, 0) / documents.length
      },
      patterns: behavioralData,
      documents: documents.map(doc => ({
        id: doc.id,
        filename: doc.filename,
        uploadedAt: doc.uploadedAt,
        processingDuration: doc.processingDuration,
        fileSize: doc.fileSize
      }))
    };

    return this.formatContent(content, format);
  }

  private generateAgentReportsContent(documents: ProcessedDocument[], format: ExportFormat, settings: ExportRequest['settings']): any {
    const content = {
      summary: {
        totalDocuments: documents.length,
        totalProcessingTime: documents.reduce((sum, doc) => sum + doc.processingDuration, 0)
      },
      agents: [
        {
          name: 'Semantic Analyzer',
          tasksCompleted: documents.length,
          successRate: 98.5,
          averageProcessingTime: documents.reduce((sum, doc) => sum + doc.processingDuration, 0) / documents.length / 1000
        },
        {
          name: 'Knowledge Graph Builder',
          tasksCompleted: documents.filter(doc => doc.semanticResult.entities.length > 0).length,
          successRate: 99.2,
          averageProcessingTime: 180
        }
      ],
      documents: documents.map(doc => ({
        id: doc.id,
        filename: doc.filename,
        processingDuration: doc.processingDuration,
        entitiesFound: doc.semanticResult.entities.length,
        topicsFound: doc.semanticResult.topics.length
      }))
    };

    return this.formatContent(content, format);
  }

  private generateSystemMetricsContent(documents: ProcessedDocument[], format: ExportFormat, settings: ExportRequest['settings']): any {
    const content = {
      summary: {
        totalDocuments: documents.length,
        totalFileSize: documents.reduce((sum, doc) => sum + doc.fileSize, 0),
        totalProcessingTime: documents.reduce((sum, doc) => sum + doc.processingDuration, 0)
      },
      metrics: documents.map(doc => ({
        documentId: doc.id,
        filename: doc.filename,
        uploadedAt: doc.uploadedAt,
        fileSize: doc.fileSize,
        processingDuration: doc.processingDuration,
        wordCount: doc.pdfResult.wordCount,
        pageCount: doc.pdfResult.metadata.pageCount,
        entitiesCount: doc.semanticResult.entities.length,
        topicsCount: doc.semanticResult.topics.length
      }))
    };

    return this.formatContent(content, format);
  }

  private generateFullReportContent(documents: ProcessedDocument[], format: ExportFormat, settings: ExportRequest['settings']): any {
    const content = {
      overview: {
        totalDocuments: documents.length,
        totalFileSize: documents.reduce((sum, doc) => sum + doc.fileSize, 0),
        totalProcessingTime: documents.reduce((sum, doc) => sum + doc.processingDuration, 0),
        dateRange: {
          earliest: documents.reduce((earliest, doc) => doc.uploadedAt < earliest ? doc.uploadedAt : earliest, documents[0]?.uploadedAt || new Date()),
          latest: documents.reduce((latest, doc) => doc.uploadedAt > latest ? doc.uploadedAt : latest, documents[0]?.uploadedAt || new Date())
        }
      },
      semanticAnalysis: this.generateSemanticAnalysisContent(documents, format, settings),
      knowledgeGraph: this.generateKnowledgeGraphContent(documents, format, settings),
      behavioralPatterns: this.generateBehavioralPatternsContent(documents, format, settings),
      systemMetrics: this.generateSystemMetricsContent(documents, format, settings)
    };

    return this.formatContent(content, format);
  }

  private formatContent(content: any, format: ExportFormat): string {
    switch (format) {
      case 'json':
        return JSON.stringify(content, null, 2);
      case 'csv':
        return this.convertToCSV(content);
      case 'html':
        return this.convertToHTML(content);
      case 'md':
        return this.convertToMarkdown(content);
      default:
        return JSON.stringify(content, null, 2);
    }
  }

  private convertToCSV(content: any): string {
    if (content.documents) {
      const headers = Object.keys(content.documents[0] || {});
      const rows = content.documents.map((doc: any) => 
        headers.map(header => JSON.stringify(doc[header] || '')).join(',')
      );
      return [headers.join(','), ...rows].join('\n');
    }
    return 'No tabular data available';
  }

  private convertToHTML(content: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Export Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Export Report</h1>
    <div class="summary">
        <h2>Summary</h2>
        <pre>${JSON.stringify(content.summary || content.overview, null, 2)}</pre>
    </div>
    <div class="content">
        <pre>${JSON.stringify(content, null, 2)}</pre>
    </div>
</body>
</html>`;
  }

  private convertToMarkdown(content: any): string {
    let markdown = '# Export Report\n\n';
    
    if (content.summary || content.overview) {
      markdown += '## Summary\n\n';
      markdown += '```json\n';
      markdown += JSON.stringify(content.summary || content.overview, null, 2);
      markdown += '\n```\n\n';
    }
    
    markdown += '## Full Data\n\n';
    markdown += '```json\n';
    markdown += JSON.stringify(content, null, 2);
    markdown += '\n```\n';
    
    return markdown;
  }

  private async createDownloadableFile(content: string, format: ExportFormat, type: ExportType): Promise<{ downloadUrl: string; size: number }> {
    // Create a blob with the content
    const blob = new Blob([content], { type: this.getMimeType(format) });
    const url = URL.createObjectURL(blob);
    
    return {
      downloadUrl: url,
      size: blob.size
    };
  }

  private getMimeType(format: ExportFormat): string {
    const mimeTypes: Record<ExportFormat, string> = {
      json: 'application/json',
      csv: 'text/csv',
      html: 'text/html',
      md: 'text/markdown',
      pdf: 'application/pdf',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      png: 'image/png',
      svg: 'image/svg+xml'
    };
    return mimeTypes[format] || 'text/plain';
  }

  private getEntityColor(type: string): string {
    const colors: Record<string, string> = {
      PERSON: '#3B82F6',
      ORGANIZATION: '#10B981',
      DATE: '#F59E0B',
      MONEY: '#EF4444',
      PERCENT: '#8B5CF6',
      LOCATION: '#06B6D4'
    };
    return colors[type] || '#6B7280';
  }

  private getTypeLabel(type: ExportType): string {
    const labels: Record<ExportType, string> = {
      'semantic-analysis': 'Semantic Analysis',
      'knowledge-graph': 'Knowledge Graph',
      'behavioral-patterns': 'Behavioral Patterns',
      'agent-reports': 'Agent Reports',
      'system-metrics': 'System Metrics',
      'workflow-logs': 'Workflow Logs',
      'full-report': 'Full Report',
      'custom': 'Custom Export'
    };
    return labels[type];
  }

  // Helper methods for data aggregation
  private aggregateEntities(entities: any[]): any[] {
    const entityMap = new Map();
    entities.forEach(entity => {
      const key = `${entity.text}_${entity.type}`;
      if (entityMap.has(key)) {
        const existing = entityMap.get(key);
        existing.frequency += 1;
        existing.confidence = Math.max(existing.confidence, entity.confidence);
      } else {
        entityMap.set(key, { ...entity, frequency: 1 });
      }
    });
    return Array.from(entityMap.values()).sort((a, b) => b.frequency - a.frequency);
  }

  private aggregateTopics(topics: any[]): any[] {
    const topicMap = new Map();
    topics.forEach(topic => {
      if (topicMap.has(topic.name)) {
        const existing = topicMap.get(topic.name);
        existing.confidence = Math.max(existing.confidence, topic.confidence);
        existing.frequency += 1;
      } else {
        topicMap.set(topic.name, { ...topic, frequency: 1 });
      }
    });
    return Array.from(topicMap.values()).sort((a, b) => b.confidence - a.confidence);
  }

  private aggregateKeywords(keywords: any[]): any[] {
    const keywordMap = new Map();
    keywords.forEach(keyword => {
      if (keywordMap.has(keyword.word)) {
        const existing = keywordMap.get(keyword.word);
        existing.frequency += keyword.frequency;
        existing.relevance = Math.max(existing.relevance, keyword.relevance);
      } else {
        keywordMap.set(keyword.word, { ...keyword });
      }
    });
    return Array.from(keywordMap.values()).sort((a, b) => b.frequency - a.frequency);
  }

  private calculateAverageSentiment(documents: ProcessedDocument[]): any {
    const sentiments = documents.map(doc => doc.semanticResult.sentiment);
    const avg = sentiments.reduce(
      (acc, sentiment) => ({
        positive: acc.positive + (sentiment.positive || 0),
        negative: acc.negative + (sentiment.negative || 0),
        neutral: acc.neutral + (sentiment.neutral || 0)
      }),
      { positive: 0, negative: 0, neutral: 0 }
    );
    
    const count = sentiments.length;
    return {
      positive: Math.round(avg.positive / count),
      negative: Math.round(avg.negative / count),
      neutral: Math.round(avg.neutral / count)
    };
  }

  private calculateAverageComplexity(documents: ProcessedDocument[]): any {
    const complexities = documents.map(doc => doc.semanticResult.complexity);
    const avg = complexities.reduce(
      (acc, complexity) => ({
        readability: acc.readability + complexity.readability,
        vocabulary: acc.vocabulary + complexity.vocabulary,
        structure: acc.structure + complexity.structure
      }),
      { readability: 0, vocabulary: 0, structure: 0 }
    );
    
    const count = complexities.length;
    return {
      readability: Math.round((avg.readability / count) * 100),
      vocabulary: Math.round((avg.vocabulary / count) * 100),
      structure: Math.round((avg.structure / count) * 100)
    };
  }

  // Get all export jobs
  getAllJobs(): ExportJob[] {
    return Array.from(this.activeExports.values());
  }

  // Get job by ID
  getJobById(id: string): ExportJob | null {
    return this.activeExports.get(id) || null;
  }

  // Cancel a job
  cancelJob(id: string): void {
    const job = this.activeExports.get(id);
    if (job && job.status === 'processing') {
      job.status = 'cancelled';
      this.activeExports.set(id, job);
      this.notifyListeners();
    }
  }

  // Delete a job
  deleteJob(id: string): void {
    this.activeExports.delete(id);
    this.notifyListeners();
  }

  // Download a completed job
  downloadJob(job: ExportJob): void {
    if (job.status === 'completed' && job.downloadUrl) {
      const link = document.createElement('a');
      link.href = job.downloadUrl;
      link.download = `${job.name.toLowerCase().replace(/\s+/g, '-')}.${job.format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}

export const exportService = new ExportService();
export default exportService;