document.addEventListener('DOMContentLoaded', () => {
    // make the .number-circle, .ausencias-box, and lines draggable
    interact('.number-circle, .ausencias-box, .connections line')
        .draggable({
            // enable inertial throwing
            inertia: true,
            // keep the element within the area of its parent
            modifiers: [
                interact.modifiers.restrictRect({
                    restriction: 'parent',
                    endOnly: true
                })
            ],
            // enable autoScroll
            autoScroll: true,

            listeners: {
                // call this function on every dragmove event
                move: dragMoveListener,
                end: snapEndListener
            }
        });

    function dragMoveListener(event) {
        var target = event.target;

        if (target.tagName.toLowerCase() === 'line') {
            var x1 = parseFloat(target.getAttribute('x1')) + event.dx;
            var y1 = parseFloat(target.getAttribute('y1')) + event.dy;
            var x2 = parseFloat(target.getAttribute('x2')) + event.dx;
            var y2 = parseFloat(target.getAttribute('y2')) + event.dy;

            target.setAttribute('x1', x1);
            target.setAttribute('y1', y1);
            target.setAttribute('x2', x2);
            target.setAttribute('y2', y2);
        } else {
            // keep the dragged position in the data-x/data-y attributes
            var x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
            var y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;

            // translate the element
            target.style.transform = 'translate(' + x + 'px, ' + y + 'px)';

            // update the position attributes
            target.setAttribute('data-x', x);
            target.setAttribute('data-y', y);
        }
    }

    // Enable dragging for node groups (<g class="number-circle">)
    interact('g.number-circle').draggable({
      listeners: {
        move (event) {
          const target = event.target;
          const circle = target.querySelector('circle');
          if (!circle) return;
          const dx = event.dx;
          const dy = event.dy;
          const cx = parseFloat(circle.getAttribute('cx')) + dx;
          const cy = parseFloat(circle.getAttribute('cy')) + dy;
          circle.setAttribute('cx', cx);
          circle.setAttribute('cy', cy);
          const texts = target.querySelectorAll('text');
          texts.forEach(t => {
            const isLabel = t.classList.contains('label');
            t.setAttribute('x', cx);
            t.setAttribute('y', isLabel ? cy + 20 : cy + 5);
          });
          if (typeof updateConnectedLines === 'function') {
            updateConnectedLines();
          }
        },
        end (event) {
          if (typeof snapEndListener === 'function') {
            snapEndListener(event);
          }
        }
      }
    });

    // Enable dragging for lines (<line> inside <g class="connections">)
    interact('g.connections line').draggable({
      listeners: {
        move (event) {
          const line = event.target;
          const dx = event.dx;
          const dy = event.dy;
          const x1 = parseFloat(line.getAttribute('x1')) + dx;
          const y1 = parseFloat(line.getAttribute('y1')) + dy;
          const x2 = parseFloat(line.getAttribute('x2')) + dx;
          const y2 = parseFloat(line.getAttribute('y2')) + dy;
          line.setAttribute('x1', x1);
          line.setAttribute('y1', y1);
          line.setAttribute('x2', x2);
          line.setAttribute('y2', y2);
        },
        end (event) {
          if (typeof snapEndListener === 'function') {
            snapEndListener(event);
          }
        }
      }
    });

    // Helper to snap values to the grid step
    function snap(value, step) {
      return Math.round(value / step) * step;
    }

    function snapEndListener(event) {
      const grid = 50; // keep 8×8 grid (50-unit steps)
      const target = event.target;
      const tag = target.tagName && target.tagName.toLowerCase();

      if (tag === 'line') {
          const x1 = snap(parseFloat(target.getAttribute('x1')), grid);
          const y1 = snap(parseFloat(target.getAttribute('y1')), grid);
          const x2 = snap(parseFloat(target.getAttribute('x2')), grid);
          const y2 = snap(parseFloat(target.getAttribute('y2')), grid);
          target.setAttribute('x1', x1);
          target.setAttribute('y1', y1);
          target.setAttribute('x2', x2);
          target.setAttribute('y2', y2);
      } else {
          const circle = target.querySelector && target.querySelector('circle');
          if (circle) {
              const cx = snap(parseFloat(circle.getAttribute('cx')), grid);
              const cy = snap(parseFloat(circle.getAttribute('cy')), grid);
              circle.setAttribute('cx', cx);
              circle.setAttribute('cy', cy);
              const texts = target.querySelectorAll('text');
              texts.forEach(t => {
                  const isLabel = t.classList.contains('label');
                  t.setAttribute('x', cx);
                  t.setAttribute('y', isLabel ? cy + 20 : cy + 5);
              });
          }
      }
      if (typeof updateConnectedLines === 'function') {
          updateConnectedLines();
      }
    }

    function updateConnectedLines() {
        // Optional: if you label each line with data-from and data-to that match node ids
        // e.g. <line data-from="H" data-to="G" ...>
        const svg = document.getElementById('numerology-chart');
        if (!svg) return;
        const nodes = Array.from(svg.querySelectorAll('.number-circle'));
        const centers = new Map();
        nodes.forEach(g => {
            const id = g.id;
            const c = g.querySelector('circle');
            if (id && c) centers.set(id, { x: parseFloat(c.getAttribute('cx')), y: parseFloat(c.getAttribute('cy')) });
        });
        const lines = Array.from(svg.querySelectorAll('.connections line'));
        lines.forEach(line => {
            const from = line.getAttribute('data-from');
            const to = line.getAttribute('data-to');
            if (from && to && centers.has(from) && centers.has(to)) {
                const a = centers.get(from);
                const b = centers.get(to);
                line.setAttribute('x1', a.x);
                line.setAttribute('y1', a.y);
                line.setAttribute('x2', b.x);
                line.setAttribute('y2', b.y);
            }
        });
    }

    window.dragMoveListener = dragMoveListener;
});