document.addEventListener('DOMContentLoaded', () => {
    // make the .number-circle, .ausencias-box, and lines draggable
    interact('.number-circle, .ausencias-box, .connections line')
        .draggable({
            // enable inertial throwing
            inertia: true,
            // keep the element within the area of its parent
            modifiers: [
                interact.modifiers.restrictRect({
                    restriction: 'parent',
                    endOnly: true
                })
            ],
            // enable autoScroll
            autoScroll: true,

            listeners: {
                // call this function on every dragmove event
                move: dragMoveListener,
            }
        });

    function dragMoveListener(event) {
        var target = event.target;

        if (target.tagName.toLowerCase() === 'line') {
            var x1 = parseFloat(target.getAttribute('x1')) + event.dx;
            var y1 = parseFloat(target.getAttribute('y1')) + event.dy;
            var x2 = parseFloat(target.getAttribute('x2')) + event.dx;
            var y2 = parseFloat(target.getAttribute('y2')) + event.dy;

            target.setAttribute('x1', x1);
            target.setAttribute('y1', y1);
            target.setAttribute('x2', x2);
            target.setAttribute('y2', y2);
        } else {
            // keep the dragged position in the data-x/data-y attributes
            var x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
            var y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;

            // translate the element
            target.style.transform = 'translate(' + x + 'px, ' + y + 'px)';

            // update the position attributes
            target.setAttribute('data-x', x);
            target.setAttribute('data-y', y);
        }
    }

    // this function is used later in the resizing and gesture demos
    window.dragMoveListener = dragMoveListener;
});