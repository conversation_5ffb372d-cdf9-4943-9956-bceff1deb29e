#!/usr/bin/env node

/**
 * Deployment Script for Image Recreation App
 * Handles deployment to various targets
 */

const fs = require('fs').promises;
const path = require('path');

class DeployScript {
    constructor() {
        this.targets = {
            static: this.deployToStatic.bind(this),
            docker: this.deployToDocker.bind(this),
            cdn: this.deployToCDN.bind(this)
        };
    }

    async run() {
        const target = process.argv[2] || 'static';
        
        console.log(`🚀 Deploying to ${target}...`);
        
        try {
            if (!this.targets[target]) {
                throw new Error(`Unknown deployment target: ${target}`);
            }
            
            await this.targets[target]();
            
            console.log(`✅ Deployment to ${target} completed`);
            
        } catch (error) {
            console.error('❌ Deployment failed:', error);
            process.exit(1);
        }
    }

    async deployToStatic() {
        console.log('📁 Deploying to static hosting...');
        
        // Check if build exists
        const buildExists = await this.checkBuildExists();
        if (!buildExists) {
            console.log('🏗️ Build not found, creating build...');
            const BuildScript = require('./build.js');
            const build = new BuildScript();
            await build.run();
        }
        
        // Generate deployment configuration
        await this.generateStaticConfig();
        
        console.log('📦 Static deployment ready');
        console.log('💡 Upload the dist/ folder to your static hosting provider');
    }

    async deployToDocker() {
        console.log('🐳 Deploying to Docker...');
        
        // Generate Docker-specific files
        await this.generateDockerConfig();
        
        console.log('🐳 Docker deployment ready');
        console.log('💡 Run: docker build -t image-recreation-app .');
        console.log('💡 Run: docker run -p 80:80 image-recreation-app');
    }

    async deployToCDN() {
        console.log('🌐 Deploying to CDN...');
        
        // Generate CDN configuration
        await this.generateCDNConfig();
        
        console.log('🌐 CDN deployment ready');
        console.log('💡 Configure your CDN provider with the generated settings');
    }

    async checkBuildExists() {
        try {
            await fs.access('dist');
            return true;
        } catch {
            return false;
        }
    }

    async generateStaticConfig() {
        const config = {
            redirects: [
                {
                    from: '/app/*',
                    to: '/index.html',
                    status: 200
                }
            ],
            headers: [
                {
                    source: '**/*.@(js|css)',
                    headers: [
                        {
                            key: 'Cache-Control',
                            value: 'public, max-age=31536000, immutable'
                        }
                    ]
                },
                {
                    source: '**/*.@(html)',
                    headers: [
                        {
                            key: 'Cache-Control',
                            value: 'public, max-age=0, must-revalidate'
                        }
                    ]
                }
            ]
        };
        
        await fs.writeFile('netlify.toml', this.generateNetlifyConfig(config));
        await fs.writeFile('vercel.json', JSON.stringify(this.generateVercelConfig(config), null, 2));
    }

    generateNetlifyConfig(config) {
        return `
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/app/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
        `.trim();
    }

    generateVercelConfig(config) {
        return {
            version: 2,
            builds: [
                {
                    src: "package.json",
                    use: "@vercel/static-build",
                    config: {
                        distDir: "dist"
                    }
                }
            ],
            routes: [
                {
                    src: "/app/(.*)",
                    dest: "/index.html"
                }
            ],
            headers: [
                {
                    source: "/(.*).js",
                    headers: [
                        {
                            key: "Cache-Control",
                            value: "public, max-age=31536000, immutable"
                        }
                    ]
                }
            ]
        };
    }

    async generateDockerConfig() {
        // Dockerfile is already created, just ensure it exists
        console.log('🐳 Docker configuration ready');
    }

    async generateCDNConfig() {
        const config = {
            zones: {
                static: {
                    origin: 'https://image-recreation-app.netlify.app',
                    cache: {
                        ttl: 31536000,
                        rules: [
                            {
                                pattern: '*.js',
                                ttl: 31536000
                            },
                            {
                                pattern: '*.css',
                                ttl: 31536000
                            },
                            {
                                pattern: '*.html',
                                ttl: 0
                            }
                        ]
                    }
                }
            }
        };
        
        await fs.writeFile('cdn-config.json', JSON.stringify(config, null, 2));
    }
}

// Run deployment if called directly
if (require.main === module) {
    const deploy = new DeployScript();
    deploy.run();
}

module.exports = DeployScript;