# Vision Mode Technical Architecture
## MCP Multi-Agent Collaboration System

## 1. Architecture Design

```mermaid
graph TD
    A[User Browser] --> B[React Frontend Application]
    B --> C[WebSocket Gateway]
    C --> D[MCP Conductor Service]
    D --> E[Agent Registry]
    D --> F[Workflow Engine]
    D --> G[Message Queue]
    
    E --> H[OCR Agent]
    E --> I[Diagram Parser Agent]
    E --> J[Text Analyzer Agent]
    E --> K[Context Extractor Agent]
    E --> L[Reviewer Agent]
    
    F --> M[Playwright Automation]
    M --> N[External Web Services]
    
    G --> O[Redis Cache]
    D --> P[PostgreSQL Database]
    
    subgraph "Frontend Layer"
        B
    end
    
    subgraph "Gateway Layer"
        C
    end
    
    subgraph "MCP Orchestration Layer"
        D
        E
        F
        G
    end
    
    subgraph "Agent Layer"
        H
        I
        J
        K
        L
    end
    
    subgraph "Automation Layer"
        M
        N
    end
    
    subgraph "Data Layer"
        O
        P
    end
```

## 2. Technology Description

- Frontend: React@18 + TypeScript + TailwindCSS + Vite + Redux Toolkit
- Backend: Node.js@20 + Express@4 + TypeScript + Socket.io
- MCP Framework: Custom MCP implementation with JSON-RPC 2.0
- Agent Runtime: Node.js workers with isolated execution contexts
- Automation: Playwright@1.40 + Puppeteer fallback
- Database: PostgreSQL@15 + Redis@7 for caching
- Message Queue: Bull Queue with Redis backend
- Real-time: WebSocket with Socket.io for live updates

## 3. Route Definitions

| Route | Purpose |
|-------|---------|
| / | Main dashboard with agent orchestration overview |
| /workspace | Semantic analysis workspace with document layering |
| /automation | Playwright automation control center |
| /progress | Real-time progress visualization with Kanban overlay |
| /export | Universal export management and configuration |
| /agents | Agent registry and configuration management |
| /workflows | Workflow designer and template management |
| /settings | System configuration and user preferences |

## 4. API Definitions

### 4.1 Core API

**MCP Conductor Management**
```
POST /api/mcp/conductor/start
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| workflowId | string | true | Unique identifier for the workflow |
| agents | string[] | true | Array of agent types to orchestrate |
| config | object | false | Configuration parameters for the workflow |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| conductorId | string | Unique identifier for the conductor instance |
| status | string | Current status of the conductor |
| agents | object[] | Array of initialized agent instances |

**Agent Communication**
```
POST /api/agents/{agentId}/message
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| message | object | true | MCP-formatted message payload |
| targetAgent | string | false | Specific agent to route message to |
| priority | number | false | Message priority (1-10) |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| messageId | string | Unique identifier for the message |
| status | string | Message processing status |
| response | object | Agent response payload |

**Document Processing**
```
POST /api/documents/analyze
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| document | File | true | Document file to analyze |
| layers | string[] | false | Specific semantic layers to extract |
| workflow | string | false | Custom workflow template to use |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| analysisId | string | Unique identifier for the analysis |
| layers | object | Extracted semantic layers |
| progress | object | Real-time progress tracking information |

**Automation Control**
```
POST /api/automation/playwright/execute
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| script | string | true | Playwright automation script |
| context | object | false | Execution context and variables |
| timeout | number | false | Script execution timeout in milliseconds |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| executionId | string | Unique identifier for the execution |
| result | object | Automation execution results |
| screenshots | string[] | Array of screenshot URLs if captured |

## 5. Server Architecture Diagram

```mermaid
graph TD
    A[Client / Frontend] --> B[API Gateway]
    B --> C[Authentication Middleware]
    C --> D[Controller Layer]
    D --> E[MCP Conductor Service]
    E --> F[Agent Manager Service]
    E --> G[Workflow Engine Service]
    E --> H[Message Queue Service]
    
    F --> I[Agent Repository]
    G --> J[Workflow Repository]
    H --> K[Message Repository]
    
    I --> L[(PostgreSQL)]
    J --> L
    K --> M[(Redis)]
    
    E --> N[WebSocket Service]
    N --> O[Real-time Updates]
    
    subgraph "Presentation Layer"
        A
    end
    
    subgraph "API Layer"
        B
        C
        D
    end
    
    subgraph "Business Logic Layer"
        E
        F
        G
        H
    end
    
    subgraph "Data Access Layer"
        I
        J
        K
    end
    
    subgraph "Data Layer"
        L
        M
    end
    
    subgraph "Real-time Layer"
        N
        O
    end
```

## 6. Data Model

### 6.1 Data Model Definition

```mermaid
erDiagram
    CONDUCTOR ||--o{ AGENT : orchestrates
    CONDUCTOR ||--o{ WORKFLOW : executes
    WORKFLOW ||--o{ TASK : contains
    AGENT ||--o{ MESSAGE : sends
    AGENT ||--o{ MESSAGE : receives
    DOCUMENT ||--o{ ANALYSIS : generates
    ANALYSIS ||--o{ SEMANTIC_LAYER : contains
    USER ||--o{ CONDUCTOR : creates
    USER ||--o{ EXPORT : generates
    
    CONDUCTOR {
        uuid id PK
        string name
        string status
        jsonb config
        timestamp created_at
        timestamp updated_at
        uuid user_id FK
    }
    
    AGENT {
        uuid id PK
        string type
        string name
        string status
        jsonb capabilities
        jsonb config
        uuid conductor_id FK
        timestamp created_at
    }
    
    WORKFLOW {
        uuid id PK
        string name
        string description
        jsonb definition
        string status
        uuid conductor_id FK
        timestamp created_at
    }
    
    TASK {
        uuid id PK
        string name
        string type
        string status
        jsonb input_data
        jsonb output_data
        uuid workflow_id FK
        uuid assigned_agent_id FK
        timestamp created_at
        timestamp completed_at
    }
    
    MESSAGE {
        uuid id PK
        string type
        jsonb payload
        string status
        uuid sender_agent_id FK
        uuid receiver_agent_id FK
        timestamp created_at
    }
    
    DOCUMENT {
        uuid id PK
        string filename
        string content_type
        text content
        jsonb metadata
        uuid user_id FK
        timestamp uploaded_at
    }
    
    ANALYSIS {
        uuid id PK
        string status
        jsonb results
        uuid document_id FK
        uuid conductor_id FK
        timestamp started_at
        timestamp completed_at
    }
    
    SEMANTIC_LAYER {
        uuid id PK
        string layer_type
        jsonb content
        float confidence_score
        uuid analysis_id FK
        uuid extracted_by_agent_id FK
    }
    
    USER {
        uuid id PK
        string email
        string name
        string role
        jsonb preferences
        timestamp created_at
    }
    
    EXPORT {
        uuid id PK
        string format
        string status
        text content
        jsonb metadata
        uuid analysis_id FK
        uuid user_id FK
        timestamp created_at
    }
```

### 6.2 Data Definition Language

**Conductor Table**
```sql
-- Create conductor table
CREATE TABLE conductors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'idle' CHECK (status IN ('idle', 'running', 'paused', 'completed', 'failed')),
    config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_conductors_user_id ON conductors(user_id);
CREATE INDEX idx_conductors_status ON conductors(status);
CREATE INDEX idx_conductors_created_at ON conductors(created_at DESC);
```

**Agent Table**
```sql
-- Create agent table
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(100) NOT NULL CHECK (type IN ('ocr', 'diagram_parser', 'text_analyzer', 'context_extractor', 'reviewer')),
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'idle' CHECK (status IN ('idle', 'busy', 'error', 'offline')),
    capabilities JSONB DEFAULT '{}',
    config JSONB DEFAULT '{}',
    conductor_id UUID REFERENCES conductors(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_agents_conductor_id ON agents(conductor_id);
CREATE INDEX idx_agents_type ON agents(type);
CREATE INDEX idx_agents_status ON agents(status);
```

**Workflow Table**
```sql
-- Create workflow table
CREATE TABLE workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    definition JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    conductor_id UUID REFERENCES conductors(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_workflows_conductor_id ON workflows(conductor_id);
CREATE INDEX idx_workflows_status ON workflows(status);
```

**Task Table**
```sql
-- Create task table
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'assigned', 'running', 'completed', 'failed')),
    input_data JSONB DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    workflow_id UUID REFERENCES workflows(id) ON DELETE CASCADE,
    assigned_agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes
CREATE INDEX idx_tasks_workflow_id ON tasks(workflow_id);
CREATE INDEX idx_tasks_assigned_agent_id ON tasks(assigned_agent_id);
CREATE INDEX idx_tasks_status ON tasks(status);
```

**Message Table**
```sql
-- Create message table
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(100) NOT NULL,
    payload JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'delivered', 'processed', 'failed')),
    sender_agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    receiver_agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_messages_sender_agent_id ON messages(sender_agent_id);
CREATE INDEX idx_messages_receiver_agent_id ON messages(receiver_agent_id);
CREATE INDEX idx_messages_created_at ON messages(created_at DESC);
```

**Initial Data**
```sql
-- Insert default agent types
INSERT INTO agent_types (type, capabilities) VALUES
('ocr', '{"input_formats": ["pdf", "png", "jpg"], "confidence_threshold": 0.8}'),
('diagram_parser', '{"diagram_types": ["flowchart", "uml", "network"], "extraction_methods": ["cv", "ml"]}'),
('text_analyzer', '{"languages": ["en", "es", "fr"], "analysis_types": ["sentiment", "entities", "topics"]}'),
('context_extractor', '{"relationship_types": ["semantic", "structural", "temporal"]}'),
('reviewer', '{"validation_types": ["accuracy", "completeness", "consistency"]}');

-- Insert default workflow templates
INSERT INTO workflow_templates (name, definition) VALUES
('document_analysis', '{
  "steps": [
    {"agent": "ocr", "task": "extract_text"},
    {"agent": "diagram_parser", "task": "parse_diagrams"},
    {"agent": "text_analyzer", "task": "analyze_content"},
    {"agent": "context_extractor", "task": "build_relationships"},
    {"agent": "reviewer", "task": "validate_results"}
  ]
}');
```

## 7. MCP Integration Specifications

### 7.1 MCP Protocol Implementation

**Message Format:**
```typescript
interface MCPMessage {
  jsonrpc: '2.0';
  id: string;
  method: string;
  params?: Record<string, any>;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}
```

**Agent Capabilities Registration:**
```typescript
interface AgentCapabilities {
  id: string;
  type: AgentType;
  methods: string[];
  resources: string[];
  tools: ToolDefinition[];
}
```

### 7.2 Real-time Communication

**WebSocket Events:**
- `agent:status_update` - Agent status changes
- `workflow:progress` - Workflow execution progress
- `task:completed` - Individual task completion
- `message:received` - Inter-agent message delivery
- `analysis:update` - Document analysis progress

### 7.3 Performance Optimization

**Caching Strategy:**
- Redis for agent status and workflow state
- PostgreSQL for persistent data storage
- In-memory caching for frequently accessed configurations

**Scalability Considerations:**
- Horizontal scaling of agent workers
- Load balancing for MCP conductor instances
- Database connection pooling and optimization

This technical architecture provides a robust foundation for implementing the Vision Mode system with transparent, scalable multi-agent collaboration capabilities.