/**
 * PDF Processing Service
 * Handles PDF file parsing, text extraction, and analysis
 */

import * as pdfjsLib from 'pdfjs-dist';

// Configure PDF.js worker to use the file from the public directory
pdfjsLib.GlobalWorkerOptions.workerSrc = `/pdf.worker.mjs`;

export interface PDFMetadata {
  title?: string;
  author?: string;
  subject?: string;
  creator?: string;
  producer?: string;
  creationDate?: Date;
  modificationDate?: Date;
  pageCount: number;
  fileSize: number;
}

export interface PDFPage {
  pageNumber: number;
  text: string;
  width: number;
  height: number;
}

export interface PDFProcessingResult {
  metadata: PDFMetadata;
  pages: PDFPage[];
  fullText: string;
  wordCount: number;
  characterCount: number;
  extractedImages: number;
  processingTime: number;
}

export class PDFProcessingService {
  private static instance: PDFProcessingService;

  public static getInstance(): PDFProcessingService {
    if (!PDFProcessingService.instance) {
      PDFProcessingService.instance = new PDFProcessingService();
    }
    return PDFProcessingService.instance;
  }

  /**
   * Process a PDF file and extract all content
   */
  public async processPDF(file: File): Promise<PDFProcessingResult> {
    const startTime = Date.now();
    
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      
      // Extract metadata
      const metadata = await this.extractMetadata(pdf, file.size);
      
      // Extract text from all pages
      const pages: PDFPage[] = [];
      let fullText = '';
      
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        const viewport = page.getViewport({ scale: 1.0 });
        
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');
        
        pages.push({
          pageNumber: pageNum,
          text: pageText,
          width: viewport.width,
          height: viewport.height
        });
        
        fullText += pageText + '\n';
      }
      
      const processingTime = Date.now() - startTime;
      
      return {
        metadata,
        pages,
        fullText: fullText.trim(),
        wordCount: this.countWords(fullText),
        characterCount: fullText.length,
        extractedImages: 0, // TODO: Implement image extraction
        processingTime
      };
    } catch (error) {
      console.error('Error processing PDF:', error);
      throw new Error(`Failed to process PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract metadata from PDF
   */
  private async extractMetadata(pdf: any, fileSize: number): Promise<PDFMetadata> {
    try {
      const metadata = await pdf.getMetadata();
      const info = metadata.info;
      
      return {
        title: info.Title || undefined,
        author: info.Author || undefined,
        subject: info.Subject || undefined,
        creator: info.Creator || undefined,
        producer: info.Producer || undefined,
        creationDate: info.CreationDate ? new Date(info.CreationDate) : undefined,
        modificationDate: info.ModDate ? new Date(info.ModDate) : undefined,
        pageCount: pdf.numPages,
        fileSize
      };
    } catch (error) {
      console.warn('Could not extract PDF metadata:', error);
      return {
        pageCount: pdf.numPages,
        fileSize
      };
    }
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Extract specific page text
   */
  public async extractPageText(file: File, pageNumber: number): Promise<string> {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      
      if (pageNumber < 1 || pageNumber > pdf.numPages) {
        throw new Error(`Page ${pageNumber} does not exist. PDF has ${pdf.numPages} pages.`);
      }
      
      const page = await pdf.getPage(pageNumber);
      const textContent = await page.getTextContent();
      
      return textContent.items
        .map((item: any) => item.str)
        .join(' ');
    } catch (error) {
      console.error('Error extracting page text:', error);
      throw new Error(`Failed to extract text from page ${pageNumber}`);
    }
  }

  /**
   * Get PDF page count without full processing
   */
  public async getPageCount(file: File): Promise<number> {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      return pdf.numPages;
    } catch (error) {
      console.error('Error getting page count:', error);
      throw new Error('Failed to get PDF page count');
    }
  }

  /**
   * Basic file validation before PDF processing
   */
  private validateFileBasics(file: File): { isValid: boolean; error?: string } {
    // Check file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      return { isValid: false, error: 'PDF file is too large. Maximum size is 50MB.' };
    }

    // Check if file is empty
    if (file.size === 0) {
      return { isValid: false, error: 'PDF file is empty.' };
    }

    // Check minimum file size (PDF files should be at least a few bytes)
    if (file.size < 100) {
      return { isValid: false, error: 'File is too small to be a valid PDF.' };
    }

    return { isValid: true };
  }

  /**
   * Fallback validation using binary signature detection
   */
  private async validatePDFSignature(arrayBuffer: ArrayBuffer): Promise<{ isValid: boolean; error?: string }> {
    try {
      const uint8Array = new Uint8Array(arrayBuffer);
      
      // Check PDF header signature
      const header = String.fromCharCode(...uint8Array.slice(0, 4));
      if (header !== '%PDF') {
        return { isValid: false, error: 'File does not appear to be a valid PDF (missing PDF header).' };
      }

      // Check for PDF version
      const versionBytes = uint8Array.slice(4, 8);
      const version = String.fromCharCode(...versionBytes);
      if (!/^-\d\.\d/.test(version)) {
        return { isValid: false, error: 'Invalid PDF version format.' };
      }

      // Look for EOF marker (%%EOF) near the end of file
      const endBytes = uint8Array.slice(-1024); // Check last 1KB
      const endString = String.fromCharCode(...endBytes);
      if (!endString.includes('%%EOF')) {
        return { isValid: false, error: 'PDF file appears to be truncated or corrupted (missing EOF marker).' };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: 'Failed to validate PDF signature.' };
    }
  }

  /**
   * Validate if file is a valid PDF
   */
  public async validatePDF(file: File): Promise<{ isValid: boolean; error?: string }> {
    try {
      // Step 1: Basic file validation
      const basicValidation = this.validateFileBasics(file);
      if (!basicValidation.isValid) {
        return basicValidation;
      }

      const arrayBuffer = await file.arrayBuffer();
      
      // Step 2: Binary signature validation (fallback method)
      const signatureValidation = await this.validatePDFSignature(arrayBuffer);
      if (!signatureValidation.isValid) {
        return signatureValidation;
      }

      // Step 3: PDF.js validation (primary method)
      try {
        const loadingTask = pdfjsLib.getDocument({ 
          data: arrayBuffer,
          verbosity: 0, // Reduce console output
          stopAtErrors: true
        });
        
        const pdf = await loadingTask.promise;
        
        // Check if PDF has at least one page
        if (pdf.numPages === 0) {
          return { isValid: false, error: 'PDF file contains no pages.' };
        }

        // Try to load the first page to ensure it's readable
        await pdf.getPage(1);
        
        return { isValid: true };
      } catch (pdfError: any) {
        console.error('PDF.js validation failed, trying fallback methods:', pdfError);
        
        // Provide more specific error messages based on the error type
        if (pdfError.name === 'PasswordException') {
          return { isValid: false, error: 'PDF is password protected. Please provide an unprotected PDF.' };
        }
        
        if (pdfError.name === 'InvalidPDFException') {
          return { isValid: false, error: 'PDF file is corrupted or invalid.' };
        }
        
        if (pdfError.name === 'MissingPDFException') {
          return { isValid: false, error: 'PDF file is missing or empty.' };
        }
        
        if (pdfError.message?.includes('Invalid PDF structure')) {
          return { isValid: false, error: 'PDF has an invalid structure and cannot be processed.' };
        }
        
        // If PDF.js fails but signature validation passed, it might still be a valid PDF
        // that PDF.js can't handle - let's try a more lenient approach
        console.warn('PDF.js failed but signature validation passed. Attempting lenient validation.');
        
        try {
          // Try with different PDF.js options
          const lenientTask = pdfjsLib.getDocument({ 
            data: arrayBuffer,
            verbosity: 0,
            stopAtErrors: false, // More lenient
            disableFontFace: true,
            disableRange: true,
            disableStream: true
          });
          
          const lenientPdf = await lenientTask.promise;
          if (lenientPdf.numPages > 0) {
            console.log('PDF validated with lenient settings');
            return { isValid: true };
          }
        } catch (lenientError) {
          console.error('Lenient validation also failed:', lenientError);
        }
        
        return { isValid: false, error: `PDF validation failed: ${pdfError.message || 'Unknown error'}` };
      }
    } catch (error: any) {
      console.error('PDF validation failed:', error);
      return { isValid: false, error: `PDF validation failed: ${error.message || 'Unknown error'}` };
    }
  }
}

export const pdfProcessingService = PDFProcessingService.getInstance();