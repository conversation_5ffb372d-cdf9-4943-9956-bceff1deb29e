/**
 * Enhanced Pattern Recognition Module
 * Advanced algorithms for improved shape detection and classification
 */

class EnhancedPatternRecognition {
    constructor() {
        this.neuralNetworkLoaded = false;
        this.shapeClassifier = null;
        this.textDetector = null;
        this.colorAnalyzer = null;
        this.patternLibrary = new Map();
        this.confidenceThresholds = {
            circle: 0.7,
            rectangle: 0.6,
            triangle: 0.65,
            polygon: 0.5,
            text: 0.8,
            line: 0.4
        };
    }
    
    /**
     * Clean up accumulator by removing entries with low vote counts
     */
    cleanupAccumulator(accumulator, minVotes) {
        const keysToDelete = [];
        
        for (const [key, votes] of accumulator.entries()) {
            if (votes < minVotes) {
                keysToDelete.push(key);
            }
        }
        
        // Remove low-vote entries
        for (const key of keysToDelete) {
            accumulator.delete(key);
        }
        
        // If still too large, remove half of remaining entries
        if (accumulator.size > 40000) {
            const entries = Array.from(accumulator.entries());
            entries.sort((a, b) => a[1] - b[1]); // Sort by votes
            
            const toRemove = Math.floor(entries.length / 2);
            for (let i = 0; i < toRemove; i++) {
                accumulator.delete(entries[i][0]);
            }
        }
    }

    /**
     * Initialize enhanced recognition with ML models
     */
    async initialize() {
        try {
            // Initialize pattern library with common shapes
            this.initializePatternLibrary();
            
            // Load pre-trained models (simulated for now)
            await this.loadShapeClassifier();
            await this.loadTextDetector();
            
            this.neuralNetworkLoaded = true;
            console.log('Enhanced Pattern Recognition initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize enhanced recognition:', error);
            return false;
        }
    }

    /**
     * Advanced circle detection with sub-pixel accuracy
     */
    detectCirclesAdvanced(imageData, canvas) {
        const circles = [];
        const edges = this.detectEdgesAdvanced(imageData, canvas);
        const gradients = this.calculateGradients(imageData, canvas);
        
        // Multi-scale circle detection
        const scales = [0.5, 1.0, 1.5, 2.0];
        
        for (const scale of scales) {
            const scaledCircles = this.detectCirclesAtScale(edges, gradients, canvas, scale);
            circles.push(...scaledCircles);
        }
        
        // Non-maximum suppression
        const filteredCircles = this.nonMaximumSuppression(circles, 'circle');
        
        // Machine learning classification
        return this.classifyShapes(filteredCircles, 'circle');
    }

    /**
     * Detect circles at a specific scale using Hough transform with memory optimization
     */
    detectCirclesAtScale(edges, gradients, canvas, scale) {
        const circles = [];
        const width = canvas.width;
        const height = canvas.height;
        
        // Scale-adjusted parameters with memory optimization
        const minRadius = Math.max(5, Math.floor(10 * scale));
        const maxRadius = Math.min(width, height) / (4 / scale);
        const radiusStep = Math.max(2, Math.floor(3 * scale)); // Increased step to reduce iterations
        
        // Memory-limited Hough accumulator
        const accumulator = new Map();
        const MAX_ACCUMULATOR_SIZE = 50000; // Prevent memory overflow
        
        // Reduced sampling density for memory efficiency
        const sampleStep = Math.max(1, Math.floor(scale)); // Skip pixels based on scale
        
        // Vote for circle centers with optimized sampling
        for (let y = 0; y < height; y += sampleStep) {
            for (let x = 0; x < width; x += sampleStep) {
                const idx = y * width + x;
                
                if (edges[idx] > 0) {
                    // Get gradient direction at this edge point
                    const gradient = gradients[idx] || { angle: 0, magnitude: 0 };
                    
                    // Vote for possible circle centers along gradient direction
                    for (let r = minRadius; r <= maxRadius; r += radiusStep) {
                        // Check accumulator size limit
                        if (accumulator.size >= MAX_ACCUMULATOR_SIZE) {
                            // Clean up low-vote entries to make space
                            this.cleanupAccumulator(accumulator, 2);
                        }
                        
                        // Two possible centers (positive and negative gradient direction)
                        const cx1 = Math.round(x + r * Math.cos(gradient.angle));
                        const cy1 = Math.round(y + r * Math.sin(gradient.angle));
                        const cx2 = Math.round(x - r * Math.cos(gradient.angle));
                        const cy2 = Math.round(y - r * Math.sin(gradient.angle));
                        
                        // Vote for both centers if they're within bounds
                        if (cx1 >= 0 && cx1 < width && cy1 >= 0 && cy1 < height) {
                            const key1 = `${cx1},${cy1},${r}`;
                            accumulator.set(key1, (accumulator.get(key1) || 0) + 1);
                        }
                        
                        if (cx2 >= 0 && cx2 < width && cy2 >= 0 && cy2 < height) {
                            const key2 = `${cx2},${cy2},${r}`;
                            accumulator.set(key2, (accumulator.get(key2) || 0) + 1);
                        }
                    }
                }
            }
        }
        
        // Extract circles from accumulator with memory cleanup
        const threshold = Math.max(6, Math.floor(8 * scale)); // Adjusted for reduced sampling
        
        for (const [key, votes] of accumulator.entries()) {
            if (votes >= threshold) {
                const [cx, cy, r] = key.split(',').map(Number);
                
                // Verify circle by checking edge points
                const confidence = this.verifyCircle(cx, cy, r, edges, width, height);
                
                if (confidence > 0.25) { // Slightly lower threshold due to reduced sampling
                    // Get average color
                    const color = this.getCircleColor(cx, cy, r, canvas);
                    
                    circles.push({
                        type: 'circle',
                        x: cx,
                        y: cy,
                        radius: r,
                        color: color,
                        confidence: confidence,
                        scale: scale,
                        votes: votes
                    });
                }
            }
        }
        
        // Clear accumulator to free memory
        accumulator.clear();
        
        // Suggest garbage collection
        if (typeof global !== 'undefined' && global.gc) {
            global.gc();
        }
        
        return circles;
    }

    verifyCircle(x, y, radius, edges) {
        let edgeCount = 0;
        let totalPoints = 0;
        const angleStep = Math.PI / 32;
        
        for (let angle = 0; angle < 2 * Math.PI; angle += angleStep) {
            const px = Math.round(x + radius * Math.cos(angle));
            const py = Math.round(y + radius * Math.sin(angle));
            
            if (px >= 0 && px < this.canvas.width && py >= 0 && py < this.canvas.height) {
                totalPoints++;
                if (edges[py * this.canvas.width + px] > 0) {
                    edgeCount++;
                }
            }
        }
        
        const confidence = totalPoints > 0 ? edgeCount / totalPoints : 0;
        return confidence > 0.3; // Minimum confidence threshold
    }

    getCircleColor(x, y, radius) {
        let r = 0, g = 0, b = 0, count = 0;
        const data = this.imageData.data;
        const canvasWidth = this.canvas.width;
        
        // Sample points within the circle
        for (let py = Math.max(0, y - radius); py < Math.min(this.canvas.height, y + radius); py++) {
            for (let px = Math.max(0, x - radius); px < Math.min(canvasWidth, x + radius); px++) {
                const distance = Math.sqrt((px - x) ** 2 + (py - y) ** 2);
                if (distance <= radius * 0.8) { // Sample inner area
                    const idx = (py * canvasWidth + px) * 4;
                    r += data[idx];
                    g += data[idx + 1];
                    b += data[idx + 2];
                    count++;
                }
            }
        }
        
        if (count === 0) return { r: 0, g: 0, b: 0, hex: '#000000' };
        
        r = Math.round(r / count);
        g = Math.round(g / count);
        b = Math.round(b / count);
        
        return {
            r, g, b,
            hex: this.rgbToHex(r, g, b)
        };
    }

    rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    /**
     * Advanced rectangle detection with perspective correction
     */
    detectRectanglesAdvanced(imageData, canvas) {
        const rectangles = [];
        const edges = this.detectEdgesAdvanced(imageData, canvas);
        const contours = this.findContoursAdvanced(edges, canvas);
        
        for (const contour of contours) {
            // Polygon approximation
            const polygon = this.approximatePolygon(contour);
            
            if (polygon.length === 4) {
                const rect = this.analyzeQuadrilateral(polygon);
                
                if (rect.isRectangle) {
                    // Perspective correction
                    const correctedRect = this.correctPerspective(rect, imageData, canvas);
                    
                    rectangles.push({
                        type: 'rectangle',
                        ...correctedRect,
                        confidence: this.calculateShapeConfidence(correctedRect, edges),
                        perspective: rect.perspective,
                        corners: polygon
                    });
                }
            }
        }
        
        return this.classifyShapes(rectangles, 'rectangle');
    }

    /**
     * Detect triangles and other polygons
     */
    detectPolygons(imageData, canvas) {
        const polygons = [];
        const edges = this.detectEdgesAdvanced(imageData, canvas);
        const contours = this.findContoursAdvanced(edges, canvas);
        
        for (const contour of contours) {
            const polygon = this.approximatePolygon(contour);
            
            if (polygon.length >= 3 && polygon.length <= 8) {
                const shape = this.classifyPolygon(polygon);
                const bounds = this.getPolygonBounds(polygon);
                const color = this.getAverageColor(bounds, imageData, canvas);
                
                polygons.push({
                    type: shape.type,
                    vertices: polygon,
                    bounds: bounds,
                    color: color,
                    confidence: shape.confidence,
                    properties: shape.properties
                });
            }
        }
        
        return this.filterValidPolygons(polygons);
    }

    /**
     * Advanced text detection with OCR capabilities
     */
    async detectTextAdvanced(imageData, canvas) {
        const textRegions = [];
        
        // Text region detection using MSER (Maximally Stable Extremal Regions)
        const mserRegions = this.detectMSER(imageData, canvas);
        
        // Filter text-like regions
        const textCandidates = this.filterTextRegions(mserRegions);
        
        // Group nearby text regions
        const textLines = this.groupTextRegions(textCandidates);
        
        // OCR processing (simulated)
        for (const line of textLines) {
            const text = await this.performOCR(line, imageData, canvas);
            
            if (text.confidence > this.confidenceThresholds.text) {
                textRegions.push({
                    type: 'text',
                    text: text.content,
                    bounds: line.bounds,
                    fontSize: this.estimateFontSize(line.bounds),
                    fontFamily: this.estimateFontFamily(line),
                    color: this.getTextColor(line, imageData, canvas),
                    confidence: text.confidence,
                    orientation: line.orientation
                });
            }
        }
        
        return textRegions;
    }

    /**
     * Curved shape detection (ellipses, arcs, splines)
     */
    detectCurvedShapes(imageData, canvas) {
        const curves = [];
        const edges = this.detectEdgesAdvanced(imageData, canvas);
        
        // Ellipse detection using Hough transform
        const ellipses = this.detectEllipses(edges, canvas);
        curves.push(...ellipses);
        
        // Arc detection
        const arcs = this.detectArcs(edges, canvas);
        curves.push(...arcs);
        
        // Bezier curve detection
        const bezierCurves = this.detectBezierCurves(edges, canvas);
        curves.push(...bezierCurves);
        
        return this.classifyShapes(curves, 'curve');
    }

    /**
     * Advanced color analysis with semantic understanding
     */
    analyzeColorsAdvanced(imageData, canvas) {
        const analysis = {
            dominantColors: [],
            colorHarmony: null,
            colorTemperature: null,
            colorScheme: null,
            semanticColors: []
        };
        
        // Extract dominant colors with clustering
        analysis.dominantColors = this.extractDominantColorsKMeans(imageData, 8);
        
        // Analyze color harmony
        analysis.colorHarmony = this.analyzeColorHarmony(analysis.dominantColors);
        
        // Determine color temperature
        analysis.colorTemperature = this.calculateColorTemperature(analysis.dominantColors);
        
        // Identify color scheme type
        analysis.colorScheme = this.identifyColorScheme(analysis.dominantColors);
        
        // Semantic color analysis
        analysis.semanticColors = this.analyzeSemanticColors(analysis.dominantColors);
        
        return analysis;
    }

    /**
     * Pattern matching against known templates
     */
    matchPatterns(detectedElements) {
        const matches = [];
        
        for (const element of detectedElements) {
            const patternMatches = this.findPatternMatches(element);
            
            if (patternMatches.length > 0) {
                const bestMatch = patternMatches.reduce((best, current) => 
                    current.similarity > best.similarity ? current : best
                );
                
                if (bestMatch.similarity > 0.8) {
                    matches.push({
                        element: element,
                        pattern: bestMatch.pattern,
                        similarity: bestMatch.similarity,
                        transformation: bestMatch.transformation
                    });
                }
            }
        }
        
        return matches;
    }

    /**
     * Advanced edge detection with multiple algorithms
     */
    detectEdgesAdvanced(imageData, canvas) {
        const width = canvas.width;
        const height = canvas.height;
        const data = imageData.data;
        
        // Combine multiple edge detection algorithms
        const sobelEdges = this.sobelEdgeDetection(data, width, height);
        const cannyEdges = this.cannyEdgeDetection(data, width, height);
        const laplacianEdges = this.laplacianEdgeDetection(data, width, height);
        
        // Combine results with weighted average
        const combinedEdges = new Uint8Array(width * height);
        
        for (let i = 0; i < combinedEdges.length; i++) {
            const combined = (
                sobelEdges[i] * 0.4 +
                cannyEdges[i] * 0.4 +
                laplacianEdges[i] * 0.2
            );
            combinedEdges[i] = Math.min(255, Math.max(0, combined));
        }
        
        return combinedEdges;
    }

    /**
     * Calculate gradients for improved shape detection
     */
    calculateGradients(imageData, canvas) {
        const width = canvas.width;
        const height = canvas.height;
        const data = imageData.data;
        const gradients = {
            magnitude: new Float32Array(width * height),
            direction: new Float32Array(width * height)
        };
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const idx = y * width + x;
                
                // Calculate gradients using Sobel operators
                const gx = this.getSobelGx(data, x, y, width);
                const gy = this.getSobelGy(data, x, y, width);
                
                gradients.magnitude[idx] = Math.sqrt(gx * gx + gy * gy);
                gradients.direction[idx] = Math.atan2(gy, gx);
            }
        }
        
        return gradients;
    }

    /**
     * Non-maximum suppression for shape filtering
     */
    nonMaximumSuppression(shapes, shapeType) {
        if (shapes.length === 0) return shapes;
        
        // Sort by confidence
        shapes.sort((a, b) => b.confidence - a.confidence);
        
        const filtered = [];
        const suppressed = new Set();
        
        for (let i = 0; i < shapes.length; i++) {
            if (suppressed.has(i)) continue;
            
            filtered.push(shapes[i]);
            
            // Suppress overlapping shapes with lower confidence
            for (let j = i + 1; j < shapes.length; j++) {
                if (suppressed.has(j)) continue;
                
                const overlap = this.calculateOverlap(shapes[i], shapes[j], shapeType);
                if (overlap > 0.5) {
                    suppressed.add(j);
                }
            }
        }
        
        return filtered;
    }

    /**
     * Machine learning-based shape classification
     */
    classifyShapes(shapes, expectedType) {
        if (!this.neuralNetworkLoaded) {
            return shapes; // Return as-is if ML not available
        }
        
        return shapes.map(shape => {
            const features = this.extractShapeFeatures(shape);
            const classification = this.shapeClassifier.predict(features);
            
            return {
                ...shape,
                mlConfidence: classification.confidence,
                mlType: classification.type,
                features: features
            };
        }).filter(shape => shape.mlConfidence > this.confidenceThresholds[expectedType]);
    }

    /**
     * Initialize pattern library with common shapes and symbols
     */
    initializePatternLibrary() {
        // Common geometric patterns
        this.patternLibrary.set('arrow', {
            type: 'arrow',
            features: ['triangle', 'rectangle'],
            arrangement: 'connected'
        });
        
        this.patternLibrary.set('star', {
            type: 'star',
            features: ['triangle_array'],
            arrangement: 'radial'
        });
        
        this.patternLibrary.set('cross', {
            type: 'cross',
            features: ['rectangle', 'rectangle'],
            arrangement: 'perpendicular'
        });
        
        // UI elements
        this.patternLibrary.set('button', {
            type: 'button',
            features: ['rectangle', 'text'],
            arrangement: 'contained'
        });
        
        this.patternLibrary.set('checkbox', {
            type: 'checkbox',
            features: ['square', 'checkmark'],
            arrangement: 'contained'
        });
    }

    /**
     * Load simulated shape classifier
     */
    async loadShapeClassifier() {
        // Simulate loading a pre-trained model
        this.shapeClassifier = {
            predict: (features) => {
                // Simplified classification logic
                const confidence = Math.random() * 0.3 + 0.7; // 0.7-1.0
                const types = ['circle', 'rectangle', 'triangle', 'polygon', 'line'];
                const type = types[Math.floor(Math.random() * types.length)];
                
                return { confidence, type };
            }
        };
    }

    /**
     * Load simulated text detector
     */
    async loadTextDetector() {
        this.textDetector = {
            detect: (region) => {
                // Simulate text detection
                return {
                    confidence: Math.random() * 0.2 + 0.8,
                    text: 'Sample Text',
                    language: 'en'
                };
            }
        };
    }

    /**
     * Helper methods for advanced algorithms
     */
    
    sobelEdgeDetection(data, width, height) {
        const edges = new Uint8Array(width * height);
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const gx = this.getSobelGx(data, x, y, width);
                const gy = this.getSobelGy(data, x, y, width);
                const magnitude = Math.sqrt(gx * gx + gy * gy);
                edges[y * width + x] = Math.min(255, magnitude);
            }
        }
        
        return edges;
    }
    
    cannyEdgeDetection(data, width, height) {
        // Simplified Canny edge detection
        const edges = new Uint8Array(width * height);
        const lowThreshold = 50;
        const highThreshold = 150;
        
        // Gaussian blur first
        const blurred = this.gaussianBlur(data, width, height);
        
        // Calculate gradients
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const idx = y * width + x;
                const gx = this.getSobelGx(blurred, x, y, width);
                const gy = this.getSobelGy(blurred, x, y, width);
                const magnitude = Math.sqrt(gx * gx + gy * gy);
                
                if (magnitude > highThreshold) {
                    edges[idx] = 255;
                } else if (magnitude > lowThreshold) {
                    edges[idx] = 128; // Weak edge
                }
            }
        }
        
        // Edge tracking by hysteresis
        return this.hysteresisThresholding(edges, width, height);
    }
    
    laplacianEdgeDetection(data, width, height) {
        // Laplacian edge detection using 3x3 kernel
        const edges = new Uint8Array(width * height);
        const threshold = 30;
        
        // Laplacian kernel: [0, -1, 0; -1, 4, -1; 0, -1, 0]
        const getPixel = (x, y) => {
            if (x < 0 || x >= width || y < 0 || y >= height) return 0;
            const idx = (y * width + x) * 4;
            return (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
        };
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const idx = y * width + x;
                
                // Apply Laplacian kernel
                const laplacian = (
                    -1 * getPixel(x, y - 1) +    // top
                    -1 * getPixel(x - 1, y) +    // left
                    4 * getPixel(x, y) +         // center
                    -1 * getPixel(x + 1, y) +    // right
                    -1 * getPixel(x, y + 1)      // bottom
                );
                
                // Apply threshold and normalize
                const magnitude = Math.abs(laplacian);
                edges[idx] = magnitude > threshold ? 255 : 0;
            }
        }
        
        return edges;
    }
    
    getSobelGx(data, x, y, width) {
        const getPixel = (px, py) => {
            const idx = (py * width + px) * 4;
            return (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
        };
        
        return (
            -1 * getPixel(x-1, y-1) + 1 * getPixel(x+1, y-1) +
            -2 * getPixel(x-1, y) + 2 * getPixel(x+1, y) +
            -1 * getPixel(x-1, y+1) + 1 * getPixel(x+1, y+1)
        );
    }
    
    getSobelGy(data, x, y, width) {
        const getPixel = (px, py) => {
            const idx = (py * width + px) * 4;
            return (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
        };
        
        return (
            -1 * getPixel(x-1, y-1) + -2 * getPixel(x, y-1) + -1 * getPixel(x+1, y-1) +
            1 * getPixel(x-1, y+1) + 2 * getPixel(x, y+1) + 1 * getPixel(x+1, y+1)
        );
    }
    
    extractDominantColorsKMeans(imageData, k) {
        const data = imageData.data;
        const pixels = [];
        
        // Sample pixels
        for (let i = 0; i < data.length; i += 16) {
            if (data[i + 3] > 128) { // Skip transparent
                pixels.push([data[i], data[i + 1], data[i + 2]]);
            }
        }
        
        // K-means clustering
        return this.kMeansClustering(pixels, k);
    }
    
    kMeansClustering(pixels, k) {
        // Simplified k-means implementation
        const centroids = [];
        const clusters = Array(k).fill().map(() => []);
        
        // Initialize centroids randomly
        for (let i = 0; i < k; i++) {
            const randomPixel = pixels[Math.floor(Math.random() * pixels.length)];
            centroids.push([...randomPixel]);
        }
        
        // Iterate until convergence (simplified)
        for (let iter = 0; iter < 10; iter++) {
            // Clear clusters
            clusters.forEach(cluster => cluster.length = 0);
            
            // Assign pixels to nearest centroid
            pixels.forEach(pixel => {
                let minDistance = Infinity;
                let nearestCluster = 0;
                
                centroids.forEach((centroid, i) => {
                    const distance = this.colorDistance(pixel, centroid);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestCluster = i;
                    }
                });
                
                clusters[nearestCluster].push(pixel);
            });
            
            // Update centroids
            centroids.forEach((centroid, i) => {
                if (clusters[i].length > 0) {
                    const sum = clusters[i].reduce((acc, pixel) => [
                        acc[0] + pixel[0],
                        acc[1] + pixel[1],
                        acc[2] + pixel[2]
                    ], [0, 0, 0]);
                    
                    centroid[0] = Math.round(sum[0] / clusters[i].length);
                    centroid[1] = Math.round(sum[1] / clusters[i].length);
                    centroid[2] = Math.round(sum[2] / clusters[i].length);
                }
            });
        }
        
        return centroids.map((centroid, i) => ({
            r: centroid[0],
            g: centroid[1],
            b: centroid[2],
            hex: this.rgbToHex(centroid[0], centroid[1], centroid[2]),
            frequency: clusters[i].length,
            percentage: (clusters[i].length / pixels.length) * 100
        }));
    }
    
    colorDistance(color1, color2) {
        return Math.sqrt(
            Math.pow(color1[0] - color2[0], 2) +
            Math.pow(color1[1] - color2[1], 2) +
            Math.pow(color1[2] - color2[2], 2)
        );
    }
    
    rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }
    
    gaussianBlur(data, width, height, radius = 1) {
        const blurred = new Uint8ClampedArray(data.length);
        const kernel = this.createGaussianKernel(radius);
        const kernelSize = kernel.length;
        const halfKernel = Math.floor(kernelSize / 2);
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let r = 0, g = 0, b = 0, a = 0;
                let weightSum = 0;
                
                for (let ky = -halfKernel; ky <= halfKernel; ky++) {
                    for (let kx = -halfKernel; kx <= halfKernel; kx++) {
                        const px = Math.max(0, Math.min(width - 1, x + kx));
                        const py = Math.max(0, Math.min(height - 1, y + ky));
                        const idx = (py * width + px) * 4;
                        const weight = kernel[ky + halfKernel] * kernel[kx + halfKernel];
                        
                        r += data[idx] * weight;
                        g += data[idx + 1] * weight;
                        b += data[idx + 2] * weight;
                        a += data[idx + 3] * weight;
                        weightSum += weight;
                    }
                }
                
                const outIdx = (y * width + x) * 4;
                blurred[outIdx] = r / weightSum;
                blurred[outIdx + 1] = g / weightSum;
                blurred[outIdx + 2] = b / weightSum;
                blurred[outIdx + 3] = a / weightSum;
            }
        }
        
        return blurred;
    }
    
    createGaussianKernel(radius) {
        const size = radius * 2 + 1;
        const kernel = new Array(size);
        const sigma = radius / 3;
        const twoSigmaSquare = 2 * sigma * sigma;
        let sum = 0;
        
        for (let i = 0; i < size; i++) {
            const x = i - radius;
            kernel[i] = Math.exp(-(x * x) / twoSigmaSquare);
            sum += kernel[i];
        }
        
        // Normalize kernel
        for (let i = 0; i < size; i++) {
            kernel[i] /= sum;
        }
        
        return kernel;
    }
    
    hysteresisThresholding(edges, width, height) {
        const result = new Uint8Array(width * height);
        const visited = new Array(width * height).fill(false);
        
        // Find strong edges and trace connected weak edges
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const idx = y * width + x;
                
                if (edges[idx] === 255 && !visited[idx]) {
                    // Strong edge - trace connected components
                    this.traceEdge(edges, result, visited, x, y, width, height);
                }
            }
        }
        
        return result;
    }
    
    traceEdge(edges, result, visited, startX, startY, width, height) {
        const stack = [[startX, startY]];
        
        while (stack.length > 0) {
            const [x, y] = stack.pop();
            const idx = y * width + x;
            
            if (x < 0 || x >= width || y < 0 || y >= height || visited[idx]) {
                continue;
            }
            
            visited[idx] = true;
            
            if (edges[idx] >= 128) { // Strong or weak edge
                result[idx] = 255;
                
                // Check 8-connected neighbors
                for (let dy = -1; dy <= 1; dy++) {
                    for (let dx = -1; dx <= 1; dx++) {
                        if (dx === 0 && dy === 0) continue;
                        stack.push([x + dx, y + dy]);
                    }
                }
            }
        }
    }
}

// Export the enhanced pattern recognition class
export { EnhancedPatternRecognition };
export default EnhancedPatternRecognition;