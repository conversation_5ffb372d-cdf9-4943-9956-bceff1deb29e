<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pináculo - Numerología</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/interactjs/dist/interact.min.js"></script>
</head>
<body>
    <div class="pinaculo-container">
        <div class="header">
            <span class="header-icon">+</span>
            <span class="header-text">Pináculo</span>
        </div>
        <div class="chart-area">
            <svg class="pinaculo-chart" viewBox="0 0 400 600">
                <!-- Connections -->
                <g class="connections">
                    <line x1="200" y1="50" x2="100" y2="160" />
                    <line x1="200" y1="50" x2="300" y2="160" />
                    <line x1="200" y1="50" x2="350" y2="160" />
                    <line x1="100" y1="160" x2="50" y2="280" />
                    <line x1="100" y1="160" x2="200" y2="280" />
                    <line x1="300" y1="160" x2="200" y2="280" />
                    <line x1="300" y1="160" x2="350" y2="280" />
                    <line x1="50" y1="280" x2="125" y2="400" />
                    <line x1="200" y1="280" x2="125" y2="400" />
                    <line x1="200" y1="280" x2="275" y2="400" />
                    <line x1="350" y1="280" x2="275" y2="400" />
                    <line x1="125" y1="400" x2="200" y2="480" />
                    <line x1="275" y1="400" x2="200" y2="480" />
                </g>

                <!-- Number Circles -->
                <svg id="numerology-chart" viewBox="0 0 400 600" preserveAspectRatio="xMidYMid meet">
                    <!-- Connection Lines -->
                    <line x1="200" y1="40" x2="100" y2="160"/> <!-- H-E -->
                    <line x1="100" y1="160" x2="50" y2="250"/> <!-- E-A -->
                    <line x1="100" y1="160" x2="150" y2="250"/> <!-- E-B -->
                    <line x1="200" y1="40" x2="200" y2="100"/> <!-- H-G -->
                    <line x1="150" y1="160" x2="200" y2="100"/> <!-- I-G -->
                    <line x1="250" y1="160" x2="200" y2="100"/> <!-- F-G -->
                    <line x1="200" y1="40" x2="300" y2="160"/> <!-- H-F -->
                    <line x1="300" y1="160" x2="250" y2="250"/> <!-- F-C -->
                    <line x1="300" y1="160" x2="350" y2="250"/> <!-- F-D -->
                    <line x1="200" y1="40" x2="350" y2="250"/> <!-- H-J -->
                    <!-- Number Circles -->
                    <g class="number-circle green" id="H">
                        <circle cx="200" cy="40" r="20"/>
                        <text x="200" y="45">7</text>
                        <text class="label" x="200" y="65">H</text>
                    </g>
                    <g class="number-circle green" id="G">
                        <circle cx="200" cy="100" r="20"/>
                        <text x="200" y="105">8</text>
                        <text class="label" x="200" y="125">G</text>
                    </g>
                    <g class="number-circle green" id="E">
                        <circle cx="100" cy="160" r="20"/>
                        <text x="100" y="165">7</text>
                        <text class="label" x="100" y="185">E</text>
                    </g>
                    <g class="number-circle green" id="I">
                        <circle cx="175" cy="160" r="20"/>
                        <text x="175" y="165">7</text>
                        <text class="label" x="175" y="185">I</text>
                    </g>
                    <g class="number-circle green" id="F">
                        <circle cx="250" cy="160" r="20"/>
                        <text x="250" y="165">1</text>
                        <text class="label" x="250" y="185">F</text>
                    </g>
                    <g class="number-circle green" id="J">
                        <circle cx="350" cy="160" r="20"/>
                        <text x="350" y="165">1</text>
                        <text class="label" x="350" y="185">J</text>
                    </g>

                    <g class="number-circle purple" id="A">
                        <circle cx="50" cy="280" r="20"/>
                        <text x="50" y="285">11</text>
                        <text class="label" x="50" y="305">A</text>
                    </g>
                    <g class="number-circle purple-center" id="B">
                        <circle cx="200" cy="280" r="25"/>
                        <text x="200" y="285">5</text>
                        <text class="label" x="200" y="310">B</text>
                    </g>
                    <g class="number-circle purple" id="C">
                        <circle cx="300" cy="280" r="20"/>
                        <text x="300" y="285">5</text>
                        <text class="label" x="300" y="305">C</text>
                    </g>
                    <g class="number-circle purple" id="D">
                        <circle cx="375" cy="280" r="20"/>
                        <text x="375" y="285">3</text>
                        <text class="label" x="375" y="305">D</text>
                    </g>

                    <g class="number-circle red" id="K">
                        <circle cx="125" cy="400" r="20"/>
                        <text x="125" y="405">3</text>
                        <text class="label" x="125" y="425">K</text>
                    </g>
                    <g class="number-circle red" id="O">
                        <circle cx="200" cy="400" r="20"/>
                        <text x="200" y="405">6</text>
                        <text class="label" x="200" y="425">O</text>
                    </g>
                    <g class="number-circle red" id="L">
                        <circle cx="275" cy="400" r="20"/>
                        <text x="275" y="405">0</text>
                        <text class="label" x="275" y="425">L</text>
                    </g>
                    <g class="number-circle red" id="M">
                        <circle cx="200" cy="350" r="20"/>
                        <text x="200" y="355">3</text>
                        <text class="label" x="200" y="375">M</text>
                    </g>
                    <g class="number-circle red" id="N">
                        <circle cx="200" cy="420" r="20"/>
                        <text x="200" y="425">3</text>
                        <text class="label" x="200" y="445">N</text>
                    </g>
                    <g class="number-circle red" id="P">
                        <circle cx="130" cy="420" r="20"/>
                        <text x="130" y="425">9</text>
                        <text class="label" x="130" y="445">P</text>
                    </g>
                    <g class="number-circle red" id="Q">
                        <circle cx="100" cy="490" r="20"/>
                        <text x="100" y="495">6</text>
                        <text class="label" x="100" y="515">Q</text>
                    </g>
                    <g class="number-circle red" id="R">
                        <circle cx="170" cy="490" r="20"/>
                        <text x="170" y="495">3</text>
                        <text class="label" x="170" y="515">R</text>
                    </g>
                    <g class="number-circle red" id="S">
                        <circle cx="240" cy="490" r="20"/>
                        <text x="240" y="495">9</text>
                        <text class="label" x="240" y="515">S</text>
                    </g>
                    <g class="number-circle red" id="W">
                        <circle cx="60" cy="350" r="20"/>
                        <text class="label" x="60" y="375">W</text>
                    </g>
                </svg>
            <div class="ausencias-box">
                <div class="ausencias-number">24</div>
                <div class="ausencias-label">Ausencias</div>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
    <script src="draggable.js"></script>
</body>
</html>