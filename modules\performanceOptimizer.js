/**
 * Performance Optimizer Module
 * Implements advanced performance optimizations for the Image Recreation App
 */

class PerformanceOptimizer {
    constructor() {
        this.config = {
            // Virtual rendering settings
            virtualCanvas: {
                enabled: true,
                viewportBuffer: 100, // pixels outside viewport to render
                maxVisibleElements: 1000,
                renderBatchSize: 50
            },
            
            // Memory management
            memory: {
                maxPoolSize: 500,
                gcThreshold: 0.8, // trigger cleanup at 80% memory usage
                elementCacheSize: 200
            },
            
            // Performance monitoring
            monitoring: {
                enabled: true,
                fpsTarget: 60,
                memoryCheckInterval: 5000,
                performanceLogInterval: 10000
            },
            
            // Debouncing settings
            debouncing: {
                stateUpdate: 16, // ~60fps
                rendering: 8, // ~120fps for smooth animations
                userInput: 100,
                resize: 250
            }
        };

        this.state = {
            isOptimized: false,
            currentFPS: 0,
            memoryUsage: 0,
            visibleElements: 0,
            renderQueue: [],
            lastRenderTime: 0
        };

        this.pools = {
            elements: new Map(),
            canvases: [],
            contexts: []
        };

        this.workers = {
            imageProcessor: null,
            analyzer: null,
            renderer: null
        };

        this.viewport = {
            x: 0,
            y: 0,
            width: 0,
            height: 0,
            scale: 1
        };

        this.debouncedFunctions = new Map();
        this.performanceMetrics = {
            frameCount: 0,
            lastFrameTime: performance.now(),
            renderTimes: [],
            memorySnapshots: []
        };

        this.initialize();
    }

    async initialize() {
        console.log('🚀 Initializing Performance Optimizer...');
        
        try {
            await this.setupWebWorkers();
            this.setupElementPooling();
            this.setupVirtualCanvas();
            this.setupPerformanceMonitoring();
            this.setupDebouncedFunctions();
            
            this.state.isOptimized = true;
            console.log('✅ Performance Optimizer initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Performance Optimizer:', error);
            this.state.isOptimized = false;
        }
    }

    // ==================== WEB WORKERS ====================

    async setupWebWorkers() {
        try {
            // Image Processing Worker
            this.workers.imageProcessor = new Worker(
                URL.createObjectURL(new Blob([this.getImageProcessorWorkerCode()], 
                { type: 'application/javascript' }))
            );

            // Analysis Worker
            this.workers.analyzer = new Worker(
                URL.createObjectURL(new Blob([this.getAnalysisWorkerCode()], 
                { type: 'application/javascript' }))
            );

            // Rendering Worker (for offscreen canvas)
            this.workers.renderer = new Worker(
                URL.createObjectURL(new Blob([this.getRenderWorkerCode()], 
                { type: 'application/javascript' }))
            );

            // Setup worker message handlers
            this.setupWorkerHandlers();
            
            console.log('✅ Web Workers initialized');
        } catch (error) {
            console.warn('⚠️ Web Workers not available, falling back to main thread');
        }
    }

    getImageProcessorWorkerCode() {
        return `
            self.onmessage = function(e) {
                const { type, data, id } = e.data;
                
                switch(type) {
                    case 'processImage':
                        processImageData(data, id);
                        break;
                    case 'resizeImage':
                        resizeImageData(data, id);
                        break;
                    case 'filterImage':
                        filterImageData(data, id);
                        break;
                }
            };

            function processImageData(imageData, id) {
                // Simulate heavy image processing
                const processed = {
                    ...imageData,
                    processed: true,
                    timestamp: Date.now()
                };
                
                self.postMessage({
                    type: 'imageProcessed',
                    data: processed,
                    id: id
                });
            }

            function resizeImageData(data, id) {
                // Image resizing logic
                const { imageData, width, height } = data;
                
                // Simulate resize operation
                const resized = {
                    width: width,
                    height: height,
                    data: imageData,
                    resized: true
                };
                
                self.postMessage({
                    type: 'imageResized',
                    data: resized,
                    id: id
                });
            }

            function filterImageData(data, id) {
                // Image filtering logic
                const { imageData, filter } = data;
                
                // Apply filter
                const filtered = {
                    ...imageData,
                    filter: filter,
                    filtered: true
                };
                
                self.postMessage({
                    type: 'imageFiltered',
                    data: filtered,
                    id: id
                });
            }
        `;
    }

    getAnalysisWorkerCode() {
        return `
            self.onmessage = function(e) {
                const { type, data, id } = e.data;
                
                switch(type) {
                    case 'analyzeElements':
                        analyzeElements(data, id);
                        break;
                    case 'detectPatterns':
                        detectPatterns(data, id);
                        break;
                    case 'calculateRelationships':
                        calculateRelationships(data, id);
                        break;
                }
            };

            function analyzeElements(elements, id) {
                // Heavy analysis computation
                const analyzed = elements.map(element => ({
                    ...element,
                    analyzed: true,
                    complexity: Math.random(),
                    confidence: Math.random()
                }));
                
                self.postMessage({
                    type: 'elementsAnalyzed',
                    data: analyzed,
                    id: id
                });
            }

            function detectPatterns(data, id) {
                // Pattern detection logic
                const patterns = [];
                
                // Simulate pattern detection
                for (let i = 0; i < data.elements.length; i++) {
                    if (Math.random() > 0.7) {
                        patterns.push({
                            type: 'pattern',
                            elements: [i],
                            confidence: Math.random()
                        });
                    }
                }
                
                self.postMessage({
                    type: 'patternsDetected',
                    data: patterns,
                    id: id
                });
            }

            function calculateRelationships(elements, id) {
                // Relationship calculation
                const relationships = [];
                
                for (let i = 0; i < elements.length; i++) {
                    for (let j = i + 1; j < elements.length; j++) {
                        const distance = Math.sqrt(
                            Math.pow(elements[i].x - elements[j].x, 2) +
                            Math.pow(elements[i].y - elements[j].y, 2)
                        );
                        
                        if (distance < 100) {
                            relationships.push({
                                from: i,
                                to: j,
                                distance: distance,
                                type: 'proximity'
                            });
                        }
                    }
                }
                
                self.postMessage({
                    type: 'relationshipsCalculated',
                    data: relationships,
                    id: id
                });
            }
        `;
    }

    getRenderWorkerCode() {
        return `
            self.onmessage = function(e) {
                const { type, data, id } = e.data;
                
                switch(type) {
                    case 'renderElements':
                        renderElements(data, id);
                        break;
                    case 'generateThumbnail':
                        generateThumbnail(data, id);
                        break;
                }
            };

            function renderElements(data, id) {
                // Offscreen rendering logic
                const { elements, viewport } = data;
                
                // Simulate rendering
                const rendered = {
                    elements: elements.length,
                    viewport: viewport,
                    renderTime: Date.now()
                };
                
                self.postMessage({
                    type: 'elementsRendered',
                    data: rendered,
                    id: id
                });
            }

            function generateThumbnail(data, id) {
                // Thumbnail generation
                const { imageData, size } = data;
                
                const thumbnail = {
                    width: size.width,
                    height: size.height,
                    data: imageData,
                    generated: true
                };
                
                self.postMessage({
                    type: 'thumbnailGenerated',
                    data: thumbnail,
                    id: id
                });
            }
        `;
    }

    setupWorkerHandlers() {
        // Image processor handlers
        if (this.workers.imageProcessor) {
            this.workers.imageProcessor.onmessage = (e) => {
                this.handleWorkerMessage('imageProcessor', e.data);
            };
        }

        // Analyzer handlers
        if (this.workers.analyzer) {
            this.workers.analyzer.onmessage = (e) => {
                this.handleWorkerMessage('analyzer', e.data);
            };
        }

        // Renderer handlers
        if (this.workers.renderer) {
            this.workers.renderer.onmessage = (e) => {
                this.handleWorkerMessage('renderer', e.data);
            };
        }
    }

    handleWorkerMessage(workerType, message) {
        const { type, data, id } = message;
        
        // Emit custom event for worker completion
        const event = new CustomEvent('workerComplete', {
            detail: {
                workerType,
                messageType: type,
                data,
                id
            }
        });
        
        document.dispatchEvent(event);
    }

    // ==================== ELEMENT POOLING ====================

    setupElementPooling() {
        // Initialize pools for different element types
        const elementTypes = ['circle', 'rectangle', 'line', 'text', 'group'];
        
        elementTypes.forEach(type => {
            this.pools.elements.set(type, []);
        });

        // Pre-populate pools
        this.prePopulatePools();
        
        console.log('✅ Element pooling initialized');
    }

    prePopulatePools() {
        const elementTypes = ['circle', 'rectangle', 'line', 'text'];
        
        elementTypes.forEach(type => {
            const pool = [];
            for (let i = 0; i < 50; i++) {
                pool.push(this.createPooledElement(type));
            }
            this.pools.elements.set(type, pool);
        });
    }

    createPooledElement(type) {
        const element = document.createElementNS('http://www.w3.org/2000/svg', type === 'rectangle' ? 'rect' : type);
        element.style.display = 'none';
        element.setAttribute('data-pooled', 'true');
        return element;
    }

    getPooledElement(type) {
        const pool = this.pools.elements.get(type);
        
        if (pool && pool.length > 0) {
            const element = pool.pop();
            element.style.display = '';
            element.removeAttribute('data-pooled');
            return element;
        }
        
        // Create new element if pool is empty
        return this.createPooledElement(type);
    }

    returnToPool(element, type) {
        if (!element) return;
        
        // Reset element properties
        this.resetElement(element);
        
        // Return to pool
        const pool = this.pools.elements.get(type);
        if (pool && pool.length < this.config.memory.maxPoolSize) {
            element.style.display = 'none';
            element.setAttribute('data-pooled', 'true');
            pool.push(element);
        } else {
            // Remove if pool is full
            element.remove();
        }
    }

    resetElement(element) {
        // Reset common attributes
        const attributes = ['x', 'y', 'width', 'height', 'cx', 'cy', 'r', 'x1', 'y1', 'x2', 'y2'];
        attributes.forEach(attr => {
            if (element.hasAttribute(attr)) {
                element.removeAttribute(attr);
            }
        });
        
        // Reset styles
        element.style.cssText = '';
        element.className = '';
    }

    // ==================== VIRTUAL CANVAS ====================

    setupVirtualCanvas() {
        this.virtualCanvas = {
            enabled: this.config.virtualCanvas.enabled,
            visibleElements: new Set(),
            renderQueue: [],
            lastViewport: { ...this.viewport }
        };
        
        // Setup viewport change detection
        this.setupViewportTracking();
        
        console.log('✅ Virtual canvas initialized');
    }

    setupViewportTracking() {
        // Track viewport changes
        const trackViewport = this.debounce(() => {
            this.updateVisibleElements();
        }, this.config.debouncing.rendering);

        // Listen for scroll and zoom events
        document.addEventListener('scroll', trackViewport);
        document.addEventListener('wheel', trackViewport);
        window.addEventListener('resize', trackViewport);
    }

    updateViewport(x, y, width, height, scale = 1) {
        this.viewport = { x, y, width, height, scale };
        
        if (this.virtualCanvas.enabled) {
            this.updateVisibleElements();
        }
    }

    updateVisibleElements() {
        if (!this.virtualCanvas.enabled) return;

        const { x, y, width, height } = this.viewport;
        const buffer = this.config.virtualCanvas.viewportBuffer;
        
        // Calculate extended viewport with buffer
        const extendedViewport = {
            left: x - buffer,
            top: y - buffer,
            right: x + width + buffer,
            bottom: y + height + buffer
        };

        // Find elements in viewport
        const allElements = document.querySelectorAll('.interactive-element');
        const newVisibleElements = new Set();

        allElements.forEach(element => {
            if (this.isElementInViewport(element, extendedViewport)) {
                newVisibleElements.add(element);
                this.showElement(element);
            } else {
                this.hideElement(element);
            }
        });

        this.virtualCanvas.visibleElements = newVisibleElements;
        this.state.visibleElements = newVisibleElements.size;

        // Trigger render if needed
        if (this.shouldRender()) {
            this.requestRender();
        }
    }

    isElementInViewport(element, viewport) {
        const rect = element.getBoundingClientRect();
        
        return !(
            rect.right < viewport.left ||
            rect.left > viewport.right ||
            rect.bottom < viewport.top ||
            rect.top > viewport.bottom
        );
    }

    showElement(element) {
        if (element.style.display === 'none') {
            element.style.display = '';
        }
    }

    hideElement(element) {
        if (element.style.display !== 'none') {
            element.style.display = 'none';
        }
    }

    shouldRender() {
        const now = performance.now();
        const timeSinceLastRender = now - this.state.lastRenderTime;
        
        return timeSinceLastRender >= this.config.debouncing.rendering;
    }

    requestRender() {
        if (this.renderRequestId) {
            cancelAnimationFrame(this.renderRequestId);
        }
        
        this.renderRequestId = requestAnimationFrame(() => {
            this.performRender();
        });
    }

    performRender() {
        const startTime = performance.now();
        
        // Batch render visible elements
        const visibleElements = Array.from(this.virtualCanvas.visibleElements);
        const batchSize = this.config.virtualCanvas.renderBatchSize;
        
        for (let i = 0; i < visibleElements.length; i += batchSize) {
            const batch = visibleElements.slice(i, i + batchSize);
            this.renderBatch(batch);
        }
        
        const renderTime = performance.now() - startTime;
        this.updatePerformanceMetrics(renderTime);
        
        this.state.lastRenderTime = performance.now();
    }

    renderBatch(elements) {
        // Render a batch of elements efficiently
        elements.forEach(element => {
            if (element.hasAttribute('data-needs-update')) {
                this.updateElementRender(element);
                element.removeAttribute('data-needs-update');
            }
        });
    }

    updateElementRender(element) {
        // Update element rendering
        const elementType = element.getAttribute('data-element-type');
        
        switch (elementType) {
            case 'circle':
                this.updateCircleRender(element);
                break;
            case 'rectangle':
                this.updateRectangleRender(element);
                break;
            case 'line':
                this.updateLineRender(element);
                break;
            case 'text':
                this.updateTextRender(element);
                break;
        }
    }

    updateCircleRender(element) {
        // Optimized circle rendering
        const cx = element.getAttribute('cx') || 0;
        const cy = element.getAttribute('cy') || 0;
        const r = element.getAttribute('r') || 10;
        
        // Apply transforms efficiently
        element.style.transform = `translate(${cx}px, ${cy}px)`;
    }

    updateRectangleRender(element) {
        // Optimized rectangle rendering
        const x = element.getAttribute('x') || 0;
        const y = element.getAttribute('y') || 0;
        const width = element.getAttribute('width') || 50;
        const height = element.getAttribute('height') || 50;
        
        element.style.transform = `translate(${x}px, ${y}px)`;
        element.style.width = width + 'px';
        element.style.height = height + 'px';
    }

    updateLineRender(element) {
        // Optimized line rendering
        const x1 = element.getAttribute('x1') || 0;
        const y1 = element.getAttribute('y1') || 0;
        const x2 = element.getAttribute('x2') || 50;
        const y2 = element.getAttribute('y2') || 50;
        
        const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
        const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
        
        element.style.transform = `translate(${x1}px, ${y1}px) rotate(${angle}deg)`;
        element.style.width = length + 'px';
    }

    updateTextRender(element) {
        // Optimized text rendering
        const x = element.getAttribute('x') || 0;
        const y = element.getAttribute('y') || 0;
        
        element.style.transform = `translate(${x}px, ${y}px)`;
    }

    // ==================== PERFORMANCE MONITORING ====================

    setupPerformanceMonitoring() {
        if (!this.config.monitoring.enabled) return;

        // FPS monitoring
        this.startFPSMonitoring();
        
        // Memory monitoring
        this.startMemoryMonitoring();
        
        // Performance logging
        this.startPerformanceLogging();
        
        console.log('✅ Performance monitoring initialized');
    }

    startFPSMonitoring() {
        let lastTime = performance.now();
        let frameCount = 0;
        
        const measureFPS = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                this.state.currentFPS = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;
                
                // Emit FPS update event
                this.emitPerformanceEvent('fpsUpdate', { fps: this.state.currentFPS });
            }
            
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
    }

    startMemoryMonitoring() {
        setInterval(() => {
            if (performance.memory) {
                const memory = performance.memory;
                this.state.memoryUsage = {
                    used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
                };
                
                // Check if memory cleanup is needed
                const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
                if (usageRatio > this.config.memory.gcThreshold) {
                    this.performMemoryCleanup();
                }
                
                this.emitPerformanceEvent('memoryUpdate', this.state.memoryUsage);
            }
        }, this.config.monitoring.memoryCheckInterval);
    }

    startPerformanceLogging() {
        setInterval(() => {
            const metrics = {
                fps: this.state.currentFPS,
                memory: this.state.memoryUsage,
                visibleElements: this.state.visibleElements,
                renderQueue: this.state.renderQueue.length,
                timestamp: Date.now()
            };
            
            this.performanceMetrics.memorySnapshots.push(metrics);
            
            // Keep only last 100 snapshots
            if (this.performanceMetrics.memorySnapshots.length > 100) {
                this.performanceMetrics.memorySnapshots.shift();
            }
            
            this.emitPerformanceEvent('performanceLog', metrics);
        }, this.config.monitoring.performanceLogInterval);
    }

    updatePerformanceMetrics(renderTime) {
        this.performanceMetrics.renderTimes.push(renderTime);
        
        // Keep only last 100 render times
        if (this.performanceMetrics.renderTimes.length > 100) {
            this.performanceMetrics.renderTimes.shift();
        }
        
        // Calculate average render time
        const avgRenderTime = this.performanceMetrics.renderTimes.reduce((a, b) => a + b, 0) / 
                             this.performanceMetrics.renderTimes.length;
        
        this.emitPerformanceEvent('renderMetrics', {
            currentRenderTime: renderTime,
            averageRenderTime: avgRenderTime,
            renderCount: this.performanceMetrics.renderTimes.length
        });
    }

    emitPerformanceEvent(type, data) {
        const event = new CustomEvent('performanceUpdate', {
            detail: { type, data }
        });
        document.dispatchEvent(event);
    }

    // ==================== MEMORY MANAGEMENT ====================

    performMemoryCleanup() {
        console.log('🧹 Performing memory cleanup...');
        
        // Clean up unused pooled elements
        this.cleanupElementPools();
        
        // Clean up render queue
        this.cleanupRenderQueue();
        
        // Clean up performance metrics
        this.cleanupPerformanceMetrics();
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
        
        console.log('✅ Memory cleanup completed');
    }

    cleanupElementPools() {
        this.pools.elements.forEach((pool, type) => {
            if (pool.length > this.config.memory.maxPoolSize / 2) {
                const excess = pool.length - Math.floor(this.config.memory.maxPoolSize / 2);
                const removed = pool.splice(0, excess);
                removed.forEach(element => element.remove());
            }
        });
    }

    cleanupRenderQueue() {
        // Remove old render requests
        this.state.renderQueue = this.state.renderQueue.filter(request => {
            const age = Date.now() - request.timestamp;
            return age < 5000; // Keep requests younger than 5 seconds
        });
    }

    cleanupPerformanceMetrics() {
        // Keep only recent metrics
        this.performanceMetrics.renderTimes = this.performanceMetrics.renderTimes.slice(-50);
        this.performanceMetrics.memorySnapshots = this.performanceMetrics.memorySnapshots.slice(-50);
    }

    // ==================== DEBOUNCED FUNCTIONS ====================

    setupDebouncedFunctions() {
        // Pre-create commonly used debounced functions
        this.debouncedFunctions.set('stateUpdate', 
            this.debounce(() => {}, this.config.debouncing.stateUpdate));
        
        this.debouncedFunctions.set('userInput', 
            this.debounce(() => {}, this.config.debouncing.userInput));
        
        this.debouncedFunctions.set('resize', 
            this.debounce(() => {}, this.config.debouncing.resize));
        
        console.log('✅ Debounced functions initialized');
    }

    debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(this, args);
        };
    }

    getDebouncedFunction(key, func, wait) {
        if (!this.debouncedFunctions.has(key)) {
            this.debouncedFunctions.set(key, this.debounce(func, wait));
        }
        return this.debouncedFunctions.get(key);
    }

    // ==================== PUBLIC API ====================

    // Process image in background worker
    async processImageAsync(imageData, options = {}) {
        if (!this.workers.imageProcessor) {
            throw new Error('Image processor worker not available');
        }

        const id = this.generateId();
        
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Image processing timeout'));
            }, 30000);

            const handler = (event) => {
                if (event.detail.id === id) {
                    clearTimeout(timeout);
                    document.removeEventListener('workerComplete', handler);
                    resolve(event.detail.data);
                }
            };

            document.addEventListener('workerComplete', handler);
            
            this.workers.imageProcessor.postMessage({
                type: 'processImage',
                data: { imageData, options },
                id
            });
        });
    }

    // Analyze elements in background
    async analyzeElementsAsync(elements) {
        if (!this.workers.analyzer) {
            throw new Error('Analyzer worker not available');
        }

        const id = this.generateId();
        
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Analysis timeout'));
            }, 30000);

            const handler = (event) => {
                if (event.detail.id === id) {
                    clearTimeout(timeout);
                    document.removeEventListener('workerComplete', handler);
                    resolve(event.detail.data);
                }
            };

            document.addEventListener('workerComplete', handler);
            
            this.workers.analyzer.postMessage({
                type: 'analyzeElements',
                data: elements,
                id
            });
        });
    }

    // Optimized element creation
    createOptimizedElement(type, properties = {}) {
        const element = this.getPooledElement(type);
        
        // Apply properties efficiently
        Object.entries(properties).forEach(([key, value]) => {
            if (key.startsWith('data-')) {
                element.setAttribute(key, value);
            } else if (key === 'style') {
                Object.assign(element.style, value);
            } else {
                element.setAttribute(key, value);
            }
        });
        
        return element;
    }

    // Optimized element removal
    removeOptimizedElement(element) {
        const type = element.getAttribute('data-element-type') || element.tagName.toLowerCase();
        this.returnToPool(element, type);
    }

    // Batch operations
    batchUpdate(operations) {
        const debouncedBatch = this.getDebouncedFunction(
            'batchUpdate',
            () => this.executeBatchOperations(operations),
            this.config.debouncing.stateUpdate
        );
        
        debouncedBatch();
    }

    executeBatchOperations(operations) {
        operations.forEach(operation => {
            try {
                operation();
            } catch (error) {
                console.error('Batch operation failed:', error);
            }
        });
    }

    // Performance metrics
    getPerformanceMetrics() {
        return {
            ...this.state,
            averageRenderTime: this.performanceMetrics.renderTimes.length > 0 ?
                this.performanceMetrics.renderTimes.reduce((a, b) => a + b, 0) / 
                this.performanceMetrics.renderTimes.length : 0,
            memorySnapshots: this.performanceMetrics.memorySnapshots.slice(-10)
        };
    }

    // Configuration
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ Performance configuration updated');
    }

    // Utility methods
    generateId() {
        return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Cleanup
    destroy() {
        // Terminate workers
        Object.values(this.workers).forEach(worker => {
            if (worker) worker.terminate();
        });

        // Clear pools
        this.pools.elements.forEach(pool => {
            pool.forEach(element => element.remove());
        });

        // Clear debounced functions
        this.debouncedFunctions.clear();

        // Cancel animation frames
        if (this.renderRequestId) {
            cancelAnimationFrame(this.renderRequestId);
        }

        console.log('🧹 Performance Optimizer destroyed');
    }
}

export default PerformanceOptimizer;