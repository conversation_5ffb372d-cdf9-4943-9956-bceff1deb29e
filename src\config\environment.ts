/**
 * Environment Configuration
 * Securely loads and validates environment variables for AI services
 */

export interface EnvironmentConfig {
  openrouter: {
    apiKey: string;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
    maxRetries: number;
    rateLimitRequests: number;
    rateLimitWindow: number;
  };
  ai: {
    enabled: boolean;
    confidenceThreshold: number;
    fallbackToLocal: boolean;
    rateLimitRequests: number;
    rateLimitWindow: number;
    timeout: number;
    maxRetries: number;
    enableLogging: boolean;
  };
}

/**
 * Load and validate environment configuration
 */
export function loadEnvironmentConfig(): EnvironmentConfig {
  // Check if we're in a browser environment
  const isBrowser = typeof window !== 'undefined';
  
  // In browser, use fallback values or throw error for missing required config
  const getEnvVar = (key: string, defaultValue?: string): string => {
    if (isBrowser) {
      // In browser, sensitive values should be passed through build process or runtime config
      // For development, we'll use the provided values
      const envVars: Record<string, string> = {
        'OPENROUTER_API_KEY': '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',
        'OPENROUTER_BASE_URL': 'https://openrouter.ai/api/v1',
        'OPENROUTER_MODEL': 'horizon-beta',
        'OPENROUTER_MAX_TOKENS': '4096',
        'OPENROUTER_TEMPERATURE': '0.7',
        'OPENROUTER_TIMEOUT': '30000',
        'OPENROUTER_MAX_RETRIES': '3',
        'OPENROUTER_RATE_LIMIT_REQUESTS': '100',
        'OPENROUTER_RATE_LIMIT_WINDOW': '3600000',
        'AI_SERVICE_ENABLED': 'true',
        'AI_CONFIDENCE_THRESHOLD': '0.7',
        'AI_FALLBACK_TO_LOCAL': 'true',
        'AI_RATE_LIMIT_REQUESTS': '100',
        'AI_RATE_LIMIT_WINDOW': '3600000',
        'AI_SERVICE_TIMEOUT': '30000',
        'AI_MAX_RETRIES': '3',
        'AI_ENABLE_LOGGING': 'true'
      };
      return envVars[key] || defaultValue || '';
    }
    
    // In Node.js environment, use process.env
    return process.env[key] || defaultValue || '';
  };

  // Validate required environment variables
  const apiKey = getEnvVar('OPENROUTER_API_KEY');
  if (!apiKey) {
    throw new Error('OPENROUTER_API_KEY environment variable is required');
  }

  const baseUrl = getEnvVar('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1');
  const model = getEnvVar('OPENROUTER_MODEL', 'horizon-beta');

  // Parse boolean and numeric values
  const parseBoolean = (value: string, defaultValue: boolean): boolean => {
    if (!value) return defaultValue;
    return value.toLowerCase() === 'true';
  };

  const parseNumber = (value: string, defaultValue: number): number => {
    if (!value) return defaultValue;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  };

  const parseFloatValue = (value: string, defaultValue: number): number => {
    if (!value) return defaultValue;
    const parsed = parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
  };

  return {
    openrouter: {
      apiKey,
      baseUrl,
      model,
      maxTokens: parseNumber(getEnvVar('OPENROUTER_MAX_TOKENS'), 4096),
      temperature: parseFloatValue(getEnvVar('OPENROUTER_TEMPERATURE'), 0.7),
      timeout: parseNumber(getEnvVar('OPENROUTER_TIMEOUT'), 30000),
      maxRetries: parseNumber(getEnvVar('OPENROUTER_MAX_RETRIES'), 3),
      rateLimitRequests: parseNumber(getEnvVar('OPENROUTER_RATE_LIMIT_REQUESTS'), 100),
      rateLimitWindow: parseNumber(getEnvVar('OPENROUTER_RATE_LIMIT_WINDOW'), 3600000)
    },
    ai: {
      enabled: parseBoolean(getEnvVar('AI_SERVICE_ENABLED'), true),
      confidenceThreshold: parseFloatValue(getEnvVar('AI_CONFIDENCE_THRESHOLD'), 0.7),
      fallbackToLocal: parseBoolean(getEnvVar('AI_FALLBACK_TO_LOCAL'), true),
      rateLimitRequests: parseNumber(getEnvVar('AI_RATE_LIMIT_REQUESTS'), 100),
      rateLimitWindow: parseNumber(getEnvVar('AI_RATE_LIMIT_WINDOW'), 3600000),
      timeout: parseNumber(getEnvVar('AI_SERVICE_TIMEOUT'), 30000),
      maxRetries: parseNumber(getEnvVar('AI_MAX_RETRIES'), 3),
      enableLogging: parseBoolean(getEnvVar('AI_ENABLE_LOGGING'), true)
    }
  };
}

/**
 * Get environment configuration singleton
 */
let environmentConfig: EnvironmentConfig | null = null;

export function getEnvironmentConfig(): EnvironmentConfig {
  if (!environmentConfig) {
    environmentConfig = loadEnvironmentConfig();
  }
  return environmentConfig;
}

/**
 * Validate API key format
 */
export function validateApiKey(apiKey: string): boolean {
  // OpenRouter API keys start with 'sk-or-v1-'
  return apiKey.startsWith('sk-or-v1-') && apiKey.length > 20;
}

/**
 * Mask sensitive information for logging
 */
export function maskSensitiveData(data: any): any {
  if (typeof data === 'string') {
    // Mask API keys
    if (data.startsWith('sk-or-v1-')) {
      return data.substring(0, 12) + '***' + data.substring(data.length - 4);
    }
    return data;
  }
  
  if (typeof data === 'object' && data !== null) {
    const masked = { ...data };
    for (const key in masked) {
      if (key.toLowerCase().includes('key') || key.toLowerCase().includes('token')) {
        masked[key] = maskSensitiveData(masked[key]);
      } else {
        masked[key] = maskSensitiveData(masked[key]);
      }
    }
    return masked;
  }
  
  return data;
}

/**
 * Export environment configuration for external use
 */
export const env = getEnvironmentConfig();