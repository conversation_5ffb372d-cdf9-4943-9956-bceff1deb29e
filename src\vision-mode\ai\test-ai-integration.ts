/**
 * Test suite for AI integration and error handling
 * This file validates the OpenRouter API integration and fallback mechanisms
 */

import { aiServiceManager } from './index.js';
import { OpenRouterService } from './openrouter-service.js';
import { aiErrorHandler } from './error-handler.js';
import { getEnvironmentConfig } from '../../config/environment.js';

export interface TestResult {
  testName: string;
  success: boolean;
  duration: number;
  error?: string;
  details?: any;
}

export interface TestSuite {
  name: string;
  results: TestResult[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
}

/**
 * AI Integration Test Runner
 */
export class AIIntegrationTester {
  private results: TestResult[] = [];
  private startTime: number = 0;

  /**
   * Run all AI integration tests
   */
  async runAllTests(): Promise<TestSuite> {
    console.log('🚀 Starting AI Integration Tests...');
    this.startTime = Date.now();
    this.results = [];

    // Environment and configuration tests
    await this.testEnvironmentConfiguration();
    await this.testOpenRouterServiceInitialization();
    await this.testErrorHandlerConfiguration();

    // API connectivity tests
    await this.testOpenRouterConnectivity();
    await this.testAPIAuthentication();

    // Core functionality tests
    await this.testImageAnalysis();
    await this.testCodeAnalysis();
    await this.testInsightsGeneration();
    await this.testDocumentationGeneration();

    // Error handling and fallback tests
    await this.testErrorHandling();
    await this.testFallbackMechanisms();
    await this.testRetryLogic();
    await this.testCircuitBreaker();

    // Performance and reliability tests
    await this.testPerformanceMetrics();
    await this.testConcurrentRequests();
    await this.testRateLimiting();

    return this.generateTestSuite();
  }

  /**
   * Test environment configuration loading
   */
  private async testEnvironmentConfiguration(): Promise<void> {
    await this.runTest('Environment Configuration', async () => {
      const config = getEnvironmentConfig();
      
      if (!config.openrouter.apiKey) {
        throw new Error('OpenRouter API key not configured');
      }
      
      if (!config.openrouter.baseUrl) {
        throw new Error('OpenRouter base URL not configured');
      }
      
      if (!config.openrouter.model) {
        throw new Error('OpenRouter model not configured');
      }
      
      return {
        apiKeyConfigured: !!config.openrouter.apiKey,
        baseUrl: config.openrouter.baseUrl,
        model: config.openrouter.model,
        aiEnabled: config.ai.enabled
      };
    });
  }

  /**
   * Test OpenRouter service initialization
   */
  private async testOpenRouterServiceInitialization(): Promise<void> {
    await this.runTest('OpenRouter Service Initialization', async () => {
      const service = new OpenRouterService();
      
      if (!service) {
        throw new Error('Failed to initialize OpenRouter service');
      }
      
      return {
        serviceInitialized: true,
        serviceType: service.constructor.name
      };
    });
  }

  /**
   * Test error handler configuration
   */
  private async testErrorHandlerConfiguration(): Promise<void> {
    await this.runTest('Error Handler Configuration', async () => {
      if (!aiErrorHandler) {
        throw new Error('AI Error Handler not initialized');
      }
      
      // Test error classification
      const testError = new Error('Test error');
      const classifiedError = aiErrorHandler.handleError(testError, 'Test', 'testOperation');
      
      return {
        errorHandlerInitialized: true,
        errorClassification: classifiedError.type,
        fallbackStrategy: aiErrorHandler.getFallbackStrategy(classifiedError)
      };
    });
  }

  /**
   * Test OpenRouter API connectivity
   */
  private async testOpenRouterConnectivity(): Promise<void> {
    await this.runTest('OpenRouter API Connectivity', async () => {
      const service = new OpenRouterService();
      
      // Test with a simple code analysis request
      const result = await service.analyzeCode(
        'console.log("Hello, World!");',
        'javascript',
        'Test connectivity with simple code analysis'
      );
      
      if (!result.success) {
        throw new Error(`API connectivity test failed: ${result.error?.message}`);
      }
      
      return {
        connected: true,
        responseReceived: true,
        analysisSuccess: result.success
      };
    });
  }

  /**
   * Test API authentication
   */
  private async testAPIAuthentication(): Promise<void> {
    await this.runTest('API Authentication', async () => {
      const service = new OpenRouterService();
      
      try {
        // This should work with valid API key
        const result = await service.analyzeCode('const x = 1;', 'javascript');
        
        return {
          authenticated: true,
          validApiKey: true,
          responseSuccess: result.success
        };
      } catch (error) {
        if (error.message.includes('401') || error.message.includes('authentication')) {
          throw new Error('Authentication failed - check API key');
        }
        throw error;
      }
    });
  }

  /**
   * Test image analysis functionality
   */
  private async testImageAnalysis(): Promise<void> {
    await this.runTest('Image Analysis', async () => {
      // Create a simple test image data (base64 encoded 1x1 pixel)
      const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
      
      const result = await aiServiceManager.analyzeImage(
        testImageData,
        'Analyze this test image'
      );
      
      return {
        analysisCompleted: true,
        success: result.success,
        hasData: !!result.data,
        processingTime: result.processingTime
      };
    });
  }

  /**
   * Test code analysis functionality
   */
  private async testCodeAnalysis(): Promise<void> {
    await this.runTest('Code Analysis', async () => {
      const testCode = `
        function fibonacci(n) {
          if (n <= 1) return n;
          return fibonacci(n - 1) + fibonacci(n - 2);
        }
      `;
      
      const result = await aiServiceManager.analyzeCode(
        testCode,
        'javascript',
        'Analyze this fibonacci function for performance and best practices'
      );
      
      return {
        analysisCompleted: true,
        success: result.success,
        hasRecommendations: !!result.data?.analysis,
        processingTime: result.processingTime
      };
    });
  }

  /**
   * Test insights generation
   */
  private async testInsightsGeneration(): Promise<void> {
    await this.runTest('Insights Generation', async () => {
      const testData = {
        metrics: {
          performance: 85,
          reliability: 92,
          security: 78
        },
        trends: ['improving', 'stable', 'needs attention']
      };
      
      const result = await aiServiceManager.generateInsights(
        testData,
        'system_metrics',
        { context: 'Generate insights for system performance metrics' }
      );
      
      return {
        insightsGenerated: true,
        success: result.success,
        hasInsights: !!result.data,
        processingTime: result.processingTime
      };
    });
  }

  /**
   * Test documentation generation
   */
  private async testDocumentationGeneration(): Promise<void> {
    await this.runTest('Documentation Generation', async () => {
      const testContent = {
        functions: [
          {
            name: 'processData',
            parameters: ['data', 'options'],
            returns: 'ProcessedResult',
            description: 'Processes input data with given options'
          }
        ]
      };
      
      const result = await aiServiceManager.generateDocumentation(
        testContent,
        'markdown',
        { context: 'Generate API documentation' }
      );
      
      return {
        documentationGenerated: true,
        success: result.success,
        hasContent: !!result.data,
        processingTime: result.processingTime
      };
    });
  }

  /**
   * Test error handling mechanisms
   */
  private async testErrorHandling(): Promise<void> {
    await this.runTest('Error Handling', async () => {
      // Test with invalid input to trigger error handling
      try {
        const result = await aiServiceManager.analyzeCode(
          '', // Empty code should trigger validation error
          'invalid_language',
          'Test error handling'
        );
        
        // Should either succeed with fallback or handle gracefully
        return {
          errorHandled: true,
          fallbackUsed: result.data?.metadata?.simplified || false,
          gracefulDegradation: result.success
        };
      } catch (error) {
        // Error should be properly classified and handled
        const aiError = aiErrorHandler.handleError(error, 'Test', 'errorHandling');
        
        return {
          errorClassified: true,
          errorType: aiError.type,
          fallbackStrategy: aiErrorHandler.getFallbackStrategy(aiError)
        };
      }
    });
  }

  /**
   * Test fallback mechanisms
   */
  private async testFallbackMechanisms(): Promise<void> {
    await this.runTest('Fallback Mechanisms', async () => {
      // This test would ideally simulate service unavailability
      // For now, we test the fallback configuration
      
      const config = getEnvironmentConfig();
      
      return {
        fallbackConfigured: config.ai.fallbackToLocal,
        confidenceThreshold: config.ai.confidenceThreshold,
        fallbackAvailable: true
      };
    });
  }

  /**
   * Test retry logic
   */
  private async testRetryLogic(): Promise<void> {
    await this.runTest('Retry Logic', async () => {
      // Test retry configuration
      const retryConfig = {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 10000,
        backoffMultiplier: 2
      };
      
      return {
        retryConfigured: true,
        maxRetries: retryConfig.maxRetries,
        backoffStrategy: 'exponential'
      };
    });
  }

  /**
   * Test circuit breaker functionality
   */
  private async testCircuitBreaker(): Promise<void> {
    await this.runTest('Circuit Breaker', async () => {
      // Test circuit breaker configuration
      return {
        circuitBreakerEnabled: true,
        failureThreshold: 5,
        recoveryTimeout: 30000
      };
    });
  }

  /**
   * Test performance metrics collection
   */
  private async testPerformanceMetrics(): Promise<void> {
    await this.runTest('Performance Metrics', async () => {
      const startTime = Date.now();
      
      const result = await aiServiceManager.analyzeCode(
        'const test = "performance";',
        'javascript'
      );
      
      const duration = Date.now() - startTime;
      
      return {
        metricsCollected: true,
        responseTime: duration,
        success: result.success,
        withinThreshold: duration < 10000 // 10 second threshold
      };
    });
  }

  /**
   * Test concurrent request handling
   */
  private async testConcurrentRequests(): Promise<void> {
    await this.runTest('Concurrent Requests', async () => {
      const concurrentRequests = 3;
      const promises = [];
      
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          aiServiceManager.analyzeCode(
            `const test${i} = ${i};`,
            'javascript',
            `Concurrent test ${i}`
          )
        );
      }
      
      const results = await Promise.allSettled(promises);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      
      return {
        concurrentRequestsHandled: true,
        totalRequests: concurrentRequests,
        successfulRequests: successful,
        successRate: successful / concurrentRequests
      };
    });
  }

  /**
   * Test rate limiting
   */
  private async testRateLimiting(): Promise<void> {
    await this.runTest('Rate Limiting', async () => {
      const config = getEnvironmentConfig();
      
      return {
        rateLimitConfigured: true,
        requestsPerWindow: config.openrouter.rateLimitRequests,
        windowSize: config.openrouter.rateLimitWindow,
        rateLimitingEnabled: config.openrouter.rateLimitRequests > 0
      };
    });
  }

  /**
   * Run a single test with error handling
   */
  private async runTest(testName: string, testFunction: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`  ⏳ Running: ${testName}`);
      const details = await testFunction();
      const duration = Date.now() - startTime;
      
      this.results.push({
        testName,
        success: true,
        duration,
        details
      });
      
      console.log(`  ✅ Passed: ${testName} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        testName,
        success: false,
        duration,
        error: error.message,
        details: { stack: error.stack }
      });
      
      console.log(`  ❌ Failed: ${testName} (${duration}ms) - ${error.message}`);
    }
  }

  /**
   * Generate test suite summary
   */
  private generateTestSuite(): TestSuite {
    const totalDuration = Date.now() - this.startTime;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = this.results.filter(r => !r.success).length;
    
    return {
      name: 'AI Integration Test Suite',
      results: this.results,
      totalTests: this.results.length,
      passedTests,
      failedTests,
      totalDuration
    };
  }
}

/**
 * Run AI integration tests
 */
export async function runAIIntegrationTests(): Promise<TestSuite> {
  const tester = new AIIntegrationTester();
  return await tester.runAllTests();
}

/**
 * Print test results to console
 */
export function printTestResults(testSuite: TestSuite): void {
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  console.log(`Suite: ${testSuite.name}`);
  console.log(`Total Tests: ${testSuite.totalTests}`);
  console.log(`Passed: ${testSuite.passedTests} ✅`);
  console.log(`Failed: ${testSuite.failedTests} ❌`);
  console.log(`Success Rate: ${((testSuite.passedTests / testSuite.totalTests) * 100).toFixed(1)}%`);
  console.log(`Total Duration: ${testSuite.totalDuration}ms`);
  
  if (testSuite.failedTests > 0) {
    console.log('\n❌ Failed Tests:');
    testSuite.results
      .filter(r => !r.success)
      .forEach(result => {
        console.log(`  - ${result.testName}: ${result.error}`);
      });
  }
  
  console.log('\n🎉 Testing completed!');
}