* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f2f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.pinaculo-container {
    width: 450px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e0e0e0;
}

.header {
    background: #333333;
    color: white;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-weight: bold;
}

.header-icon {
    font-size: 20px;
}

.chart-area {
    padding: 20px;
    position: relative;
}

.pinaculo-chart {
    width: 100%;
    height: auto;
}

.connections line {
    stroke: #4a90e2;
    stroke-width: 1.5;
}

.number-circle circle {
    filter: drop-shadow(2px 3px 3px rgba(0, 0, 0, 0.15));
    transition: all 0.3s ease;
}

.number-circle:hover circle {
    transform: scale(1.05);
}

.number-circle text {
    font-size: 20px;
    font-weight: bold;
    fill: #333;
    text-anchor: middle;
}

.number-circle text.label {
    font-size: 14px;
    font-weight: normal;
    fill: #555;
}

/* Color styles */
.number-circle.green circle {
    fill: #e0f2e0;
    stroke: #a0c8a0;
    stroke-width: 2;
}

.number-circle.purple circle {
    fill: #e6e0f2;
    stroke: #b8a8d8;
    stroke-width: 2;
}

.number-circle.purple-center circle {
    fill: #e6e0f2;
    stroke: #ff9800;
    stroke-width: 3;
}

.number-circle.red circle {
    fill: #f8d7da; /* Lighter red */
    stroke: #f5c6cb; /* Border color */
    stroke-width: 2;
}

.ausencias-box {
    position: absolute;
    bottom: 40px; /* Adjusted position */
    right: 30px;
    width: 90px; /* Adjusted size */
    height: 90px;
    background: #f8d7da; /* Match red circles */
    border: 2px solid #f5c6cb;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ausencias-number {
    font-size: 32px; /* Larger font */
    font-weight: bold;
    color: #333;
}

.ausencias-label {
    font-size: 14px;
    color: #555;
    margin-top: 5px;
}
.ausencias-box {
    position: absolute;
    bottom: 40px; /* Adjusted position */
    right: 30px;
    width: 90px; /* Adjusted size */
    height: 90px;
    background: #f8d7da; /* Match red circles */
    border: 2px solid #f5c6cb;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    pointer-events: none; /* Allow clicks to pass through */
}

.ausencias-number,
.ausencias-label {
    pointer-events: auto; /* Make children clickable again */
}

.ausencias-number {
    font-size: 32px; /* Larger font */
    font-weight: bold;
    color: #333;
}

.ausencias-label {
    font-size: 14px;
    color: #555;
    margin-top: 5px;