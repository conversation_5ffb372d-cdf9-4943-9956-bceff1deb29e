/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
}

/* Navigation */
.main-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-brand h1 {
    color: #667eea;
    font-size: 1.5rem;
    font-weight: 600;
}

.nav-controls {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* Workflow Steps */
.workflow-container {
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem 2rem;
    margin-bottom: 2rem;
}

.workflow-steps {
    display: flex;
    justify-content: center;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.step {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: #f5f5f5;
    transition: all 0.3s ease;
    cursor: pointer;
}

.step.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: scale(1.05);
}

.step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
}

.step.active .step-number {
    background: rgba(255, 255, 255, 0.3);
}

/* Main Layout */
.main-container {
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 2rem;
    padding: 0 2rem;
    min-height: calc(100vh - 200px);
}

/* Panel Styles */
.left-panel, .right-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    height: fit-content;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.center-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
}

/* Panel Sections */
.panel-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.panel-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.panel-section h3 {
    color: #667eea;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #667eea;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(102, 126, 234, 0.05);
}

.upload-area:hover {
    border-color: #764ba2;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #764ba2;
    background: rgba(118, 75, 162, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.upload-placeholder p {
    font-weight: 500;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.upload-placeholder small {
    color: #888;
}

/* Progress Bar */
.upload-progress {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 500;
}

/* Form Controls */
.option-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
}

.option-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
}

.option-group input[type="range"] {
    flex: 1;
    accent-color: #667eea;
}

.option-group select {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

/* Buttons */
.primary-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    width: 100%;
    transition: all 0.3s ease;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.export-btn {
    background: #f8f9fa;
    border: 1px solid #ddd;
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    margin: 0.25rem;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.export-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

/* Canvas Area */
.canvas-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.image-display, .recreation-display {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.image-frame, .recreation-frame {
    flex: 1;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

#originalImage {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

#analysisCanvas {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
}

.recreation-svg {
    width: 100%;
    height: 100%;
    border-radius: 10px;
}

/* Comparison View */
.comparison-display {
    flex: 1;
}

.comparison-container {
    display: flex;
    height: 100%;
    gap: 1rem;
}

.comparison-side {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.comparison-frame {
    flex: 1;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    background: #f8f9fa;
}

.comparison-divider {
    width: 2px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
    border-radius: 1px;
}

/* Canvas Controls */
.canvas-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-top: 1px solid #e0e0e0;
    margin-top: 1rem;
}

.view-controls {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    background: #f8f9fa;
    border: 1px solid #ddd;
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-btn {
    background: #f8f9fa;
    border: 1px solid #ddd;
    color: #333;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    min-width: 30px;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: #e9ecef;
}

#zoomLevel {
    font-weight: 500;
    color: #667eea;
    min-width: 50px;
    text-align: center;
}

/* Element Library */
.element-categories {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.category h4 {
    color: #333;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.element-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 0.5rem;
}

.element-item {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.element-item:hover {
    border-color: #667eea;
    transform: scale(1.1);
}

/* Properties Panel */
.properties-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.no-selection {
    color: #888;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

.property-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.property-label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.property-input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

/* Analysis Results */
.results-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.result-label {
    font-size: 0.9rem;
    color: #666;
}

.result-value {
    font-weight: 600;
    color: #667eea;
}

/* Color Palette */
.color-palette {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
}

.color-swatch {
    width: 30px;
    height: 30px;
    border-radius: 6px;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.color-swatch:hover {
    transform: scale(1.1);
}

/* Layers Panel */
.layers-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.layer-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.layer-item:hover {
    background: #e9ecef;
}

.layer-item.active {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid #667eea;
}

.layer-visibility {
    cursor: pointer;
    font-size: 1.2rem;
}

.layer-name {
    flex: 1;
    font-weight: 500;
}

.layer-count {
    background: #667eea;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Status Bar */
.status-bar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e0e0e0;
    font-size: 0.9rem;
}

.status-left {
    color: #667eea;
    font-weight: 500;
}

.status-right {
    display: flex;
    gap: 1rem;
    color: #888;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #888;
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

/* Template Grid */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.template-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.template-preview {
    height: 120px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-info {
    padding: 1rem;
}

.template-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.template-description {
    font-size: 0.9rem;
    color: #666;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    backdrop-filter: blur(5px);
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e0e0e0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loadingMessage {
    color: #667eea;
    font-weight: 500;
}

/* SVG Styles */
.recreation-svg .background-layer {
    pointer-events: none;
}

.recreation-svg .shapes-layer circle {
    fill: #4CAF50;
    stroke: #2E7D32;
    stroke-width: 2;
    cursor: pointer;
    transition: all 0.3s ease;
}

.recreation-svg .shapes-layer circle:hover {
    fill: #66BB6A;
    stroke-width: 3;
}

.recreation-svg .shapes-layer rect {
    fill: #FF9800;
    stroke: #F57C00;
    stroke-width: 2;
    cursor: pointer;
    transition: all 0.3s ease;
}

.recreation-svg .connections-layer line {
    stroke: #2196F3;
    stroke-width: 2;
    cursor: pointer;
    transition: all 0.3s ease;
}

.recreation-svg .connections-layer line:hover {
    stroke: #42A5F5;
    stroke-width: 3;
}

.recreation-svg .text-layer text {
    font-family: 'Segoe UI', sans-serif;
    font-weight: 600;
    fill: #333;
    cursor: pointer;
    user-select: none;
}

.recreation-svg .annotations-layer rect {
    fill: rgba(244, 67, 54, 0.3);
    stroke: #F44336;
    stroke-width: 2;
    stroke-dasharray: 5,5;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-container {
        grid-template-columns: 250px 1fr 250px;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .main-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .left-panel, .right-panel {
        max-height: none;
    }
    
    .workflow-steps {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .nav-controls {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .comparison-container {
        flex-direction: column;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.text-center {
    text-align: center;
}

.text-muted {
    color: #888;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }