/**
 * Vision Mode System
 * 
 * Complete vision-based analysis and automation system for understanding
 * UI components, application architecture, user behavior, and generating insights.
 */

// Export semantic analysis components
export * from './semantic';

// Export main Vision Mode Agent
export { 
  VisionModeAgent,
  createDefaultVisionModeConfig
} from './VisionModeAgent';
export type {
  VisionModeAgentConfig,
  VisionModeCapability,
  VisionModeRequest,
  VisionModeResponse,
  AutomationTrigger,
  AutomationAction,
  AutomationWorkflow,
  WorkflowStep,
  AlertConfig,
  AutomationResult,
  MonitoringResult
} from './VisionModeAgent';

// Export utility functions
export { VisionModeFactory } from './VisionModeFactory';
export { VisionModeUtils } from './VisionModeUtils';

/**
 * Vision Mode Factory
 * 
 * Factory class for creating and configuring Vision Mode instances
 */
export class VisionModeFactory {
  /**
   * Create a complete Vision Mode system with default configuration
   */
  static async createDefault(): Promise<VisionModeAgent> {
    const config = createDefaultVisionModeConfig();
    const agent = new VisionModeAgent(config);
    await agent.initialize();
    return agent;
  }

  /**
   * Create a Vision Mode system with custom configuration
   */
  static async create(config: Partial<VisionModeAgentConfig>): Promise<VisionModeAgent> {
    const defaultConfig = createDefaultVisionModeConfig();
    const mergedConfig = this.mergeConfigs(defaultConfig, config);
    const agent = new VisionModeAgent(mergedConfig);
    await agent.initialize();
    return agent;
  }

  /**
   * Create a lightweight Vision Mode system for analysis only
   */
  static async createAnalysisOnly(): Promise<VisionModeAgent> {
    const config = createDefaultVisionModeConfig();
    config.automation.enabled = false;
    config.mcp.enabled = false;
    config.monitoring.enabled = false;
    config.capabilities = [
      'screenshot-analysis',
      'ui-understanding',
      'pattern-recognition',
      'code-analysis',
      'knowledge-graph',
      'export-documentation'
    ];
    
    const agent = new VisionModeAgent(config);
    await agent.initialize();
    return agent;
  }

  /**
   * Create a Vision Mode system for behavioral analysis
   */
  static async createBehavioralAnalysis(): Promise<VisionModeAgent> {
    const config = createDefaultVisionModeConfig();
    config.capabilities = [
      'behavioral-tracking',
      'real-time-monitoring',
      'predictive-insights',
      'export-documentation'
    ];
    
    const agent = new VisionModeAgent(config);
    await agent.initialize();
    return agent;
  }

  /**
   * Create a Vision Mode system for automation
   */
  static async createAutomation(): Promise<VisionModeAgent> {
    const config = createDefaultVisionModeConfig();
    config.capabilities = [
      'screenshot-analysis',
      'ui-understanding',
      'automation',
      'real-time-monitoring'
    ];
    
    const agent = new VisionModeAgent(config);
    await agent.initialize();
    return agent;
  }

  /**
   * Merge configurations
   */
  private static mergeConfigs(
    defaultConfig: VisionModeAgentConfig,
    customConfig: Partial<VisionModeAgentConfig>
  ): VisionModeAgentConfig {
    return {
      ...defaultConfig,
      ...customConfig,
      semantic: {
        ...defaultConfig.semantic,
        ...customConfig.semantic
      },
      automation: {
        ...defaultConfig.automation,
        ...customConfig.automation
      },
      mcp: {
        ...defaultConfig.mcp,
        ...customConfig.mcp
      },
      monitoring: {
        ...defaultConfig.monitoring,
        ...customConfig.monitoring
      },
      security: {
        ...defaultConfig.security,
        ...customConfig.security
      }
    };
  }
}

/**
 * Vision Mode Utilities
 * 
 * Utility functions for working with Vision Mode
 */
export class VisionModeUtils {
  /**
   * Validate Vision Mode configuration
   */
  static validateConfig(config: VisionModeAgentConfig): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required fields
    if (!config.agentId) errors.push('Agent ID is required');
    if (!config.name) errors.push('Agent name is required');
    if (!config.version) errors.push('Agent version is required');
    if (!config.capabilities || config.capabilities.length === 0) {
      errors.push('At least one capability is required');
    }

    // Validate semantic config
    if (!config.semantic) {
      errors.push('Semantic configuration is required');
    }

    // Validate MCP config if enabled
    if (config.mcp.enabled) {
      if (!config.mcp.server.host) errors.push('MCP server host is required');
      if (!config.mcp.server.port) errors.push('MCP server port is required');
    }

    // Validate automation config if enabled
    if (config.automation.enabled) {
      if (!config.automation.workflows) {
        warnings.push('No automation workflows configured');
      }
    }

    // Validate monitoring config if enabled
    if (config.monitoring.enabled) {
      if (!config.monitoring.metrics || config.monitoring.metrics.length === 0) {
        warnings.push('No monitoring metrics configured');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get system requirements
   */
  static getSystemRequirements(): {
    minimum: SystemRequirements;
    recommended: SystemRequirements;
  } {
    return {
      minimum: {
        memory: '2GB',
        cpu: '2 cores',
        storage: '1GB',
        node: '16.0.0',
        browser: 'Chrome 90+'
      },
      recommended: {
        memory: '8GB',
        cpu: '4 cores',
        storage: '5GB',
        node: '18.0.0',
        browser: 'Chrome 100+'
      }
    };
  }

  /**
   * Check system compatibility
   */
  static checkCompatibility(): {
    compatible: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check Node.js version
    if (typeof process !== 'undefined' && process.version) {
      const nodeVersion = process.version.replace('v', '');
      const majorVersion = parseInt(nodeVersion.split('.')[0]);
      if (majorVersion < 16) {
        issues.push(`Node.js ${nodeVersion} is not supported. Minimum version is 16.0.0`);
      } else if (majorVersion < 18) {
        recommendations.push('Consider upgrading to Node.js 18+ for better performance');
      }
    }

    // Check browser environment
    if (typeof window !== 'undefined') {
      if (!window.navigator.userAgent.includes('Chrome')) {
        recommendations.push('Chrome browser is recommended for best compatibility');
      }
    }

    // Check available memory
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const memory = process.memoryUsage();
      if (memory.heapTotal < 1000000000) { // 1GB
        recommendations.push('More memory is recommended for optimal performance');
      }
    }

    return {
      compatible: issues.length === 0,
      issues,
      recommendations
    };
  }

  /**
   * Generate configuration template
   */
  static generateConfigTemplate(options: {
    includeComments?: boolean;
    format?: 'json' | 'typescript';
  } = {}): string {
    const config = createDefaultVisionModeConfig();
    
    if (options.format === 'typescript') {
      return `import { VisionModeAgentConfig } from '@/vision-mode';

export const visionModeConfig: VisionModeAgentConfig = ${JSON.stringify(config, null, 2)};`;
    }
    
    return JSON.stringify(config, null, 2);
  }

  /**
   * Parse screenshot data
   */
  static parseScreenshot(data: string | File | Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      if (typeof data === 'string') {
        // Already base64 or data URL
        resolve(data);
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = () => {
        reject(new Error('Failed to read screenshot data'));
      };
      reader.readAsDataURL(data);
    });
  }

  /**
   * Format analysis results for display
   */
  static formatAnalysisResults(results: ComprehensiveAnalysisResult): {
    summary: string;
    details: Record<string, any>;
    charts: any[];
  } {
    const summary = `Analysis completed in ${results.metrics.duration}ms. Found ${results.metrics.patternsDetected} patterns and ${results.metrics.issuesFound} issues.`;
    
    const details = {
      semantic: results.semantic ? 'Completed' : 'Skipped',
      knowledge: results.knowledge ? 'Completed' : 'Skipped',
      patterns: results.patterns ? 'Completed' : 'Skipped',
      code: results.code ? 'Completed' : 'Skipped',
      behavioral: results.behavioral ? 'Completed' : 'Skipped',
      insights: results.insights.length,
      recommendations: results.recommendations.length
    };

    const charts = [
      {
        type: 'pie',
        title: 'Analysis Coverage',
        data: [
          { name: 'Semantic', value: results.semantic ? 1 : 0 },
          { name: 'Knowledge', value: results.knowledge ? 1 : 0 },
          { name: 'Patterns', value: results.patterns ? 1 : 0 },
          { name: 'Code', value: results.code ? 1 : 0 },
          { name: 'Behavioral', value: results.behavioral ? 1 : 0 }
        ]
      },
      {
        type: 'bar',
        title: 'Metrics',
        data: [
          { name: 'Components', value: results.metrics.componentsAnalyzed },
          { name: 'Patterns', value: results.metrics.patternsDetected },
          { name: 'Issues', value: results.metrics.issuesFound },
          { name: 'Recommendations', value: results.metrics.recommendationsGenerated }
        ]
      }
    ];

    return { summary, details, charts };
  }

  /**
   * Create analysis request from options
   */
  static createAnalysisRequest(options: {
    screenshot?: string | File | Blob;
    url?: string;
    codebase?: string;
    timeRange?: { start: Date; end: Date };
    includeSemanticAnalysis?: boolean;
    includeKnowledgeGraph?: boolean;
    includePatternAnalysis?: boolean;
    includeCodeAnalysis?: boolean;
    includeBehavioralAnalysis?: boolean;
    priority?: number;
    tags?: string[];
  }): Promise<VisionModeRequest> {
    return new Promise(async (resolve, reject) => {
      try {
        const screenshot = options.screenshot ? 
          await this.parseScreenshot(options.screenshot) : undefined;

        const request: VisionModeRequest = {
          id: `request_${Date.now()}`,
          type: 'analysis',
          input: {
            screenshot,
            url: options.url,
            codebase: options.codebase,
            timeRange: options.timeRange
          },
          config: {
            includeSemanticAnalysis: options.includeSemanticAnalysis ?? true,
            includeKnowledgeGraph: options.includeKnowledgeGraph ?? true,
            includePatternAnalysis: options.includePatternAnalysis ?? true,
            includeCodeAnalysis: options.includeCodeAnalysis ?? true,
            includeBehavioralAnalysis: options.includeBehavioralAnalysis ?? true
          },
          metadata: {
            timestamp: new Date(),
            priority: options.priority ?? 1,
            tags: options.tags ?? []
          }
        };

        resolve(request);
      } catch (error) {
        reject(error);
      }
    });
  }
}

/**
 * System Requirements
 */
export interface SystemRequirements {
  memory: string;
  cpu: string;
  storage: string;
  node: string;
  browser: string;
}

/**
 * Quick Start Functions
 */

/**
 * Analyze a screenshot with default settings
 */
export async function analyzeScreenshot(
  screenshot: string | File | Blob,
  options: {
    includeCodeAnalysis?: boolean;
    includeBehavioralAnalysis?: boolean;
    exportFormat?: 'pdf' | 'html' | 'json';
  } = {}
): Promise<{ analysis: ComprehensiveAnalysisResult; export?: ExportResult }> {
  const agent = await VisionModeFactory.createAnalysisOnly();
  
  try {
    const request = await VisionModeUtils.createAnalysisRequest({
      screenshot,
      includeCodeAnalysis: options.includeCodeAnalysis ?? false,
      includeBehavioralAnalysis: options.includeBehavioralAnalysis ?? false
    });

    const response = await agent.processRequest(request);
    
    if (response.status === 'error') {
      throw new Error(response.error?.message || 'Analysis failed');
    }

    const result: any = { analysis: response.result?.analysis };

    // Generate export if requested
    if (options.exportFormat) {
      const exportRequest: VisionModeRequest = {
        ...request,
        type: 'export',
        config: {
          ...request.config,
          exportFormat: options.exportFormat,
          reportType: 'technical-analysis'
        }
      };

      const exportResponse = await agent.processRequest(exportRequest);
      if (exportResponse.status === 'success') {
        result.export = exportResponse.result?.export;
      }
    }

    return result;
  } finally {
    await agent.shutdown();
  }
}

/**
 * Analyze code with default settings
 */
export async function analyzeCode(
  codebase: string,
  options: {
    includePatterns?: boolean;
    includeKnowledgeGraph?: boolean;
    exportFormat?: 'pdf' | 'html' | 'json';
  } = {}
): Promise<{ analysis: ComprehensiveAnalysisResult; export?: ExportResult }> {
  const agent = await VisionModeFactory.createAnalysisOnly();
  
  try {
    const request = await VisionModeUtils.createAnalysisRequest({
      codebase,
      includeSemanticAnalysis: false,
      includePatternAnalysis: options.includePatterns ?? true,
      includeKnowledgeGraph: options.includeKnowledgeGraph ?? true,
      includeBehavioralAnalysis: false
    });

    const response = await agent.processRequest(request);
    
    if (response.status === 'error') {
      throw new Error(response.error?.message || 'Analysis failed');
    }

    const result: any = { analysis: response.result?.analysis };

    // Generate export if requested
    if (options.exportFormat) {
      const exportRequest: VisionModeRequest = {
        ...request,
        type: 'export',
        config: {
          ...request.config,
          exportFormat: options.exportFormat,
          reportType: 'technical-analysis'
        }
      };

      const exportResponse = await agent.processRequest(exportRequest);
      if (exportResponse.status === 'success') {
        result.export = exportResponse.result?.export;
      }
    }

    return result;
  } finally {
    await agent.shutdown();
  }
}

/**
 * Start behavioral tracking
 */
export async function startBehavioralTracking(
  options: {
    sessionTimeout?: number;
    privacyMode?: 'strict' | 'balanced' | 'permissive';
    realTime?: boolean;
  } = {}
): Promise<VisionModeAgent> {
  const config = createDefaultVisionModeConfig();
  config.behavioral.sessionTimeout = options.sessionTimeout ?? 1800000;
  config.behavioral.privacyMode = options.privacyMode ?? 'balanced';
  config.behavioral.realTime.enabled = options.realTime ?? true;
  
  const agent = await VisionModeFactory.createBehavioralAnalysis();
  return agent;
}

// Default export
export default {
  VisionModeAgent,
  VisionModeFactory,
  VisionModeUtils,
  createDefaultVisionModeConfig,
  analyzeScreenshot,
  analyzeCode,
  startBehavioralTracking
};