
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pináculo - Numerología</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            color: #333;
            padding: 20px;
        }

        .pinaculo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .input-section {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
        }

        .input-section label {
            font-weight: 600;
            color: #333;
        }

        .input-section input {
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .input-section input:focus {
            border-color: #667eea;
        }

        .input-section button {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
        }

        .input-section button:hover {
            transform: translateY(-2px);
        }

        .chart-area {
            padding: 40px 20px;
            position: relative;
            background: white;
        }

        .pinaculo-chart {
            width: 100%;
            height: auto;
            max-width: 600px;
            margin: 0 auto;
            display: block;
        }

        .connections line {
            stroke: #ccc;
            stroke-width: 2;
            opacity: 0.7;
        }

        .number-circle {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .number-circle:hover {
            transform: scale(1.1);
        }

        .number-circle.green circle {
            fill: #e8f5e8;
            stroke: #4caf50;
            stroke-width: 3;
        }

        .number-circle.green text {
            fill: #2e7d32;
            font-weight: bold;
        }

        .number-circle.purple circle {
            fill: #f3e5f5;
            stroke: #9c27b0;
            stroke-width: 3;
        }

        .number-circle.purple text {
            fill: #6a1b9a;
            font-weight: bold;
        }

        .number-circle.purple-center circle {
            fill: #f3e5f5;
            stroke: #9c27b0;
            stroke-width: 4;
        }

        .number-circle.red circle {
            fill: #ffebee;
            stroke: #f44336;
            stroke-width: 3;
        }

        .number-circle.red text {
            fill: #c62828;
            font-weight: bold;
        }

        .number-circle.red-center circle {
            fill: #ffebee;
            stroke: #f44336;
            stroke-width: 4;
        }

        .number-circle text {
            text-anchor: middle;
            dominant-baseline: middle;
            font-size: 16px;
            font-family: 'Arial', sans-serif;
        }

        .number-circle .label {
            font-size: 12px;
            font-weight: normal;
            opacity: 0.8;
        }

        .ausencias-box {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 100px;
            height: 100px;
            background: #ffebee;
            border: 3px solid #f44336;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.2);
        }

        .ausencias-number {
            font-size: 36px;
            font-weight: bold;
            color: #c62828;
        }

        .ausencias-label {
            font-size: 14px;
            color: #c62828;
            margin-top: 5px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .input-section {
                flex-direction: column;
                gap: 10px;
            }

            .ausencias-box {
                width: 80px;
                height: 80px;
                bottom: 10px;
                right: 10px;
            }

            .ausencias-number {
                font-size: 28px;
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/interactjs/dist/interact.min.js"></script>
</head>
<body>
    <div class="pinaculo-container">
        <div class="header">
            Pináculo - Numerología
        </div>
        <div class="input-section">
            <label for="birthdate">Fecha de Nacimiento:</label>
            <input type="date" id="birthdate" name="birthdate">
            <button onclick="calculateAndUpdate()">Calcular</button>
        </div>
        <div class="chart-area">
            <svg class="pinaculo-chart numerology-chart" id="numerology-chart" viewBox="0 0 500 450">
                <!-- Connection Lines -->
                <g class="connections">
                    <!-- Top to Second Level -->
                    <line x1="250" y1="60" x2="180" y2="120"/> <!-- H-E -->
                    <line x1="250" y1="60" x2="250" y2="120"/> <!-- H-G -->
                    <line x1="250" y1="60" x2="320" y2="120"/> <!-- H-F -->

                    <!-- Second to Third Level -->
                    <line x1="180" y1="120" x2="130" y2="180"/> <!-- E-A -->
                    <line x1="180" y1="120" x2="220" y2="180"/> <!-- E-B -->
                    <line x1="250" y1="120" x2="220" y2="180"/> <!-- G-B -->
                    <line x1="250" y1="120" x2="280" y2="180"/> <!-- G-C -->
                    <line x1="320" y1="120" x2="280" y2="180"/> <!-- F-C -->
                    <line x1="320" y1="120" x2="370" y2="180"/> <!-- F-D -->

                    <!-- Third to Fourth Level -->
                    <line x1="130" y1="180" x2="180" y2="240"/> <!-- A-K -->
                    <line x1="220" y1="180" x2="180" y2="240"/> <!-- B-K -->
                    <line x1="220" y1="180" x2="250" y2="240"/> <!-- B-O -->
                    <line x1="220" y1="180" x2="320" y2="240"/> <!-- B-L -->
                    <line x1="280" y1="180" x2="250" y2="240"/> <!-- C-O -->
                    <line x1="280" y1="180" x2="320" y2="240"/> <!-- C-L -->
                    <line x1="370" y1="180" x2="320" y2="240"/> <!-- D-L -->

                    <!-- Fourth to Final Level -->
                    <line x1="180" y1="240" x2="250" y2="300"/> <!-- K-Final -->
                    <line x1="250" y1="240" x2="250" y2="300"/> <!-- O-Final -->
                    <line x1="320" y1="240" x2="250" y2="300"/> <!-- L-Final -->
                </g>

                <!-- Number Circles - Clean Pyramid Structure -->
                <!-- Top Level (1 circle) -->
                <g class="number-circle green" id="H">
                    <circle cx="250" cy="60" r="25"/>
                    <text x="250" y="66">7</text>
                    <text class="label" x="250" y="90">H</text>
                </g>

                <!-- Second Level (3 circles) -->
                <g class="number-circle green" id="E">
                    <circle cx="180" cy="120" r="22"/>
                    <text x="180" y="126">7</text>
                    <text class="label" x="180" y="148">E</text>
                </g>
                <g class="number-circle green" id="G">
                    <circle cx="250" cy="120" r="22"/>
                    <text x="250" y="126">8</text>
                    <text class="label" x="250" y="148">G</text>
                </g>
                <g class="number-circle green" id="F">
                    <circle cx="320" cy="120" r="22"/>
                    <text x="320" y="126">1</text>
                    <text class="label" x="320" y="148">F</text>
                </g>

                <!-- Third Level (4 circles) -->
                <g class="number-circle purple" id="A">
                    <circle cx="130" cy="180" r="20"/>
                    <text x="130" y="186">11</text>
                    <text class="label" x="130" y="206">A</text>
                </g>
                <g class="number-circle purple-center" id="B">
                    <circle cx="220" cy="180" r="25"/>
                    <text x="220" y="186">5</text>
                    <text class="label" x="220" y="212">B</text>
                </g>
                <g class="number-circle purple" id="C">
                    <circle cx="280" cy="180" r="20"/>
                    <text x="280" y="186">5</text>
                    <text class="label" x="280" y="206">C</text>
                </g>
                <g class="number-circle purple" id="D">
                    <circle cx="370" cy="180" r="20"/>
                    <text x="370" y="186">3</text>
                    <text class="label" x="370" y="206">D</text>
                </g>

                <!-- Fourth Level (3 circles) -->
                <g class="number-circle red" id="K">
                    <circle cx="180" cy="240" r="20"/>
                    <text x="180" y="246">3</text>
                    <text class="label" x="180" y="266">K</text>
                </g>
                <g class="number-circle red" id="O">
                    <circle cx="250" cy="240" r="20"/>
                    <text x="250" y="246">6</text>
                    <text class="label" x="250" y="266">O</text>
                </g>
                <g class="number-circle red" id="L">
                    <circle cx="320" cy="240" r="20"/>
                    <text x="320" y="246">0</text>
                    <text class="label" x="320" y="266">L</text>
                </g>

                <!-- Final Level (1 circle) -->
                <g class="number-circle red-center" id="FINAL">
                    <circle cx="250" cy="300" r="28"/>
                    <text x="250" y="307">9</text>
                    <text class="label" x="250" y="330">FINAL</text>
                </g>
            </svg>
            <div class="ausencias-box">
                <div class="ausencias-number">24</div>
                <div class="ausencias-label">Ausencias</div>
            </div>
        </div>
    </div>

    <script>
        // Numerology calculation functions
        function reduceToSingleDigit(num) {
            while (num > 9 && num !== 11 && num !== 22 && num !== 33) {
                num = num.toString().split('').reduce((sum, digit) => sum + parseInt(digit), 0);
            }
            return num;
        }

        function calculateNumerology(birthdate) {
            const [month, day, year] = birthdate.split('/').map(Number);

            // Calculate basic numbers
            const dayNum = reduceToSingleDigit(day);
            const monthNum = reduceToSingleDigit(month);
            const yearNum = reduceToSingleDigit(year);

            // Calculate derived numbers
            const lifePathNum = reduceToSingleDigit(day + month + year);
            const personalityNum = reduceToSingleDigit(dayNum + monthNum);
            const destinyNum = reduceToSingleDigit(monthNum + yearNum);

            return {
                H: lifePathNum,
                E: dayNum,
                G: monthNum,
                F: yearNum,
                A: reduceToSingleDigit(dayNum + monthNum),
                B: personalityNum,
                C: reduceToSingleDigit(monthNum + yearNum),
                D: destinyNum,
                K: reduceToSingleDigit(personalityNum + destinyNum),
                O: reduceToSingleDigit(personalityNum + lifePathNum),
                L: reduceToSingleDigit(destinyNum + lifePathNum),
                FINAL: reduceToSingleDigit(lifePathNum + personalityNum + destinyNum)
            };
        }

        function updateChart(results) {
            const chart = document.querySelector('#numerology-chart');
            if (!chart) return;

            Object.keys(results).forEach(key => {
                const circle = chart.querySelector(`#${key}`);
                if (circle) {
                    const textElement = circle.querySelector('text:not(.label)');
                    if (textElement) {
                        textElement.textContent = results[key];
                    }
                }
            });
        }

        function calculateAndUpdate() {
            const birthdateInput = document.getElementById('birthdate');
            const birthdate = birthdateInput.value;

            if (!birthdate) {
                alert('Por favor, ingrese una fecha de nacimiento válida.');
                return;
            }

            // Convert date format from YYYY-MM-DD to MM/DD/YYYY
            const [year, month, day] = birthdate.split('-');
            const formattedDate = `${month}/${day}/${year}`;

            const results = calculateNumerology(formattedDate);
            updateChart(results);
        }

        // Initialize with a default date for demonstration
        document.addEventListener('DOMContentLoaded', () => {
            const defaultDate = '1990-01-01';
            document.getElementById('birthdate').value = defaultDate;
            calculateAndUpdate();
        });
    </script>
</body>
</html>
