
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pináculo - Numerología</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/interactjs/dist/interact.min.js"></script>
</head>
<body>
    <div class="pinaculo-container">
        <div class="header">
            <span class="header-icon">+</span>
            <span class="header-text">Pináculo</span>
        </div>
        <div class="input-section">
            <label for="birthdate"><PERSON><PERSON> de Nacimiento:</label>
            <input type="date" id="birthdate" name="birthdate">
            <button onclick="calculateAndUpdate()">Calcular</button>
        </div>
        <div class="chart-area">
            <svg class="pinaculo-chart numerology-chart" id="numerology-chart" viewBox="0 0 450 580">
                <!-- Connection Lines -->
                <g class="connections">
                    <!-- Top level connections -->
                    <line x1="225" y1="50" x2="150" y2="130"/> <!-- H-E -->
                    <line x1="225" y1="50" x2="225" y2="130"/> <!-- H-G -->
                    <line x1="225" y1="50" x2="300" y2="130"/> <!-- H-F -->

                    <!-- Second level connections -->
                    <line x1="150" y1="130" x2="100" y2="210"/> <!-- E-A -->
                    <line x1="150" y1="130" x2="200" y2="210"/> <!-- E-B -->
                    <line x1="225" y1="130" x2="200" y2="210"/> <!-- G-B -->
                    <line x1="225" y1="130" x2="250" y2="210"/> <!-- G-C -->
                    <line x1="300" y1="130" x2="250" y2="210"/> <!-- F-C -->
                    <line x1="300" y1="130" x2="350" y2="210"/> <!-- F-D -->

                    <!-- Third level connections -->
                    <line x1="100" y1="210" x2="150" y2="290"/> <!-- A-K -->
                    <line x1="200" y1="210" x2="150" y2="290"/> <!-- B-K -->
                    <line x1="200" y1="210" x2="225" y2="290"/> <!-- B-O -->
                    <line x1="200" y1="210" x2="300" y2="290"/> <!-- B-L -->
                    <line x1="250" y1="210" x2="225" y2="290"/> <!-- C-O -->
                    <line x1="250" y1="210" x2="300" y2="290"/> <!-- C-L -->
                    <line x1="350" y1="210" x2="300" y2="290"/> <!-- D-L -->

                    <!-- Final level connections -->
                    <line x1="150" y1="290" x2="225" y2="370"/> <!-- K-Final -->
                    <line x1="225" y1="290" x2="225" y2="370"/> <!-- O-Final -->
                    <line x1="300" y1="290" x2="225" y2="370"/> <!-- L-Final -->
                </g>

                <!-- Number Circles - Pyramid Structure -->
                <!-- Top Level (1 circle) -->
                <g class="number-circle green" id="H">
                    <circle cx="225" cy="50" r="20"/>
                    <text x="225" y="55">7</text>
                    <text class="label" x="225" y="75">H</text>
                </g>

                <!-- Second Level (3 circles) -->
                <g class="number-circle green" id="E">
                    <circle cx="150" cy="130" r="20"/>
                    <text x="150" y="135">7</text>
                    <text class="label" x="150" y="155">E</text>
                </g>
                <g class="number-circle green" id="G">
                    <circle cx="225" cy="130" r="20"/>
                    <text x="225" y="135">8</text>
                    <text class="label" x="225" y="155">G</text>
                </g>
                <g class="number-circle green" id="F">
                    <circle cx="300" cy="130" r="20"/>
                    <text x="300" y="135">1</text>
                    <text class="label" x="300" y="155">F</text>
                </g>

                <!-- Third Level (4 circles) -->
                <g class="number-circle purple" id="A">
                    <circle cx="100" cy="210" r="20"/>
                    <text x="100" y="215">11</text>
                    <text class="label" x="100" y="235">A</text>
                </g>
                <g class="number-circle purple-center" id="B">
                    <circle cx="200" cy="210" r="25"/>
                    <text x="200" y="215">5</text>
                    <text class="label" x="200" y="240">B</text>
                </g>
                <g class="number-circle purple" id="C">
                    <circle cx="250" cy="210" r="20"/>
                    <text x="250" y="215">5</text>
                    <text class="label" x="250" y="235">C</text>
                </g>
                <g class="number-circle purple" id="D">
                    <circle cx="350" cy="210" r="20"/>
                    <text x="350" y="215">3</text>
                    <text class="label" x="350" y="235">D</text>
                </g>

                <!-- Fourth Level (3 circles) -->
                <g class="number-circle red" id="K">
                    <circle cx="150" cy="290" r="20"/>
                    <text x="150" y="295">3</text>
                    <text class="label" x="150" y="315">K</text>
                </g>
                <g class="number-circle red" id="O">
                    <circle cx="225" cy="290" r="20"/>
                    <text x="225" y="295">6</text>
                    <text class="label" x="225" y="315">O</text>
                </g>
                <g class="number-circle red" id="L">
                    <circle cx="300" cy="290" r="20"/>
                    <text x="300" y="295">0</text>
                    <text class="label" x="300" y="315">L</text>
                </g>

                <!-- Final Level (1 circle) -->
                <g class="number-circle red-center" id="FINAL">
                    <circle cx="225" cy="370" r="25"/>
                    <text x="225" y="375">9</text>
                    <text class="label" x="225" y="400">Final</text>
                </g>

                <!-- Additional side circles -->
                <g class="number-circle green" id="I">
                    <circle cx="75" cy="130" r="18"/>
                    <text x="75" y="135">7</text>
                    <text class="label" x="75" y="155">I</text>
                </g>
                <g class="number-circle green" id="J">
                    <circle cx="375" cy="130" r="18"/>
                    <text x="375" y="135">1</text>
                    <text class="label" x="375" y="155">J</text>
                </g>

                <!-- Bottom row additional circles -->
                <g class="number-circle red" id="M">
                    <circle cx="100" cy="450" r="18"/>
                    <text x="100" y="455">3</text>
                    <text class="label" x="100" y="475">M</text>
                </g>
                <g class="number-circle red" id="N">
                    <circle cx="175" cy="450" r="18"/>
                    <text x="175" y="455">3</text>
                    <text class="label" x="175" y="475">N</text>
                </g>
                <g class="number-circle red" id="P">
                    <circle cx="275" cy="450" r="18"/>
                    <text x="275" y="455">9</text>
                    <text class="label" x="275" y="475">P</text>
                </g>
                <g class="number-circle red" id="Q">
                    <circle cx="350" cy="450" r="18"/>
                    <text x="350" y="455">6</text>
                    <text class="label" x="350" y="475">Q</text>
                </g>
                <g class="number-circle red" id="R">
                    <circle cx="125" cy="520" r="18"/>
                    <text x="125" y="525">3</text>
                    <text class="label" x="125" y="545">R</text>
                </g>
                <g class="number-circle red" id="S">
                    <circle cx="200" cy="520" r="18"/>
                    <text x="200" y="525">9</text>
                    <text class="label" x="200" y="545">S</text>
                </g>
                <g class="number-circle red" id="W">
                    <circle cx="325" cy="520" r="18"/>
                    <text x="325" y="525">4</text>
                    <text class="label" x="325" y="545">W</text>
                </g>
            </svg>
            <div class="ausencias-box">
                <div class="ausencias-number">24</div>
                <div class="ausencias-label">Ausencias</div>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
    <script src="draggable.js"></script>
</body>
</html>
