// Numerology calculation functions
function calculateNumerology(birthdate) {
    const [month, day, year] = birthdate.split('/').map(num => parseInt(num));
    
    // Helper function to reduce numbers to single digit (except master numbers)
    function reduceNumber(num) {
        while (num > 9 && num !== 11 && num !== 22 && num !== 33) {
            num = num.toString().split('').reduce((sum, digit) => sum + parseInt(digit), 0);
        }
        return num;
    }
    
    // Calculate different numerology numbers
    const results = {
        // A. Karma Number (Life Path)
        karma: reduceNumber(month + day + year),
        
        // B. Personal Number (Expression)
        personal: reduceNumber(month + day),
        
        // C. Past Life Number
        pastLife: reduceNumber(day + year),
        
        // D. Personality Number
        personality: reduceNumber(month),
        
        // I. Subconscious Number
        subconscious: reduceNumber(day),
        
        // J. Unconscious Number
        unconscious: reduceNumber(year),
        
        // Shadow numbers (calculated from various combinations)
        shadow: reduceNumber(month * day),
        negativeUnconscious: reduceNumber(year - month),
        familyInferior: reduceNumber(month + year),
        consciousInferior: reduceNumber(day * 2),
        latentInferior: reduceNumber((month + day + year) * 2)
    };
    
    return results;
}

function updateChart(results) {
    // Update the SVG chart with calculated numbers
    const chart = document.querySelector('.numerology-chart');
    
    // Update main numbers (A, B, C, D)
    updateCircleNumber(chart, 'A', results.karma);
    updateCircleNumber(chart, 'B', results.personal);
    updateCircleNumber(chart, 'C', results.pastLife);
    updateCircleNumber(chart, 'D', results.personality);
    
    // Update subconscious/unconscious numbers
    updateCircleNumber(chart, 'I', results.subconscious);
    updateCircleNumber(chart, 'J', results.unconscious);
    
    // Update shadow numbers
    updateCircleNumber(chart, 'P', results.shadow);
    updateCircleNumber(chart, 'O', results.negativeUnconscious);
    updateCircleNumber(chart, 'Q', results.familyInferior);
    updateCircleNumber(chart, 'R', results.consciousInferior);
    updateCircleNumber(chart, 'S', results.latentInferior);
    
    // Update results panel
    updateResultsPanel(results);
}

function updateCircleNumber(chart, letter, number) {
    const circles = chart.querySelectorAll('g.number-circle');
    circles.forEach(circle => {
        const letterText = circle.querySelector('text:last-child');
        if (letterText && letterText.textContent === letter) {
            const numberText = circle.querySelector('text:first-child');
            if (numberText) {
                numberText.textContent = number;
            }
        }
    });
}

function updateResultsPanel(results) {
    const resultItems = document.querySelectorAll('.result-item');
    
    // Update each result item with calculated numbers
    const updates = [
        { selector: '.result-item:nth-child(1) .number-badge', value: results.karma },
        { selector: '.result-item:nth-child(2) .number-badge', value: results.personal },
        { selector: '.result-item:nth-child(3) .number-badge', value: results.pastLife },
        { selector: '.result-item:nth-child(4) .number-badge', value: results.personality },
        { selector: '.result-item:nth-child(5) .number-badge', value: results.subconscious },
        { selector: '.result-item:nth-child(6) .number-badge', value: results.unconscious },
        { selector: '.result-item:nth-child(7) .number-badge', value: results.shadow },
        { selector: '.result-item:nth-child(8) .number-badge', value: results.negativeUnconscious },
        { selector: '.result-item:nth-child(9) .number-badge', value: results.familyInferior },
        { selector: '.result-item:nth-child(10) .number-badge', value: results.consciousInferior },
        { selector: '.result-item:nth-child(11) .number-badge', value: results.latentInferior }
    ];
    
    updates.forEach(update => {
        const element = document.querySelector(update.selector);
        if (element) {
            element.textContent = update.value;
        }
    });
}