// Numerology calculation functions
function calculateNumerology(birthdate) {
    const [month, day, year] = birthdate.split('/').map(num => parseInt(num));
    
    // Helper function to reduce numbers to single digit (except master numbers)
    function reduceNumber(num) {
        while (num > 9 && num !== 11 && num !== 22 && num !== 33) {
            num = num.toString().split('').reduce((sum, digit) => sum + parseInt(digit), 0);
        }
        return num;
    }
    
    // Calculate different numerology numbers
    const results = {
        karma: reduceNumber(month + day + year),
        personal: reduceNumber(month + day),
        pastLife: reduceNumber(day + year),
        personality: reduceNumber(month),
        subconscious: reduceNumber(day),
        unconscious: reduceNumber(year),
        shadow: reduceNumber(month * day),
        negativeUnconscious: reduceNumber(year - month),
        familyInferior: reduceNumber(month + year),
        consciousInferior: reduceNumber(day * 2),
        latentInferior: reduceNumber((month + day + year) * 2)
    };
    
    return results;
}

function updateChart(results) {
    const chart = document.querySelector('#numerology-chart');
    updateCircleNumber(chart, 'A', results.karma);
    updateCircleNumber(chart, 'B', results.personal);
    updateCircleNumber(chart, 'C', results.pastLife);
    updateCircleNumber(chart, 'D', results.personality);
    updateCircleNumber(chart, 'I', results.subconscious);
    updateCircleNumber(chart, 'J', results.unconscious);
    updateCircleNumber(chart, 'P', results.shadow);
    updateCircleNumber(chart, 'O', results.negativeUnconscious);
    updateCircleNumber(chart, 'Q', results.familyInferior);
    updateCircleNumber(chart, 'R', results.consciousInferior);
    updateCircleNumber(chart, 'S', results.latentInferior);
    updateResultsPanel(results);
}

function updateCircleNumber(chart, letter, number) {
    const circles = chart.querySelectorAll('g.number-circle');
    circles.forEach(circle => {
        const letterText = circle.querySelector('text:last-child');
        if (letterText && letterText.textContent === letter) {
            const numberText = circle.querySelector('text:first-child');
            if (numberText) {
                numberText.textContent = number;
            }
        }
    });
}

function updateResultsPanel(results) {
    const resultItems = document.querySelectorAll('.result-item');
    const updates = [
        { selector: '.result-item:nth-child(1) .number-badge', value: results.karma },
        { selector: '.result-item:nth-child(2) .number-badge', value: results.personal },
        { selector: '.result-item:nth-child(3) .number-badge', value: results.pastLife },
        { selector: '.result-item:nth-child(4) .number-badge', value: results.personality },
        { selector: '.result-item:nth-child(5) .number-badge', value: results.subconscious },
        { selector: '.result-item:nth-child(6) .number-badge', value: results.unconscious },
        { selector: '.result-item:nth-child(7) .number-badge', value: results.shadow },
        { selector: '.result-item:nth-child(8) .number-badge', value: results.negativeUnconscious },
        { selector: '.result-item:nth-child(9) .number-badge', value: results.familyInferior },
        { selector: '.result-item:nth-child(10) .number-badge', value: results.consciousInferior },
        { selector: '.result-item:nth-child(11) .number-badge', value: results.latentInferior }
    ];
    
    updates.forEach(update => {
        const element = document.querySelector(update.selector);
        if (element) {
            element.textContent = update.value;
        }
    });
}

function calculateAndUpdate() {
    const birthdateInput = document.getElementById('birthdate');
    const birthdate = birthdateInput.value;

    if (!birthdate) {
        alert('Por favor, ingrese una fecha de nacimiento válida.');
        return;
    }

    const [year, month, day] = birthdate.split('-');
    const formattedDate = `${month}/${day}/${year}`;
    const results = calculateNumerology(formattedDate);
    updateChart(results);
}

// AGGRESSIVE Image compression utility
function aggressiveImageCompress(file, targetSizeKB = 500) {
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = function() {
            // Start with very small dimensions
            let maxDimension = 800;
            let quality = 0.3;
            
            function tryCompress(dimension, qual) {
                let { width, height } = img;
                
                // Aggressive resizing
                if (width > dimension || height > dimension) {
                    if (width > height) {
                        height = (height * dimension) / width;
                        width = dimension;
                    } else {
                        width = (width * dimension) / height;
                        height = dimension;
                    }
                }
                
                canvas.width = width;
                canvas.height = height;
                
                // Clear and draw
                ctx.clearRect(0, 0, width, height);
                ctx.drawImage(img, 0, 0, width, height);
                
                return new Promise((res) => {
                    canvas.toBlob((blob) => {
                        if (blob) {
                            const sizeKB = blob.size / 1024;
                            console.log(`Compressed to ${Math.round(sizeKB)}KB at ${width}x${height}, quality: ${qual}`);
                            
                            if (sizeKB <= targetSizeKB || qual <= 0.1 || dimension <= 200) {
                                res(blob);
                            } else {
                                // Try smaller
                                if (sizeKB > targetSizeKB * 2) {
                                    tryCompress(Math.round(dimension * 0.7), qual * 0.8).then(res);
                                } else {
                                    tryCompress(dimension, qual * 0.8).then(res);
                                }
                            }
                        } else {
                            res(null);
                        }
                    }, 'image/jpeg', qual);
                });
            }
            
            tryCompress(maxDimension, quality).then(resolve);
        };
        
        img.onerror = reject;
        img.src = URL.createObjectURL(file);
    });
}

// Image upload and annotation functionality
let isAnnotating = false;
let currentAnnotation = null;
let annotations = [];

function handleImageUpload(file) {
    const svg = document.querySelector('svg.pinaculo-chart');
    const bgLayer = svg.querySelector('g.background-layer');
    
    if (!bgLayer) {
        console.error('Background layer not found');
        return;
    }
    
    // Show loading with file size info
    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
    const loadingDiv = document.createElement('div');
    loadingDiv.innerHTML = `
        <div style="text-align: center;">
            <div>Processing image...</div>
            <div style="font-size: 12px; color: #666;">Original: ${fileSizeMB}MB</div>
            <div style="font-size: 12px; color: #666;">Compressing to ~500KB...</div>
        </div>
    `;
    loadingDiv.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border:2px solid #ccc;border-radius:8px;z-index:1000;box-shadow:0 4px 8px rgba(0,0,0,0.2);';
    document.body.appendChild(loadingDiv);
    
    // Always compress aggressively
    aggressiveImageCompress(file, 500) // Target 500KB
        .then(compressedBlob => {
            if (compressedBlob) {
                const finalSizeKB = (compressedBlob.size / 1024).toFixed(0);
                console.log(`Final compressed size: ${finalSizeKB}KB`);
                processImageFile(compressedBlob, bgLayer, loadingDiv);
            } else {
                throw new Error('Compression failed');
            }
        })
        .catch(error => {
            console.error('Compression failed:', error);
            document.body.removeChild(loadingDiv);
            alert('Failed to process image. The file might be corrupted or too complex to compress.');
        });
}

function processImageFile(file, bgLayer, loadingDiv) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        // Clear existing background
        bgLayer.innerHTML = '';
        
        // Create SVG image
        const svgImage = document.createElementNS('http://www.w3.org/2000/svg', 'image');
        svgImage.setAttribute('href', e.target.result);
        svgImage.setAttribute('x', '0');
        svgImage.setAttribute('y', '0');
        svgImage.setAttribute('width', '1200');
        svgImage.setAttribute('height', '800');
        svgImage.setAttribute('preserveAspectRatio', 'xMidYMid meet');
        svgImage.style.opacity = '0.6';
        
        bgLayer.appendChild(svgImage);
        document.body.removeChild(loadingDiv);
        
        console.log('✅ Image uploaded successfully');
        
        // Show success message
        const successDiv = document.createElement('div');
        successDiv.textContent = '✅ Image loaded! Click "Annotate" to mark areas.';
        successDiv.style.cssText = 'position:fixed;top:20px;right:20px;background:#4CAF50;color:white;padding:10px;border-radius:4px;z-index:1000;';
        document.body.appendChild(successDiv);
        setTimeout(() => {
            if (document.body.contains(successDiv)) {
                document.body.removeChild(successDiv);
            }
        }, 3000);
    };
    
    reader.onerror = function() {
        document.body.removeChild(loadingDiv);
        alert('Failed to read compressed image file');
    };
    
    reader.readAsDataURL(file);
}

function enterAnnotateMode() {
    isAnnotating = true;
    const svg = document.querySelector('svg.pinaculo-chart');
    svg.style.cursor = 'crosshair';
    
    svg.addEventListener('mousedown', startAnnotation);
    svg.addEventListener('mousemove', updateAnnotation);
    svg.addEventListener('mouseup', endAnnotation);
    
    // Visual feedback
    const btn = document.getElementById('enterAnnotate');
    if (btn) {
        btn.style.backgroundColor = '#ff4444';
        btn.textContent = 'Drawing Mode ON';
    }
    
    console.log('🎯 Annotation mode enabled - Click and drag to draw marks');
}

function exitAnnotateMode() {
    isAnnotating = false;
    const svg = document.querySelector('svg.pinaculo-chart');
    svg.style.cursor = 'default';
    
    svg.removeEventListener('mousedown', startAnnotation);
    svg.removeEventListener('mousemove', updateAnnotation);
    svg.removeEventListener('mouseup', endAnnotation);
    
    // Reset button
    const btn = document.getElementById('enterAnnotate');
    if (btn) {
        btn.style.backgroundColor = '';
        btn.textContent = 'Annotate';
    }
    
    console.log('✅ Annotation mode disabled');
}

function startAnnotation(e) {
    if (!isAnnotating) return;
    
    const svg = e.currentTarget;
    const rect = svg.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const annotationsLayer = svg.querySelector('g.annotations');
    const newRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    
    newRect.setAttribute('x', x);
    newRect.setAttribute('y', y);
    newRect.setAttribute('width', '0');
    newRect.setAttribute('height', '0');
    newRect.setAttribute('class', 'mark');
    newRect.setAttribute('data-start-x', x);
    newRect.setAttribute('data-start-y', y);
    
    annotationsLayer.appendChild(newRect);
    currentAnnotation = newRect;
}

function updateAnnotation(e) {
    if (!isAnnotating || !currentAnnotation) return;
    
    const svg = e.currentTarget;
    const rect = svg.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const startX = parseFloat(currentAnnotation.getAttribute('data-start-x'));
    const startY = parseFloat(currentAnnotation.getAttribute('data-start-y'));
    
    const width = Math.abs(x - startX);
    const height = Math.abs(y - startY);
    const newX = Math.min(x, startX);
    const newY = Math.min(y, startY);
    
    currentAnnotation.setAttribute('x', newX);
    currentAnnotation.setAttribute('y', newY);
    currentAnnotation.setAttribute('width', width);
    currentAnnotation.setAttribute('height', height);
}

function endAnnotation(e) {
    if (!isAnnotating || !currentAnnotation) return;
    
    // Only keep marks that are big enough
    const width = parseFloat(currentAnnotation.getAttribute('width'));
    const height = parseFloat(currentAnnotation.getAttribute('height'));
    
    if (width < 10 || height < 10) {
        currentAnnotation.remove();
    } else {
        makeAnnotationInteractive(currentAnnotation);
        annotations.push(currentAnnotation);
        console.log(`📍 Mark created at (${currentAnnotation.getAttribute('x')}, ${currentAnnotation.getAttribute('y')})`);
    }
    
    currentAnnotation = null;
}

function makeAnnotationInteractive(rect) {
    rect.addEventListener('click', function(e) {
        e.stopPropagation();
        
        if (rect.classList.contains('selected')) {
            rect.classList.remove('selected');
        } else {
            if (!e.ctrlKey && !e.metaKey) {
                document.querySelectorAll('.mark.selected').forEach(el => {
                    el.classList.remove('selected');
                });
            }
            rect.classList.add('selected');
        }
    });
    
    // Simple drag functionality
    let isDragging = false;
    let dragStartX, dragStartY, rectStartX, rectStartY;
    
    rect.addEventListener('mousedown', function(e) {
        if (e.button !== 0 || isAnnotating) return;
        isDragging = true;
        dragStartX = e.clientX;
        dragStartY = e.clientY;
        rectStartX = parseFloat(rect.getAttribute('x'));
        rectStartY = parseFloat(rect.getAttribute('y'));
        e.preventDefault();
        e.stopPropagation();
    });
    
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;
        
        const dx = e.clientX - dragStartX;
        const dy = e.clientY - dragStartY;
        
        rect.setAttribute('x', rectStartX + dx);
        rect.setAttribute('y', rectStartY + dy);
    });
    
    document.addEventListener('mouseup', function() {
        isDragging = false;
    });
}

function getAnnotationMarks() {
    const marks = document.querySelectorAll('.mark');
    return Array.from(marks).map((mark, index) => ({
        id: index,
        x: parseFloat(mark.getAttribute('x')),
        y: parseFloat(mark.getAttribute('y')),
        width: parseFloat(mark.getAttribute('width')),
        height: parseFloat(mark.getAttribute('height')),
        selected: mark.classList.contains('selected')
    }));
}

// Initialize everything
document.addEventListener('DOMContentLoaded', () => {
    // Initialize numerology
    const defaultDate = '1990-01-01';
    const birthdateInput = document.getElementById('birthdate');
    if (birthdateInput) {
        birthdateInput.value = defaultDate;
        calculateAndUpdate();
    }
    
    // Wire up image upload
    const imageInput = document.getElementById('imageInput');
    if (imageInput) {
        imageInput.addEventListener('change', (e) => {
            const file = e.target.files && e.target.files[0];
            if (file) {
                console.log(`📁 File selected: ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)}MB)`);
                handleImageUpload(file);
            }
        });
        console.log('✅ Image input wired');
    }
    
    // Wire up annotation buttons
    const enterAnnotateBtn = document.getElementById('enterAnnotate');
    const exitAnnotateBtn = document.getElementById('exitAnnotate');
    
    if (enterAnnotateBtn) {
        enterAnnotateBtn.addEventListener('click', enterAnnotateMode);
        console.log('✅ Annotate button wired');
    }
    
    if (exitAnnotateBtn) {
        exitAnnotateBtn.addEventListener('click', exitAnnotateMode);
        console.log('✅ Done button wired');
    }
    
    // Clear selections on empty click
    const svg = document.querySelector('svg.pinaculo-chart');
    if (svg) {
        svg.addEventListener('click', function(e) {
            if (e.target === svg && !isAnnotating) {
                document.querySelectorAll('.mark.selected').forEach(el => {
                    el.classList.remove('selected');
                });
            }
        });
        console.log('✅ SVG click handler wired');
    }
    
    console.log('🚀 Image upload and annotation system ready!');
});