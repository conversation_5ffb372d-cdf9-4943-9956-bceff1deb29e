/**
 * Image Analysis Module
 * Handles image processing, pattern recognition, and element detection
 */

import { EnhancedPatternRecognition } from './enhancedPatternRecognition.js';

class ImageAnalyzer {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.imageData = null;
        this.detectedElements = [];
        this.colorPalette = [];
        this.processingTime = 0;
        this.enhancedRecognition = new EnhancedPatternRecognition();
        this.isEnhanced = false;
    }

    /**
     * Initialize the analyzer with an image
     */
    async initialize(imageElement) {
        const startTime = performance.now();
        
        try {
            // Create canvas for analysis
            this.canvas = document.createElement('canvas');
            this.ctx = this.canvas.getContext('2d');
            
            // Set canvas dimensions
            this.canvas.width = imageElement.naturalWidth;
            this.canvas.height = imageElement.naturalHeight;
            
            // Draw image to canvas
            this.ctx.drawImage(imageElement, 0, 0);
            this.imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
            
            // Initialize enhanced recognition
            this.isEnhanced = await this.enhancedRecognition.initialize();
            
            this.processingTime = performance.now() - startTime;
            return true;
        } catch (error) {
            console.error('Failed to initialize image analyzer:', error);
            return false;
        }
    }

    /**
     * Check if the analyzer is properly initialized
     */
    isInitialized() {
        return this.canvas !== null && this.ctx !== null && this.imageData !== null;
    }

    /**
     * Enhanced circle detection
     */
    detectCircles() {
        if (this.isEnhanced) {
            return this.enhancedRecognition.detectCirclesAdvanced(this.imageData, this.canvas);
        }
        
        // Fallback to basic detection
        return this.detectCirclesBasic();
    }

    /**
     * Enhanced rectangle detection
     */
    detectRectangles() {
        if (this.isEnhanced) {
            return this.enhancedRecognition.detectRectanglesAdvanced(this.imageData, this.canvas);
        }
        
        // Fallback to basic detection
        return this.detectRectanglesBasic();
    }

    /**
     * Enhanced polygon detection (triangles, pentagons, etc.)
     */
    detectPolygons() {
        if (this.isEnhanced) {
            return this.enhancedRecognition.detectPolygons(this.imageData, this.canvas);
        }
        
        return []; // Basic version doesn't support polygons
    }

    /**
     * Enhanced curved shape detection
     */
    detectCurvedShapes() {
        if (this.isEnhanced) {
            return this.enhancedRecognition.detectCurvedShapes(this.imageData, this.canvas);
        }
        
        return []; // Basic version doesn't support curved shapes
    }

    /**
     * Enhanced text detection
     */
    async detectTextRegions() {
        if (this.isEnhanced) {
            return await this.enhancedRecognition.detectTextAdvanced(this.imageData, this.canvas);
        }
        
        // Fallback to basic detection
        return this.detectTextRegionsBasic();
    }

    /**
     * Enhanced color analysis
     */
    extractColors(maxColors = 10) {
        if (this.isEnhanced) {
            const advancedAnalysis = this.enhancedRecognition.analyzeColorsAdvanced(this.imageData, this.canvas);
            this.colorPalette = advancedAnalysis.dominantColors.slice(0, maxColors);
            return {
                colors: this.colorPalette,
                analysis: advancedAnalysis
            };
        }
        
        // Fallback to basic extraction
        return this.extractColorsBasic(maxColors);
    }

    /**
     * Pattern matching against known templates
     */
    matchPatterns() {
        if (this.isEnhanced) {
            return this.enhancedRecognition.matchPatterns(this.detectedElements);
        }
        
        return [];
    }

    /**
     * Perform complete enhanced analysis
     */
    async analyzeImage(options = {}) {
        const startTime = performance.now();
        this.detectedElements = [];
        
        try {
            // Enhanced shape detection
            if (options.detectShapes !== false) {
                const circles = this.detectCircles();
                const rectangles = this.detectRectangles();
                const polygons = this.detectPolygons();
                const curves = this.detectCurvedShapes();
                
                this.detectedElements.push(...circles, ...rectangles, ...polygons, ...curves);
            }
            
            // Enhanced line detection
            if (options.detectConnections !== false) {
                const lines = this.detectLines();
                this.detectedElements.push(...lines);
            }
            
            // Enhanced text detection
            if (options.detectText !== false) {
                const textRegions = await this.detectTextRegions();
                this.detectedElements.push(...textRegions);
            }
            
            // Enhanced color analysis
            if (options.detectColors !== false) {
                const colorAnalysis = this.extractColors();
                this.colorAnalysis = colorAnalysis.analysis || null;
            }
            
            // Pattern matching
            if (options.matchPatterns !== false) {
                this.patternMatches = this.matchPatterns();
            }
            
            // Analyze spatial relationships
            this.analyzeRelationships();
            
            this.processingTime = performance.now() - startTime;
            
            return {
                elements: this.detectedElements,
                colors: this.colorPalette,
                colorAnalysis: this.colorAnalysis,
                patternMatches: this.patternMatches || [],
                processingTime: this.processingTime,
                imageSize: {
                    width: this.canvas.width,
                    height: this.canvas.height
                },
                enhanced: this.isEnhanced
            };
        } catch (error) {
            console.error('Enhanced analysis failed:', error);
            throw error;
        }
    }

    /**
     * Basic circle detection (fallback)
     */
    detectCirclesBasic() {
        const circles = [];
        const data = this.imageData.data;
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        // Simple circle detection using edge detection and Hough transform approximation
        const edges = this.detectEdges();
        const minRadius = 10;
        const maxRadius = Math.min(width, height) / 4;
        
        for (let r = minRadius; r <= maxRadius; r += 5) {
            for (let y = r; y < height - r; y += 10) {
                for (let x = r; x < width - r; x += 10) {
                    if (this.isCircleCenter(x, y, r, edges)) {
                        const color = this.getAverageColor(x - r, y - r, r * 2, r * 2);
                        circles.push({
                            type: 'circle',
                            x: x,
                            y: y,
                            radius: r,
                            color: color,
                            confidence: this.calculateCircleConfidence(x, y, r, edges)
                        });
                    }
                }
            }
        }
        
        // Filter overlapping circles and keep the best ones
        return this.filterOverlappingCircles(circles);
    }

    /**
     * Detect rectangular shapes
     */
    detectRectangles() {
        const rectangles = [];
        const edges = this.detectEdges();
        const contours = this.findContours(edges);
        
        contours.forEach(contour => {
            const rect = this.approximateRectangle(contour);
            if (rect && this.isValidRectangle(rect)) {
                const color = this.getAverageColor(rect.x, rect.y, rect.width, rect.height);
                rectangles.push({
                    type: 'rectangle',
                    x: rect.x,
                    y: rect.y,
                    width: rect.width,
                    height: rect.height,
                    color: color,
                    confidence: rect.confidence
                });
            }
        });
        
        return rectangles;
    }

    /**
     * Detect lines and connections
     */
    detectLines() {
        const lines = [];
        const edges = this.detectEdges();
        
        // Hough line transform approximation
        const angleStep = Math.PI / 180; // 1 degree
        const maxDistance = Math.sqrt(this.canvas.width ** 2 + this.canvas.height ** 2);
        
        for (let angle = 0; angle < Math.PI; angle += angleStep) {
            for (let distance = 0; distance < maxDistance; distance += 5) {
                const votes = this.countLineVotes(angle, distance, edges);
                if (votes > 50) { // Threshold for line detection
                    const line = this.parameterToLine(angle, distance);
                    if (line) {
                        lines.push({
                            type: 'line',
                            x1: line.x1,
                            y1: line.y1,
                            x2: line.x2,
                            y2: line.y2,
                            angle: angle,
                            length: line.length,
                            confidence: votes / 100
                        });
                    }
                }
            }
        }
        
        return this.filterSimilarLines(lines);
    }

    /**
     * Detect text regions using connected components
     */
    detectTextRegions() {
        const textRegions = [];
        const binary = this.binarizeImage();
        const components = this.findConnectedComponents(binary);
        
        components.forEach(component => {
            if (this.isTextLike(component)) {
                const bounds = this.getComponentBounds(component);
                const text = this.extractText(bounds); // OCR would go here
                
                textRegions.push({
                    type: 'text',
                    x: bounds.x,
                    y: bounds.y,
                    width: bounds.width,
                    height: bounds.height,
                    text: text || 'Detected Text',
                    fontSize: this.estimateFontSize(bounds),
                    confidence: component.confidence
                });
            }
        });
        
        return textRegions;
    }

    /**
     * Extract dominant colors from the image
     */
    extractColors(maxColors = 10) {
        const colors = [];
        const data = this.imageData.data;
        const colorMap = new Map();
        
        // Sample pixels and count colors
        for (let i = 0; i < data.length; i += 16) { // Sample every 4th pixel
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            const a = data[i + 3];
            
            if (a > 128) { // Skip transparent pixels
                const colorKey = `${Math.floor(r/16)*16},${Math.floor(g/16)*16},${Math.floor(b/16)*16}`;
                colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1);
            }
        }
        
        // Sort by frequency and take top colors
        const sortedColors = Array.from(colorMap.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, maxColors);
        
        sortedColors.forEach(([colorKey, count]) => {
            const [r, g, b] = colorKey.split(',').map(Number);
            colors.push({
                r, g, b,
                hex: this.rgbToHex(r, g, b),
                frequency: count,
                percentage: (count / (data.length / 4)) * 100
            });
        });
        
        this.colorPalette = colors;
        return colors;
    }

    /**
     * Perform complete analysis
     */
    async analyzeImage(options = {}) {
        const startTime = performance.now();
        this.detectedElements = [];
        
        try {
            if (options.detectShapes !== false) {
                const circles = this.detectCircles();
                const rectangles = this.detectRectangles();
                this.detectedElements.push(...circles, ...rectangles);
            }
            
            if (options.detectConnections !== false) {
                const lines = this.detectLines();
                this.detectedElements.push(...lines);
            }
            
            if (options.detectText !== false) {
                const textRegions = this.detectTextRegions();
                this.detectedElements.push(...textRegions);
            }
            
            if (options.detectColors !== false) {
                this.extractColors();
            }
            
            // Analyze spatial relationships
            this.analyzeRelationships();
            
            this.processingTime = performance.now() - startTime;
            
            return {
                elements: this.detectedElements,
                colors: this.colorPalette,
                processingTime: this.processingTime,
                imageSize: {
                    width: this.canvas.width,
                    height: this.canvas.height
                }
            };
        } catch (error) {
            console.error('Analysis failed:', error);
            throw error;
        }
    }

    /**
     * Helper Methods
     */
    
    detectEdges() {
        const data = this.imageData.data;
        const width = this.canvas.width;
        const height = this.canvas.height;
        const edges = new Uint8Array(width * height);
        
        // Sobel edge detection
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const idx = (y * width + x) * 4;
                
                // Sobel operators
                const gx = (
                    -1 * this.getGray(x-1, y-1) + 1 * this.getGray(x+1, y-1) +
                    -2 * this.getGray(x-1, y) + 2 * this.getGray(x+1, y) +
                    -1 * this.getGray(x-1, y+1) + 1 * this.getGray(x+1, y+1)
                );
                
                const gy = (
                    -1 * this.getGray(x-1, y-1) + -2 * this.getGray(x, y-1) + -1 * this.getGray(x+1, y-1) +
                    1 * this.getGray(x-1, y+1) + 2 * this.getGray(x, y+1) + 1 * this.getGray(x+1, y+1)
                );
                
                const magnitude = Math.sqrt(gx * gx + gy * gy);
                edges[y * width + x] = magnitude > 50 ? 255 : 0;
            }
        }
        
        return edges;
    }
    
    getGray(x, y) {
        if (x < 0 || x >= this.canvas.width || y < 0 || y >= this.canvas.height) return 0;
        const idx = (y * this.canvas.width + x) * 4;
        return (this.imageData.data[idx] + this.imageData.data[idx + 1] + this.imageData.data[idx + 2]) / 3;
    }
    
    isCircleCenter(x, y, r, edges) {
        let edgeCount = 0;
        const angleStep = Math.PI / 16; // Check 32 points around circle
        
        for (let angle = 0; angle < 2 * Math.PI; angle += angleStep) {
            const px = Math.round(x + r * Math.cos(angle));
            const py = Math.round(y + r * Math.sin(angle));
            
            if (px >= 0 && px < this.canvas.width && py >= 0 && py < this.canvas.height) {
                if (edges[py * this.canvas.width + px] > 0) {
                    edgeCount++;
                }
            }
        }
        
        return edgeCount > 20; // Threshold for circle detection
    }
    
    calculateCircleConfidence(x, y, r, edges) {
        let edgeCount = 0;
        let totalPoints = 0;
        const angleStep = Math.PI / 32;
        
        for (let angle = 0; angle < 2 * Math.PI; angle += angleStep) {
            const px = Math.round(x + r * Math.cos(angle));
            const py = Math.round(y + r * Math.sin(angle));
            
            if (px >= 0 && px < this.canvas.width && py >= 0 && py < this.canvas.height) {
                totalPoints++;
                if (edges[py * this.canvas.width + px] > 0) {
                    edgeCount++;
                }
            }
        }
        
        return totalPoints > 0 ? edgeCount / totalPoints : 0;
    }
    
    filterOverlappingCircles(circles) {
        const filtered = [];
        circles.sort((a, b) => b.confidence - a.confidence);
        
        for (const circle of circles) {
            let overlaps = false;
            for (const existing of filtered) {
                const distance = Math.sqrt((circle.x - existing.x) ** 2 + (circle.y - existing.y) ** 2);
                if (distance < (circle.radius + existing.radius) * 0.7) {
                    overlaps = true;
                    break;
                }
            }
            if (!overlaps) {
                filtered.push(circle);
            }
        }
        
        return filtered;
    }
    
    getAverageColor(x, y, width, height) {
        let r = 0, g = 0, b = 0, count = 0;
        const data = this.imageData.data;
        const canvasWidth = this.canvas.width;
        
        for (let py = Math.max(0, y); py < Math.min(this.canvas.height, y + height); py++) {
            for (let px = Math.max(0, x); px < Math.min(canvasWidth, x + width); px++) {
                const idx = (py * canvasWidth + px) * 4;
                r += data[idx];
                g += data[idx + 1];
                b += data[idx + 2];
                count++;
            }
        }
        
        if (count === 0) return { r: 0, g: 0, b: 0, hex: '#000000' };
        
        r = Math.round(r / count);
        g = Math.round(g / count);
        b = Math.round(b / count);
        
        return {
            r, g, b,
            hex: this.rgbToHex(r, g, b)
        };
    }
    
    rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }
    
    findContours(edges) {
        // Simplified contour detection
        const contours = [];
        const visited = new Set();
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const idx = y * width + x;
                if (edges[idx] > 0 && !visited.has(idx)) {
                    const contour = this.traceContour(x, y, edges, visited);
                    if (contour.length > 20) { // Minimum contour size
                        contours.push(contour);
                    }
                }
            }
        }
        
        return contours;
    }
    
    traceContour(startX, startY, edges, visited) {
        const contour = [];
        const stack = [[startX, startY]];
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        while (stack.length > 0) {
            const [x, y] = stack.pop();
            const idx = y * width + x;
            
            if (visited.has(idx) || x < 0 || x >= width || y < 0 || y >= height || edges[idx] === 0) {
                continue;
            }
            
            visited.add(idx);
            contour.push({ x, y });
            
            // Add 8-connected neighbors
            for (let dy = -1; dy <= 1; dy++) {
                for (let dx = -1; dx <= 1; dx++) {
                    if (dx === 0 && dy === 0) continue;
                    stack.push([x + dx, y + dy]);
                }
            }
        }
        
        return contour;
    }
    
    approximateRectangle(contour) {
        if (contour.length < 4) return null;
        
        // Find bounding box
        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;
        
        contour.forEach(point => {
            minX = Math.min(minX, point.x);
            maxX = Math.max(maxX, point.x);
            minY = Math.min(minY, point.y);
            maxY = Math.max(maxY, point.y);
        });
        
        const width = maxX - minX;
        const height = maxY - minY;
        
        // Check if it's a reasonable rectangle
        if (width < 10 || height < 10) return null;
        
        return {
            x: minX,
            y: minY,
            width: width,
            height: height,
            confidence: Math.min(width, height) / Math.max(width, height) // Aspect ratio confidence
        };
    }
    
    isValidRectangle(rect) {
        const aspectRatio = rect.width / rect.height;
        return aspectRatio > 0.1 && aspectRatio < 10 && rect.confidence > 0.3;
    }
    
    countLineVotes(angle, distance, edges) {
        let votes = 0;
        const width = this.canvas.width;
        const height = this.canvas.height;
        const cosAngle = Math.cos(angle);
        const sinAngle = Math.sin(angle);
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                if (edges[y * width + x] > 0) {
                    const d = x * cosAngle + y * sinAngle;
                    if (Math.abs(d - distance) < 2) {
                        votes++;
                    }
                }
            }
        }
        
        return votes;
    }
    
    parameterToLine(angle, distance) {
        const cosAngle = Math.cos(angle);
        const sinAngle = Math.sin(angle);
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        let x1, y1, x2, y2;
        
        if (Math.abs(sinAngle) > 0.5) {
            // More vertical line
            x1 = 0;
            y1 = distance / sinAngle;
            x2 = width;
            y2 = (distance - width * cosAngle) / sinAngle;
        } else {
            // More horizontal line
            y1 = 0;
            x1 = distance / cosAngle;
            y2 = height;
            x2 = (distance - height * sinAngle) / cosAngle;
        }
        
        // Clip to image bounds
        if (x1 < 0 || x1 >= width || y1 < 0 || y1 >= height ||
            x2 < 0 || x2 >= width || y2 < 0 || y2 >= height) {
            return null;
        }
        
        const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
        
        return { x1, y1, x2, y2, length };
    }
    
    filterSimilarLines(lines) {
        const filtered = [];
        const threshold = 10; // Distance threshold for similar lines
        
        lines.sort((a, b) => b.confidence - a.confidence);
        
        for (const line of lines) {
            let similar = false;
            for (const existing of filtered) {
                const distance = this.lineDistance(line, existing);
                if (distance < threshold) {
                    similar = true;
                    break;
                }
            }
            if (!similar) {
                filtered.push(line);
            }
        }
        
        return filtered;
    }
    
    lineDistance(line1, line2) {
        const midX1 = (line1.x1 + line1.x2) / 2;
        const midY1 = (line1.y1 + line1.y2) / 2;
        const midX2 = (line2.x1 + line2.x2) / 2;
        const midY2 = (line2.y1 + line2.y2) / 2;
        
        return Math.sqrt((midX1 - midX2) ** 2 + (midY1 - midY2) ** 2);
    }
    
    binarizeImage() {
        const data = this.imageData.data;
        const binary = new Uint8Array(this.canvas.width * this.canvas.height);
        
        for (let i = 0; i < data.length; i += 4) {
            const gray = (data[i] + data[i + 1] + data[i + 2]) / 3;
            binary[i / 4] = gray < 128 ? 0 : 255;
        }
        
        return binary;
    }
    
    findConnectedComponents(binary) {
        const components = [];
        const visited = new Set();
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const idx = y * width + x;
                if (binary[idx] === 0 && !visited.has(idx)) {
                    const component = this.floodFill(x, y, binary, visited);
                    if (component.length > 20) {
                        components.push({
                            pixels: component,
                            confidence: Math.min(component.length / 100, 1)
                        });
                    }
                }
            }
        }
        
        return components;
    }
    
    floodFill(startX, startY, binary, visited) {
        const component = [];
        const stack = [[startX, startY]];
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        while (stack.length > 0) {
            const [x, y] = stack.pop();
            const idx = y * width + x;
            
            if (visited.has(idx) || x < 0 || x >= width || y < 0 || y >= height || binary[idx] !== 0) {
                continue;
            }
            
            visited.add(idx);
            component.push({ x, y });
            
            stack.push([x + 1, y], [x - 1, y], [x, y + 1], [x, y - 1]);
        }
        
        return component;
    }
    
    isTextLike(component) {
        const bounds = this.getComponentBounds(component);
        const aspectRatio = bounds.width / bounds.height;
        
        // Text-like characteristics: reasonable aspect ratio, not too small/large
        return aspectRatio > 0.1 && aspectRatio < 10 && 
               bounds.width > 5 && bounds.height > 5 &&
               bounds.width < this.canvas.width / 2 && bounds.height < this.canvas.height / 2;
    }
    
    getComponentBounds(component) {
        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;
        
        component.pixels.forEach(pixel => {
            minX = Math.min(minX, pixel.x);
            maxX = Math.max(maxX, pixel.x);
            minY = Math.min(minY, pixel.y);
            maxY = Math.max(maxY, pixel.y);
        });
        
        return {
            x: minX,
            y: minY,
            width: maxX - minX + 1,
            height: maxY - minY + 1
        };
    }
    
    extractText(bounds) {
        // Placeholder for OCR functionality
        // In a real implementation, this would use Tesseract.js or similar
        return `Text at (${bounds.x}, ${bounds.y})`;
    }
    
    estimateFontSize(bounds) {
        return Math.max(8, Math.min(bounds.height * 0.8, 72));
    }
    
    analyzeRelationships() {
        // Analyze spatial relationships between detected elements
        this.detectedElements.forEach((element, i) => {
            element.relationships = [];
            
            this.detectedElements.forEach((other, j) => {
                if (i !== j) {
                    const relationship = this.calculateRelationship(element, other);
                    if (relationship) {
                        element.relationships.push(relationship);
                    }
                }
            });
        });
    }
    
    calculateRelationship(element1, element2) {
        const distance = this.calculateDistance(element1, element2);
        const direction = this.calculateDirection(element1, element2);
        
        if (distance < 100) { // Close proximity threshold
            return {
                target: element2,
                distance: distance,
                direction: direction,
                type: this.classifyRelationship(element1, element2, distance, direction)
            };
        }
        
        return null;
    }
    
    calculateDistance(element1, element2) {
        const x1 = element1.x + (element1.width || element1.radius || 0) / 2;
        const y1 = element1.y + (element1.height || element1.radius || 0) / 2;
        const x2 = element2.x + (element2.width || element2.radius || 0) / 2;
        const y2 = element2.y + (element2.height || element2.radius || 0) / 2;
        
        return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    }
    
    calculateDirection(element1, element2) {
        const x1 = element1.x + (element1.width || element1.radius || 0) / 2;
        const y1 = element1.y + (element1.height || element1.radius || 0) / 2;
        const x2 = element2.x + (element2.width || element2.radius || 0) / 2;
        const y2 = element2.y + (element2.height || element2.radius || 0) / 2;
        
        const angle = Math.atan2(y2 - y1, x2 - x1);
        return angle * 180 / Math.PI;
    }
    
    classifyRelationship(element1, element2, distance, direction) {
        if (element1.type === 'line' || element2.type === 'line') {
            return 'connected';
        }
        
        if (distance < 50) {
            return 'adjacent';
        }
        
        if (Math.abs(direction) < 15 || Math.abs(direction - 180) < 15) {
            return 'horizontal_aligned';
        }
        
        if (Math.abs(direction - 90) < 15 || Math.abs(direction + 90) < 15) {
            return 'vertical_aligned';
        }
        
        return 'nearby';
    }
}

// ES6 module export
export default ImageAnalyzer;

// Make available globally for debugging
if (typeof window !== 'undefined') {
    window.ImageAnalyzer = ImageAnalyzer;
}