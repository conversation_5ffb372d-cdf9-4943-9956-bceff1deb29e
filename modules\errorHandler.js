/**
 * Comprehensive Error Handling System
 * Provides centralized error management, logging, and recovery mechanisms
 */

export class ErrorHandler {
    constructor() {
        this.errors = [];
        this.errorListeners = [];
        this.config = {
            maxErrors: 100,
            enableLogging: true,
            enableUserNotifications: true,
            enableRecovery: true,
            logLevel: 'error' // 'debug', 'info', 'warn', 'error', 'critical'
        };
        this.errorTypes = {
            UPLOAD: 'upload',
            ANALYSIS: 'analysis',
            RECREATION: 'recreation',
            EXPORT: 'export',
            NETWORK: 'network',
            MEMORY: 'memory',
            WORKER: 'worker',
            RENDERING: 'render',
            VALIDATION: 'validation',
            PERMISSION: 'permission'
        };
        this.severityLevels = {
            LOW: 'low',
            MEDIUM: 'medium',
            HIGH: 'high',
            CRITICAL: 'critical'
        };
        this.isInitialized = false;
    }

    async initialize() {
        try {
            this.setupGlobalErrorHandlers();
            this.setupUnhandledRejectionHandler();
            this.setupResourceErrorHandler();
            this.createErrorUI();
            this.isInitialized = true;
            console.log('✅ Error Handler initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Error Handler:', error);
            return false;
        }
    }

    setupGlobalErrorHandlers() {
        // Global JavaScript error handler
        window.addEventListener('error', (event) => {
            this.handleError({
                type: this.errorTypes.RENDERING,
                severity: this.severityLevels.HIGH,
                message: event.message,
                source: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error?.stack,
                timestamp: new Date().toISOString(),
                context: 'global'
            });
        });

        // Global unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: this.errorTypes.NETWORK,
                severity: this.severityLevels.HIGH,
                message: `Unhandled Promise Rejection: ${event.reason}`,
                stack: event.reason?.stack,
                timestamp: new Date().toISOString(),
                context: 'promise'
            });
        });
    }

    setupUnhandledRejectionHandler() {
        // Custom promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            event.preventDefault(); // Prevent default browser behavior
            
            this.handleError({
                type: this.errorTypes.NETWORK,
                severity: this.severityLevels.MEDIUM,
                message: `Promise rejected: ${event.reason}`,
                stack: event.reason?.stack,
                timestamp: new Date().toISOString(),
                context: 'async_operation',
                recoverable: true
            });
        });
    }

    setupResourceErrorHandler() {
        // Resource loading error handler
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleError({
                    type: this.errorTypes.NETWORK,
                    severity: this.severityLevels.MEDIUM,
                    message: `Resource failed to load: ${event.target.src || event.target.href}`,
                    source: event.target.tagName,
                    timestamp: new Date().toISOString(),
                    context: 'resource_loading',
                    recoverable: true
                });
            }
        }, true);
    }

    handleError(errorInfo) {
        // Enhance error with additional context
        const enhancedError = {
            id: this.generateErrorId(),
            ...errorInfo,
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: errorInfo.timestamp || new Date().toISOString()
        };

        // Store error
        this.storeError(enhancedError);

        // Log error
        if (this.config.enableLogging) {
            this.logError(enhancedError);
        }

        // Notify user if appropriate
        if (this.config.enableUserNotifications && this.shouldNotifyUser(enhancedError)) {
            this.showUserNotification(enhancedError);
        }

        // Attempt recovery
        if (this.config.enableRecovery && enhancedError.recoverable) {
            this.attemptRecovery(enhancedError);
        }

        // Notify listeners
        this.notifyErrorListeners(enhancedError);

        return enhancedError;
    }

    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    storeError(error) {
        this.errors.unshift(error);
        
        // Maintain max errors limit
        if (this.errors.length > this.config.maxErrors) {
            this.errors = this.errors.slice(0, this.config.maxErrors);
        }

        // Store in localStorage for persistence
        try {
            const storedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
            storedErrors.unshift(error);
            localStorage.setItem('app_errors', JSON.stringify(storedErrors.slice(0, 50)));
        } catch (e) {
            console.warn('Failed to store error in localStorage:', e);
        }
    }

    logError(error) {
        const logMethod = this.getLogMethod(error.severity);
        const logMessage = this.formatErrorForLogging(error);
        
        logMethod(logMessage);

        // Send to analytics if available
        if (window.gtag) {
            window.gtag('event', 'exception', {
                description: error.message,
                fatal: error.severity === this.severityLevels.CRITICAL
            });
        }
    }

    getLogMethod(severity) {
        switch (severity) {
            case this.severityLevels.CRITICAL:
                return console.error;
            case this.severityLevels.HIGH:
                return console.error;
            case this.severityLevels.MEDIUM:
                return console.warn;
            case this.severityLevels.LOW:
                return console.info;
            default:
                return console.log;
        }
    }

    formatErrorForLogging(error) {
        return `[${error.severity.toUpperCase()}] ${error.type}: ${error.message}
        Context: ${error.context}
        Source: ${error.source || 'unknown'}
        Time: ${error.timestamp}
        ${error.stack ? `Stack: ${error.stack}` : ''}`;
    }

    shouldNotifyUser(error) {
        // Don't notify for low severity errors or too frequent errors
        if (error.severity === this.severityLevels.LOW) {
            return false;
        }

        // Check if we've shown too many notifications recently
        const recentNotifications = this.errors.filter(e => 
            e.userNotified && 
            Date.now() - new Date(e.timestamp).getTime() < 60000 // Last minute
        );

        return recentNotifications.length < 3;
    }

    showUserNotification(error) {
        const notification = this.createErrorNotification(error);
        document.body.appendChild(notification);
        
        error.userNotified = true;

        // Auto-remove after delay
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, this.getNotificationDuration(error.severity));
    }

    createErrorNotification(error) {
        const notification = document.createElement('div');
        notification.className = `error-notification severity-${error.severity}`;
        notification.innerHTML = `
            <div class="error-content">
                <div class="error-icon">${this.getErrorIcon(error.type)}</div>
                <div class="error-details">
                    <div class="error-title">${this.getErrorTitle(error)}</div>
                    <div class="error-message">${this.getUserFriendlyMessage(error)}</div>
                    ${error.recoverable ? '<div class="error-recovery">Attempting automatic recovery...</div>' : ''}
                </div>
                <div class="error-actions">
                    ${this.getErrorActions(error)}
                    <button class="error-dismiss" onclick="this.parentElement.parentElement.parentElement.remove()">×</button>
                </div>
            </div>
        `;
        return notification;
    }

    getErrorIcon(type) {
        const icons = {
            [this.errorTypes.UPLOAD]: '📁',
            [this.errorTypes.ANALYSIS]: '🔍',
            [this.errorTypes.RECREATION]: '🎨',
            [this.errorTypes.EXPORT]: '📤',
            [this.errorTypes.NETWORK]: '🌐',
            [this.errorTypes.MEMORY]: '💾',
            [this.errorTypes.WORKER]: '⚙️',
            [this.errorTypes.RENDERING]: '🖼️',
            [this.errorTypes.VALIDATION]: '✅',
            [this.errorTypes.PERMISSION]: '🔒'
        };
        return icons[type] || '⚠️';
    }

    getErrorTitle(error) {
        const titles = {
            [this.errorTypes.UPLOAD]: 'Upload Error',
            [this.errorTypes.ANALYSIS]: 'Analysis Error',
            [this.errorTypes.RECREATION]: 'Recreation Error',
            [this.errorTypes.EXPORT]: 'Export Error',
            [this.errorTypes.NETWORK]: 'Network Error',
            [this.errorTypes.MEMORY]: 'Memory Error',
            [this.errorTypes.WORKER]: 'Processing Error',
            [this.errorTypes.RENDERING]: 'Display Error',
            [this.errorTypes.VALIDATION]: 'Validation Error',
            [this.errorTypes.PERMISSION]: 'Permission Error'
        };
        return titles[error.type] || 'Application Error';
    }

    getUserFriendlyMessage(error) {
        // Convert technical errors to user-friendly messages
        const friendlyMessages = {
            'Failed to fetch': 'Unable to connect to the server. Please check your internet connection.',
            'NetworkError': 'Network connection issue. Please try again.',
            'QuotaExceededError': 'Storage limit exceeded. Please clear some space.',
            'SecurityError': 'Security restriction encountered. Please check permissions.',
            'TypeError': 'An unexpected error occurred. Please refresh the page.',
            'ReferenceError': 'A required component is missing. Please refresh the page.'
        };

        for (const [technical, friendly] of Object.entries(friendlyMessages)) {
            if (error.message.includes(technical)) {
                return friendly;
            }
        }

        return error.message.length > 100 ? 
            error.message.substring(0, 100) + '...' : 
            error.message;
    }

    getErrorActions(error) {
        const actions = [];
        
        if (error.recoverable) {
            actions.push('<button class="error-retry" onclick="window.errorHandler.retryOperation(\'' + error.id + '\')">Retry</button>');
        }
        
        if (error.type === this.errorTypes.NETWORK) {
            actions.push('<button class="error-refresh" onclick="window.location.reload()">Refresh Page</button>');
        }
        
        return actions.join('');
    }

    getNotificationDuration(severity) {
        const durations = {
            [this.severityLevels.LOW]: 3000,
            [this.severityLevels.MEDIUM]: 5000,
            [this.severityLevels.HIGH]: 8000,
            [this.severityLevels.CRITICAL]: 0 // Manual dismiss only
        };
        return durations[severity] || 5000;
    }

    attemptRecovery(error) {
        console.log(`🔄 Attempting recovery for error: ${error.id}`);
        
        switch (error.type) {
            case this.errorTypes.NETWORK:
                this.recoverFromNetworkError(error);
                break;
            case this.errorTypes.MEMORY:
                this.recoverFromMemoryError(error);
                break;
            case this.errorTypes.WORKER:
                this.recoverFromWorkerError(error);
                break;
            case this.errorTypes.RENDERING:
                this.recoverFromRenderingError(error);
                break;
            default:
                console.log(`No recovery strategy for error type: ${error.type}`);
        }
    }

    recoverFromNetworkError(error) {
        // Implement network error recovery
        setTimeout(() => {
            console.log('🔄 Retrying network operation...');
            // Trigger retry of the failed operation
            this.dispatchEvent('network-retry', { errorId: error.id });
        }, 2000);
    }

    recoverFromMemoryError(error) {
        // Clear caches and reduce memory usage
        if (window.gc) {
            window.gc();
        }
        
        // Clear image caches
        this.dispatchEvent('memory-cleanup', { errorId: error.id });
    }

    recoverFromWorkerError(error) {
        // Restart workers
        this.dispatchEvent('worker-restart', { errorId: error.id });
    }

    recoverFromRenderingError(error) {
        // Reset canvas or re-render
        this.dispatchEvent('render-reset', { errorId: error.id });
    }

    createErrorUI() {
        // Create error boundary fallback UI
        const errorBoundary = document.createElement('div');
        errorBoundary.id = 'error-boundary';
        errorBoundary.className = 'error-boundary hidden';
        errorBoundary.innerHTML = `
            <div class="error-boundary-content">
                <div class="error-boundary-icon">💥</div>
                <h2>Something went wrong</h2>
                <p>We're sorry, but something unexpected happened.</p>
                <div class="error-boundary-actions">
                    <button onclick="window.location.reload()" class="primary-btn">Refresh Page</button>
                    <button onclick="this.parentElement.parentElement.parentElement.classList.add('hidden')" class="secondary-btn">Dismiss</button>
                </div>
            </div>
        `;
        document.body.appendChild(errorBoundary);

        // Create critical error page
        const criticalErrorPage = document.createElement('div');
        criticalErrorPage.id = 'critical-error-page';
        criticalErrorPage.className = 'critical-error-page hidden';
        criticalErrorPage.innerHTML = `
            <div class="critical-error-content">
                <div class="critical-error-icon">🚨</div>
                <h1>Critical Error</h1>
                <p>The application has encountered a critical error and cannot continue.</p>
                <div class="critical-error-details">
                    <p>Please try refreshing the page. If the problem persists, contact support.</p>
                </div>
                <div class="critical-error-actions">
                    <button onclick="window.location.reload()" class="primary-btn">Refresh Page</button>
                    <button onclick="window.errorHandler.showErrorDetails()" class="secondary-btn">Show Details</button>
                </div>
            </div>
        `;
        document.body.appendChild(criticalErrorPage);
    }

    showErrorBoundary() {
        const boundary = document.getElementById('error-boundary');
        if (boundary) {
            boundary.classList.remove('hidden');
        }
    }

    showCriticalErrorPage() {
        const page = document.getElementById('critical-error-page');
        if (page) {
            page.classList.remove('hidden');
        }
    }

    showErrorDetails() {
        const recentErrors = this.errors.slice(0, 5);
        const details = recentErrors.map(error => 
            `${error.timestamp}: ${error.type} - ${error.message}`
        ).join('\n');
        
        alert(`Recent Errors:\n\n${details}`);
    }

    // Wrapper methods for common operations
    wrapAsync(asyncFn, context = 'async_operation') {
        return async (...args) => {
            try {
                return await asyncFn(...args);
            } catch (error) {
                this.handleError({
                    type: this.errorTypes.VALIDATION,
                    severity: this.severityLevels.MEDIUM,
                    message: error.message,
                    stack: error.stack,
                    context,
                    recoverable: true
                });
                throw error;
            }
        };
    }

    wrapSync(syncFn, context = 'sync_operation') {
        return (...args) => {
            try {
                return syncFn(...args);
            } catch (error) {
                this.handleError({
                    type: this.errorTypes.VALIDATION,
                    severity: this.severityLevels.MEDIUM,
                    message: error.message,
                    stack: error.stack,
                    context,
                    recoverable: true
                });
                throw error;
            }
        };
    }

    // Event system for error handling
    addEventListener(event, callback) {
        this.errorListeners.push({ event, callback });
    }

    removeEventListener(event, callback) {
        this.errorListeners = this.errorListeners.filter(
            listener => listener.event !== event || listener.callback !== callback
        );
    }

    dispatchEvent(event, data) {
        this.errorListeners
            .filter(listener => listener.event === event)
            .forEach(listener => {
                try {
                    listener.callback(data);
                } catch (error) {
                    console.error('Error in error listener:', error);
                }
            });
    }

    notifyErrorListeners(error) {
        this.dispatchEvent('error', error);
    }

    // Utility methods
    getErrors(filter = {}) {
        let filteredErrors = this.errors;

        if (filter.type) {
            filteredErrors = filteredErrors.filter(error => error.type === filter.type);
        }

        if (filter.severity) {
            filteredErrors = filteredErrors.filter(error => error.severity === filter.severity);
        }

        if (filter.since) {
            const since = new Date(filter.since);
            filteredErrors = filteredErrors.filter(error => new Date(error.timestamp) >= since);
        }

        return filteredErrors;
    }

    clearErrors() {
        this.errors = [];
        localStorage.removeItem('app_errors');
    }

    getErrorStats() {
        const stats = {
            total: this.errors.length,
            byType: {},
            bySeverity: {},
            recent: this.errors.filter(error => 
                Date.now() - new Date(error.timestamp).getTime() < 3600000 // Last hour
            ).length
        };

        this.errors.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
            stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
        });

        return stats;
    }

    retryOperation(errorId) {
        const error = this.errors.find(e => e.id === errorId);
        if (error) {
            this.dispatchEvent('retry-operation', { error });
        }
    }

    // Configuration methods
    configure(config) {
        this.config = { ...this.config, ...config };
    }

    setLogLevel(level) {
        this.config.logLevel = level;
    }

    enableUserNotifications(enable = true) {
        this.config.enableUserNotifications = enable;
    }

    enableRecovery(enable = true) {
        this.config.enableRecovery = enable;
    }
}

// Export singleton instance
export const errorHandler = new ErrorHandler();

// Make it globally available
if (typeof window !== 'undefined') {
    window.ErrorHandler = ErrorHandler;
    window.errorHandler = errorHandler;
}