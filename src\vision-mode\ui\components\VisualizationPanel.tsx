/**
 * Visualization Panel Component
 * Interactive visualization components for semantic analysis, knowledge graphs, and behavioral patterns
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Network,
  Brain,
  Users,
  Eye,
  Filter,
  Download,
  Maximize2,
  RefreshCw,
  Settings,
  Layers,
  Search,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Play,
  Pause,
  Square
} from 'lucide-react';
import { dataStore } from '../../../services/DataStoreService';

// Agent type definition
interface VisionModeAgent {
  initialize(): Promise<void>;
  getStatus(): any;
}

/**
 * Visualization panel props
 */
export interface VisualizationPanelProps {
  visionAgent: VisionModeAgent | null;
  systemStatus: any;
  onStatusUpdate?: (status: any) => void;
  className?: string;
}

/**
 * Knowledge graph node
 */
export interface KnowledgeNode {
  id: string;
  label: string;
  type: 'entity' | 'concept' | 'document' | 'keyword' | 'relation';
  size: number;
  color: string;
  x?: number;
  y?: number;
  connections: string[];
  metadata: Record<string, any>;
}

/**
 * Knowledge graph edge
 */
export interface KnowledgeEdge {
  id: string;
  source: string;
  target: string;
  type: 'relates_to' | 'contains' | 'references' | 'similar_to' | 'part_of';
  weight: number;
  label?: string;
}

/**
 * Semantic analysis result
 */
export interface SemanticAnalysisData {
  entities: Array<{
    text: string;
    type: string;
    confidence: number;
    frequency: number;
  }>;
  topics: Array<{
    name: string;
    weight: number;
    keywords: string[];
  }>;
  sentiment: {
    positive: number;
    negative: number;
    neutral: number;
  };
  complexity: {
    readability: number;
    vocabulary: number;
    structure: number;
  };
}

/**
 * Behavioral pattern data
 */
export interface BehavioralPatternData {
  userJourneys: Array<{
    id: string;
    path: string[];
    frequency: number;
    duration: number;
    conversionRate: number;
  }>;
  interactions: Array<{
    type: string;
    count: number;
    avgDuration: number;
    successRate: number;
  }>;
  heatmap: Array<{
    x: number;
    y: number;
    intensity: number;
    element: string;
  }>;
  timePatterns: Array<{
    hour: number;
    activity: number;
    engagement: number;
  }>;
}

/**
 * Chart data point
 */
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: Record<string, any>;
}

/**
 * Visualization Panel Component
 */
export const VisualizationPanel: React.FC<VisualizationPanelProps> = ({
  visionAgent,
  systemStatus,
  onStatusUpdate,
  className = ''
}) => {
  const [activeView, setActiveView] = useState<'knowledge' | 'semantic' | 'behavioral'>('knowledge');
  const [knowledgeGraph, setKnowledgeGraph] = useState<{ nodes: KnowledgeNode[]; edges: KnowledgeEdge[] }>({ nodes: [], edges: [] });
  const [semanticData, setSemanticData] = useState<SemanticAnalysisData | null>(null);
  const [behavioralData, setBehavioralData] = useState<BehavioralPatternData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isPlaying, setIsPlaying] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);

  // Initialize knowledge graph data from processed documents
  useEffect(() => {
    const documents = dataStore.getAllDocuments();
    if (documents.length === 0) {
      setKnowledgeGraph({ nodes: [], edges: [] });
      return;
    }

    const knowledgeGraphData = dataStore.getKnowledgeGraphData();
    
    // Create nodes from entities and documents
    const nodes: KnowledgeNode[] = [];
    const edges: KnowledgeEdge[] = [];
    
    // Add document nodes
    documents.forEach((doc, index) => {
      nodes.push({
        id: `doc_${doc.id}`,
        label: doc.filename,
        type: 'document',
        size: Math.min(20, 10 + doc.pdfResult.metadata.pageCount / 5),
        color: '#3B82F6',
        connections: [],
        metadata: { 
          pages: doc.pdfResult.metadata.pageCount, 
          lastModified: doc.uploadedAt.toISOString().split('T')[0],
          wordCount: doc.pdfResult.wordCount
        }
      });
    });
    
    // Add entity nodes
    knowledgeGraphData.entities.forEach((entity, index) => {
      const nodeSize = Math.min(18, 8 + entity.frequency * 10);
      nodes.push({
        id: `entity_${entity.name.replace(/\s+/g, '_')}`,
        label: entity.name,
        type: 'entity',
        size: nodeSize,
        color: entity.type === 'PERSON' ? '#F59E0B' : 
               entity.type === 'ORGANIZATION' ? '#10B981' : 
               entity.type === 'LOCATION' ? '#EF4444' : '#8B5CF6',
        connections: [],
        metadata: { 
          type: entity.type, 
          frequency: entity.frequency,
          confidence: entity.frequency
        }
      });
    });
    
    // Add topic nodes
    documents.forEach(doc => {
      doc.semanticResult.topics.forEach((topic, index) => {
        const topicId = `topic_${topic.name.replace(/\s+/g, '_')}`;
        if (!nodes.find(n => n.id === topicId)) {
          nodes.push({
            id: topicId,
            label: topic.name,
            type: 'concept',
            size: Math.min(15, 8 + topic.relevance * 10),
            color: '#10B981',
            connections: [],
            metadata: { 
              weight: topic.relevance,
              keywords: topic.keywords || []
            }
          });
        }
      });
    });
    
    // Create edges from relationships
    knowledgeGraphData.relationships.forEach((rel, index) => {
      const sourceNode = nodes.find(n => n.label.toLowerCase() === rel.source);
      const targetNode = nodes.find(n => n.label.toLowerCase() === rel.target);
      
      if (sourceNode && targetNode) {
        edges.push({
          id: `edge_${index}`,
          source: sourceNode.id,
          target: targetNode.id,
          type: 'relates_to',
          weight: Math.min(1, rel.strength),
          label: 'relates to'
        });
      }
    });
    
    // Connect documents to their entities and topics
    documents.forEach(doc => {
      const docNode = nodes.find(n => n.id === `doc_${doc.id}`);
      if (!docNode) return;
      
      // Connect to entities
      doc.semanticResult.entities.forEach((entity, index) => {
        const entityNode = nodes.find(n => n.label.toLowerCase() === entity.text.toLowerCase());
        if (entityNode) {
          edges.push({
            id: `doc_entity_${doc.id}_${index}`,
            source: docNode.id,
            target: entityNode.id,
            type: 'contains',
            weight: entity.confidence,
            label: 'contains'
          });
        }
      });
      
      // Connect to topics
      doc.semanticResult.topics.forEach((topic, index) => {
        const topicNode = nodes.find(n => n.label.toLowerCase() === topic.name.toLowerCase());
        if (topicNode) {
          edges.push({
            id: `doc_topic_${doc.id}_${index}`,
            source: docNode.id,
            target: topicNode.id,
            type: 'references',
            weight: topic.relevance,
            label: 'discusses'
          });
        }
      });
    });
    
    setKnowledgeGraph({ nodes, edges });
    
    // Subscribe to data changes
    const unsubscribe = dataStore.subscribe(() => {
      // Refresh knowledge graph when data changes
      const updatedDocuments = dataStore.getAllDocuments();
      if (updatedDocuments.length !== documents.length) {
        // Re-run this effect by updating a dependency
        setIsLoading(prev => !prev);
      }
    });
    
    return unsubscribe;
  }, [isLoading]);

  // Initialize semantic analysis data from processed documents
  useEffect(() => {
    const documents = dataStore.getAllDocuments();
    if (documents.length === 0) {
      setSemanticData(null);
      return;
    }

    // Aggregate semantic data from all documents
    const allEntities = documents.flatMap(doc => doc.semanticResult.entities);
    const allTopics = documents.flatMap(doc => doc.semanticResult.topics);
    const allSentiments = documents.map(doc => doc.semanticResult.sentiment);
    const allComplexity = documents.map(doc => doc.semanticResult.complexity);

    // Aggregate entities by text and type
    const entityMap = new Map<string, { text: string; type: string; confidence: number; frequency: number }>();
    allEntities.forEach(entity => {
      const key = `${entity.text}_${entity.type}`;
      if (entityMap.has(key)) {
        const existing = entityMap.get(key)!;
        existing.confidence = Math.max(existing.confidence, entity.confidence);
        existing.frequency += 1;
      } else {
        entityMap.set(key, {
          text: entity.text,
          type: entity.type,
          confidence: entity.confidence,
          frequency: 1
        });
      }
    });

    // Aggregate topics by name
    const topicMap = new Map<string, { name: string; weight: number; keywords: string[] }>();
    allTopics.forEach(topic => {
      if (topicMap.has(topic.name)) {
        const existing = topicMap.get(topic.name)!;
        existing.weight = Math.max(existing.weight, topic.relevance);
        existing.keywords = [...new Set([...existing.keywords, ...(topic.keywords || [])])];
      } else {
        topicMap.set(topic.name, {
          name: topic.name,
          weight: topic.relevance,
          keywords: topic.keywords || []
        });
      }
    });

    // Calculate average sentiment
    const avgSentiment = allSentiments.reduce(
      (acc, sentiment) => ({
        positive: acc.positive + (sentiment.positive || 0),
        negative: acc.negative + (sentiment.negative || 0),
        neutral: acc.neutral + (sentiment.neutral || 0)
      }),
      { positive: 0, negative: 0, neutral: 0 }
    );
    
    const sentimentCount = allSentiments.length;
    if (sentimentCount > 0) {
      avgSentiment.positive = Math.round(avgSentiment.positive / sentimentCount);
      avgSentiment.negative = Math.round(avgSentiment.negative / sentimentCount);
      avgSentiment.neutral = Math.round(avgSentiment.neutral / sentimentCount);
    }

    // Calculate average complexity
    const avgComplexity = allComplexity.reduce(
      (acc, complexity) => ({
        readability: acc.readability + complexity.readability,
        vocabulary: acc.vocabulary + complexity.vocabulary,
        structure: acc.structure + complexity.structure
      }),
      { readability: 0, vocabulary: 0, structure: 0 }
    );
    
    const complexityCount = allComplexity.length;
    if (complexityCount > 0) {
      avgComplexity.readability = Math.round((avgComplexity.readability / complexityCount) * 100);
      avgComplexity.vocabulary = Math.round((avgComplexity.vocabulary / complexityCount) * 100);
      avgComplexity.structure = Math.round((avgComplexity.structure / complexityCount) * 100);
    }

    const semanticData: SemanticAnalysisData = {
      entities: Array.from(entityMap.values())
        .sort((a, b) => b.frequency - a.frequency)
        .slice(0, 10), // Top 10 entities
      topics: Array.from(topicMap.values())
        .sort((a, b) => b.weight - a.weight)
        .slice(0, 10), // Top 10 topics
      sentiment: avgSentiment,
      complexity: avgComplexity
    };

    setSemanticData(semanticData);
  }, []);

  // Initialize behavioral data from processed documents
  useEffect(() => {
    const documents = dataStore.getAllDocuments();
    if (documents.length === 0) {
      setBehavioralData(null);
      return;
    }

    const behavioralPatternsData = dataStore.getBehavioralPatternsData();
    
    // Generate user journeys based on document processing patterns
    const userJourneys = [
      { 
        id: 'j1', 
        path: ['home', 'upload', 'processing', 'vision-mode'], 
        frequency: documents.length, 
        duration: documents.reduce((sum, doc) => sum + doc.processingDuration, 0) / documents.length / 1000, 
        conversionRate: 0.95 
      },
      { 
        id: 'j2', 
        path: ['vision-mode', 'agents', 'analysis', 'export'], 
        frequency: Math.floor(documents.length * 0.7), 
        duration: 180, 
        conversionRate: 0.8 
      },
      { 
        id: 'j3', 
        path: ['dashboard', 'visualization', 'knowledge-graph'], 
        frequency: Math.floor(documents.length * 0.5), 
        duration: 120, 
        conversionRate: 0.75 
      }
    ];

    // Generate interaction data based on document complexity
    const avgComplexity = documents.reduce((sum, doc) => sum + doc.semanticResult.complexity.readability, 0) / documents.length;
    const interactions = [
      { type: 'upload', count: documents.length, avgDuration: 5.0, successRate: 1.0 },
      { type: 'analysis', count: documents.length, avgDuration: avgComplexity * 30, successRate: 0.95 },
      { type: 'visualization', count: Math.floor(documents.length * 0.8), avgDuration: 45.0, successRate: 0.9 },
      { type: 'export', count: Math.floor(documents.length * 0.6), avgDuration: 15.0, successRate: 0.85 }
    ];

    // Generate heatmap based on document types and sizes
    const heatmap = [
      { x: 100, y: 150, intensity: Math.min(1, documents.length / 10), element: 'upload-button' },
      { x: 300, y: 200, intensity: Math.min(1, documents.filter(d => d.pdfResult.wordCount > 1000).length / 5), element: 'analysis-panel' },
      { x: 500, y: 100, intensity: Math.min(1, documents.length / 8), element: 'vision-mode-link' },
      { x: 200, y: 400, intensity: Math.min(1, documents.filter(d => d.semanticResult.entities.length > 5).length / 3), element: 'knowledge-graph' }
    ];

    // Generate time patterns based on upload times
    const timePatterns = Array.from({ length: 24 }, (_, hour) => {
      const docsAtHour = documents.filter(doc => doc.uploadedAt.getHours() === hour).length;
      return {
        hour,
        activity: Math.min(100, docsAtHour * 20),
        engagement: Math.min(1, docsAtHour / Math.max(1, documents.length / 24))
      };
    }).filter(pattern => pattern.activity > 0);

    const behavioralData: BehavioralPatternData = {
      userJourneys,
      interactions,
      heatmap,
      timePatterns: timePatterns.length > 0 ? timePatterns : [
        { hour: new Date().getHours(), activity: 100, engagement: 1.0 }
      ]
    };

    setBehavioralData(behavioralData);
  }, []);

  // Knowledge graph rendering
  useEffect(() => {
    if (!canvasRef.current || activeView !== 'knowledge') return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const renderGraph = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Position nodes in a circle for demo
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      const radius = Math.min(centerX, centerY) * 0.7;
      
      knowledgeGraph.nodes.forEach((node, index) => {
        const angle = (index / knowledgeGraph.nodes.length) * 2 * Math.PI;
        node.x = centerX + Math.cos(angle) * radius;
        node.y = centerY + Math.sin(angle) * radius;
      });

      // Draw edges
      knowledgeGraph.edges.forEach(edge => {
        const sourceNode = knowledgeGraph.nodes.find(n => n.id === edge.source);
        const targetNode = knowledgeGraph.nodes.find(n => n.id === edge.target);
        
        if (sourceNode && targetNode && sourceNode.x && sourceNode.y && targetNode.x && targetNode.y) {
          ctx.beginPath();
          ctx.moveTo(sourceNode.x, sourceNode.y);
          ctx.lineTo(targetNode.x, targetNode.y);
          ctx.strokeStyle = `rgba(148, 163, 184, ${edge.weight})`;
          ctx.lineWidth = edge.weight * 3;
          ctx.stroke();
        }
      });

      // Draw nodes
      knowledgeGraph.nodes.forEach(node => {
        if (node.x && node.y) {
          ctx.beginPath();
          ctx.arc(node.x, node.y, node.size * zoomLevel, 0, 2 * Math.PI);
          ctx.fillStyle = selectedNode === node.id ? '#FBBF24' : node.color;
          ctx.fill();
          ctx.strokeStyle = '#1F2937';
          ctx.lineWidth = 2;
          ctx.stroke();
          
          // Draw label
          ctx.fillStyle = '#FFFFFF';
          ctx.font = `${12 * zoomLevel}px Arial`;
          ctx.textAlign = 'center';
          ctx.fillText(node.label, node.x, node.y + node.size * zoomLevel + 15);
        }
      });
    };

    renderGraph();
  }, [knowledgeGraph, selectedNode, zoomLevel, activeView]);

  // Animation loop for behavioral patterns
  useEffect(() => {
    if (!isPlaying || activeView !== 'behavioral') return;

    const animate = () => {
      // Animation logic for behavioral patterns
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isPlaying, activeView]);

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (activeView !== 'knowledge') return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Find clicked node
    const clickedNode = knowledgeGraph.nodes.find(node => {
      if (!node.x || !node.y) return false;
      const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
      return distance <= node.size * zoomLevel;
    });

    setSelectedNode(clickedNode ? clickedNode.id : null);
  };

  const handleZoom = (direction: 'in' | 'out') => {
    setZoomLevel(prev => {
      const newZoom = direction === 'in' ? prev * 1.2 : prev / 1.2;
      return Math.max(0.5, Math.min(3, newZoom));
    });
  };

  const renderSemanticCharts = () => {
    if (!semanticData) return null;

    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Entity Distribution */}
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
            <Brain className="w-5 h-5 text-blue-500" />
            <span>Entity Distribution</span>
          </h3>
          <div className="space-y-3">
            {semanticData.entities.map((entity, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium">{entity.text}</span>
                    <span className="text-xs text-gray-400">{entity.frequency}</span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${entity.confidence * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Topic Analysis */}
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
            <Layers className="w-5 h-5 text-green-500" />
            <span>Topic Analysis</span>
          </h3>
          <div className="space-y-4">
            {semanticData.topics.map((topic, index) => (
              <div key={index} className="">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{topic.name}</span>
                  <span className="text-sm text-gray-400">{(topic.weight * 100).toFixed(1)}%</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {topic.keywords.map((keyword, kIndex) => (
                    <span
                      key={kIndex}
                      className="px-2 py-1 bg-slate-700 text-xs rounded"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Sentiment Analysis */}
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-purple-500" />
            <span>Sentiment Analysis</span>
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-green-400">Positive</span>
              <span className="font-bold">{semanticData.sentiment.positive}%</span>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-3">
              <div
                className="bg-green-500 h-3 rounded-full"
                style={{ width: `${semanticData.sentiment.positive}%` }}
              />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-red-400">Negative</span>
              <span className="font-bold">{semanticData.sentiment.negative}%</span>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-3">
              <div
                className="bg-red-500 h-3 rounded-full"
                style={{ width: `${semanticData.sentiment.negative}%` }}
              />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Neutral</span>
              <span className="font-bold">{semanticData.sentiment.neutral}%</span>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-3">
              <div
                className="bg-gray-500 h-3 rounded-full"
                style={{ width: `${semanticData.sentiment.neutral}%` }}
              />
            </div>
          </div>
        </div>

        {/* Complexity Metrics */}
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-yellow-500" />
            <span>Complexity Metrics</span>
          </h3>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span>Readability</span>
                <span className="font-bold">{semanticData.complexity.readability}%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full"
                  style={{ width: `${semanticData.complexity.readability}%` }}
                />
              </div>
            </div>
            <div>
              <div className="flex items-center justify-between mb-1">
                <span>Vocabulary</span>
                <span className="font-bold">{semanticData.complexity.vocabulary}%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: `${semanticData.complexity.vocabulary}%` }}
                />
              </div>
            </div>
            <div>
              <div className="flex items-center justify-between mb-1">
                <span>Structure</span>
                <span className="font-bold">{semanticData.complexity.structure}%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div
                  className="bg-purple-500 h-2 rounded-full"
                  style={{ width: `${semanticData.complexity.structure}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderBehavioralCharts = () => {
    if (!behavioralData) return null;

    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Journeys */}
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
            <Users className="w-5 h-5 text-blue-500" />
            <span>User Journeys</span>
          </h3>
          <div className="space-y-3">
            {behavioralData.userJourneys.map((journey, index) => (
              <div key={journey.id} className="border border-slate-700 rounded p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">Journey {index + 1}</span>
                  <span className="text-sm text-gray-400">{journey.frequency} users</span>
                </div>
                <div className="flex items-center space-x-2 mb-2">
                  {journey.path.map((step, stepIndex) => (
                    <React.Fragment key={stepIndex}>
                      <span className="px-2 py-1 bg-blue-600 text-xs rounded">
                        {step}
                      </span>
                      {stepIndex < journey.path.length - 1 && (
                        <span className="text-gray-400">→</span>
                      )}
                    </React.Fragment>
                  ))}
                </div>
                <div className="flex items-center justify-between text-sm text-gray-400">
                  <span>Duration: {journey.duration}s</span>
                  <span>Conversion: {(journey.conversionRate * 100).toFixed(1)}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Interaction Types */}
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
            <Eye className="w-5 h-5 text-green-500" />
            <span>Interaction Types</span>
          </h3>
          <div className="space-y-4">
            {behavioralData.interactions.map((interaction, index) => (
              <div key={index} className="">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium capitalize">{interaction.type}</span>
                  <span className="text-sm text-gray-400">{interaction.count} events</span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Avg Duration:</span>
                    <span className="ml-2 font-medium">{interaction.avgDuration}s</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Success Rate:</span>
                    <span className="ml-2 font-medium">{(interaction.successRate * 100).toFixed(1)}%</span>
                  </div>
                </div>
                <div className="w-full bg-slate-700 rounded-full h-2 mt-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${interaction.successRate * 100}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Time Patterns */}
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700 lg:col-span-2">
          <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-purple-500" />
            <span>Activity Patterns</span>
          </h3>
          <div className="grid grid-cols-6 gap-2">
            {behavioralData.timePatterns.map((pattern, index) => (
              <div key={index} className="text-center">
                <div className="text-xs text-gray-400 mb-1">{pattern.hour}:00</div>
                <div className="bg-slate-700 rounded h-20 flex items-end justify-center">
                  <div
                    className="bg-purple-500 rounded-t w-full"
                    style={{ height: `${pattern.activity}%` }}
                  />
                </div>
                <div className="text-xs text-gray-400 mt-1">{pattern.activity}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Visualization Dashboard</h2>
          <p className="text-gray-400 mt-1">Interactive analysis and pattern visualization</p>
        </div>
        <div className="flex items-center space-x-4">
          {activeView === 'behavioral' && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsPlaying(!isPlaying)}
                className={`p-2 rounded transition-colors ${
                  isPlaying ? 'text-yellow-500 hover:bg-slate-700' : 'text-green-500 hover:bg-slate-700'
                }`}
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </button>
              <button
                onClick={() => setIsPlaying(false)}
                className="p-2 text-red-500 hover:bg-slate-700 rounded transition-colors"
              >
                <Square className="w-4 h-4" />
              </button>
            </div>
          )}
          <button className="flex items-center space-x-2 px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded transition-colors">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* View Tabs */}
      <div className="flex space-x-1 bg-slate-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveView('knowledge')}
          className={`flex-1 px-4 py-2 rounded-md transition-colors ${
            activeView === 'knowledge'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <div className="flex items-center justify-center space-x-2">
            <Network className="w-4 h-4" />
            <span>Knowledge Graph</span>
          </div>
        </button>
        <button
          onClick={() => setActiveView('semantic')}
          className={`flex-1 px-4 py-2 rounded-md transition-colors ${
            activeView === 'semantic'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <div className="flex items-center justify-center space-x-2">
            <Brain className="w-4 h-4" />
            <span>Semantic Analysis</span>
          </div>
        </button>
        <button
          onClick={() => setActiveView('behavioral')}
          className={`flex-1 px-4 py-2 rounded-md transition-colors ${
            activeView === 'behavioral'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <div className="flex items-center justify-center space-x-2">
            <Users className="w-4 h-4" />
            <span>Behavioral Patterns</span>
          </div>
        </button>
      </div>

      {/* Content */}
      {activeView === 'knowledge' && (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Knowledge Graph Canvas */}
          <div className="lg:col-span-3 bg-slate-800 rounded-lg border border-slate-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Knowledge Graph</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleZoom('out')}
                  className="p-1 text-gray-400 hover:text-white hover:bg-slate-700 rounded transition-colors"
                >
                  <ZoomOut className="w-4 h-4" />
                </button>
                <span className="text-sm text-gray-400">{(zoomLevel * 100).toFixed(0)}%</span>
                <button
                  onClick={() => handleZoom('in')}
                  className="p-1 text-gray-400 hover:text-white hover:bg-slate-700 rounded transition-colors"
                >
                  <ZoomIn className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setZoomLevel(1)}
                  className="p-1 text-gray-400 hover:text-white hover:bg-slate-700 rounded transition-colors"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </div>
            </div>
            <canvas
              ref={canvasRef}
              width={800}
              height={600}
              onClick={handleCanvasClick}
              className="w-full h-96 bg-slate-900 rounded cursor-pointer"
            />
          </div>

          {/* Node Details */}
          <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
            <h3 className="text-lg font-semibold mb-4">Node Details</h3>
            {selectedNode ? (
              <div className="space-y-3">
                {(() => {
                  const node = knowledgeGraph.nodes.find(n => n.id === selectedNode);
                  if (!node) return null;
                  return (
                    <>
                      <div>
                        <span className="text-gray-400">Label:</span>
                        <span className="ml-2 font-medium">{node.label}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Type:</span>
                        <span className="ml-2 font-medium capitalize">{node.type}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Connections:</span>
                        <span className="ml-2 font-medium">{node.connections.length}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Metadata:</span>
                        <div className="mt-2 space-y-1">
                          {Object.entries(node.metadata).map(([key, value]) => (
                            <div key={key} className="text-sm">
                              <span className="text-gray-500">{key}:</span>
                              <span className="ml-2">{String(value)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            ) : (
              <p className="text-gray-400">Click on a node to view details</p>
            )}
          </div>
        </div>
      )}

      {activeView === 'semantic' && renderSemanticCharts()}
      {activeView === 'behavioral' && renderBehavioralCharts()}
    </div>
  );
};

export default VisualizationPanel;