/**
 * Agent Conductor - Central orchestrator for multi-agent workflows
 * Manages agent registration, task distribution, and workflow coordination
 */

import { EventEmitter } from 'events';
import {
  AgentId,
  TaskId,
  MessageId,
  AgentMetadata,
  AgentTask,
  AgentMessage,
  AgentConfig,
  AgentContext,
  AgentStatus,
  AgentCapability,
  TaskStatus,
  TaskPriority,
  MessageType,
  AgentEvent,
  AgentEventType,
  AgentHealthCheck
} from '../agents/types.js';
import { IAgent } from '../agents/BaseAgent.js';

/**
 * Workflow definition for multi-agent collaboration
 */
export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  dependencies: Record<string, string[]>;
  timeout: number;
  retryPolicy: {
    maxRetries: number;
    backoffMultiplier: number;
  };
}

/**
 * Individual step in a workflow
 */
export interface WorkflowStep {
  id: string;
  name: string;
  agentCapability: AgentCapability;
  taskType: string;
  priority: TaskPriority;
  timeout: number;
  retries: number;
  dependencies: string[];
  parameters: Record<string, any>;
  outputMapping: Record<string, string>;
}

/**
 * Workflow execution context
 */
export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  currentStep?: string;
  stepResults: Record<string, any>;
  context: Record<string, any>;
  error?: string;
}

/**
 * Agent load balancing strategy
 */
export enum LoadBalancingStrategy {
  ROUND_ROBIN = 'round_robin',
  LEAST_LOADED = 'least_loaded',
  CAPABILITY_BASED = 'capability_based',
  PRIORITY_BASED = 'priority_based'
}

/**
 * Conductor configuration
 */
export interface ConductorConfig {
  maxConcurrentWorkflows: number;
  taskTimeout: number;
  heartbeatInterval: number;
  loadBalancingStrategy: LoadBalancingStrategy;
  enableMetrics: boolean;
  enableHealthChecks: boolean;
  retryPolicy: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
}

/**
 * Agent Conductor - Central orchestrator for the MCP system
 */
export class AgentConductor extends EventEmitter {
  private _config: ConductorConfig;
  private _agents: Map<AgentId, IAgent> = new Map();
  private _agentMetadata: Map<AgentId, AgentMetadata> = new Map();
  private _tasks: Map<TaskId, AgentTask> = new Map();
  private _workflows: Map<string, WorkflowDefinition> = new Map();
  private _workflowExecutions: Map<string, WorkflowExecution> = new Map();
  private _messageQueue: AgentMessage[] = [];
  private _isRunning = false;
  private _heartbeatInterval?: NodeJS.Timeout;
  private _taskProcessingInterval?: NodeJS.Timeout;
  private _roundRobinIndex = 0;

  constructor(config: Partial<ConductorConfig> = {}) {
    super();
    this._config = {
      maxConcurrentWorkflows: 10,
      taskTimeout: 300000, // 5 minutes
      heartbeatInterval: 30000, // 30 seconds
      loadBalancingStrategy: LoadBalancingStrategy.LEAST_LOADED,
      enableMetrics: true,
      enableHealthChecks: true,
      retryPolicy: {
        maxRetries: 3,
        backoffMultiplier: 2,
        initialDelay: 1000
      },
      ...config
    };
  }

  /**
   * Start the conductor
   */
  async start(): Promise<void> {
    if (this._isRunning) {
      return;
    }

    this._isRunning = true;
    this.startHeartbeat();
    this.startTaskProcessing();
    
    console.log('Agent Conductor started');
    this.emit('conductor-started');
  }

  /**
   * Stop the conductor
   */
  async stop(): Promise<void> {
    if (!this._isRunning) {
      return;
    }

    this._isRunning = false;
    this.stopHeartbeat();
    this.stopTaskProcessing();
    
    // Cancel all running workflows
    for (const execution of this._workflowExecutions.values()) {
      if (execution.status === 'running') {
        await this.cancelWorkflow(execution.id);
      }
    }

    console.log('Agent Conductor stopped');
    this.emit('conductor-stopped');
  }

  /**
   * Register an agent with the conductor
   */
  async registerAgent(agent: IAgent): Promise<void> {
    if (this._agents.has(agent.id)) {
      throw new Error(`Agent ${agent.id} is already registered`);
    }

    this._agents.set(agent.id, agent);
    this._agentMetadata.set(agent.id, agent.metadata);

    // Set up event listeners
    agent.on('agent-event', (event: AgentEvent) => {
      this.handleAgentEvent(event);
    });

    // Initialize and start the agent
    if (!agent.metadata.status || agent.metadata.status === AgentStatus.INITIALIZING) {
      await agent.initialize();
    }
    
    if (agent.metadata.status === AgentStatus.READY) {
      await agent.start();
    }

    console.log(`Agent ${agent.id} registered successfully`);
    this.emit('agent-registered', { agentId: agent.id, metadata: agent.metadata });
  }

  /**
   * Unregister an agent
   */
  async unregisterAgent(agentId: AgentId): Promise<void> {
    const agent = this._agents.get(agentId);
    if (!agent) {
      return;
    }

    // Cancel all tasks assigned to this agent
    for (const task of this._tasks.values()) {
      if (task.assignedAgentId === agentId && task.status === TaskStatus.IN_PROGRESS) {
        await this.cancelTask(task.id);
      }
    }

    await agent.stop();
    this._agents.delete(agentId);
    this._agentMetadata.delete(agentId);

    console.log(`Agent ${agentId} unregistered`);
    this.emit('agent-unregistered', { agentId });
  }

  /**
   * Register a workflow definition
   */
  registerWorkflow(workflow: WorkflowDefinition): void {
    this._workflows.set(workflow.id, workflow);
    console.log(`Workflow ${workflow.id} registered`);
    this.emit('workflow-registered', { workflowId: workflow.id });
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(
    workflowId: string, 
    context: Record<string, any> = {},
    executionId?: string
  ): Promise<string> {
    const workflow = this._workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    if (this._workflowExecutions.size >= this._config.maxConcurrentWorkflows) {
      throw new Error('Maximum concurrent workflows reached');
    }

    const execution: WorkflowExecution = {
      id: executionId || this.generateExecutionId(),
      workflowId,
      status: 'pending',
      startTime: new Date(),
      stepResults: {},
      context
    };

    this._workflowExecutions.set(execution.id, execution);
    
    // Start workflow execution asynchronously
    this.executeWorkflowSteps(execution).catch(error => {
      console.error(`Workflow ${execution.id} failed:`, error);
      execution.status = 'failed';
      execution.error = error.message;
      execution.endTime = new Date();
      this.emit('workflow-failed', { executionId: execution.id, error: error.message });
    });

    console.log(`Workflow ${workflowId} execution ${execution.id} started`);
    this.emit('workflow-started', { executionId: execution.id, workflowId });
    
    return execution.id;
  }

  /**
   * Cancel a workflow execution
   */
  async cancelWorkflow(executionId: string): Promise<void> {
    const execution = this._workflowExecutions.get(executionId);
    if (!execution || execution.status !== 'running') {
      return;
    }

    execution.status = 'cancelled';
    execution.endTime = new Date();

    // Cancel any running tasks for this workflow
    for (const task of this._tasks.values()) {
      if (task.payload.workflowExecutionId === executionId && task.status === TaskStatus.IN_PROGRESS) {
        await this.cancelTask(task.id);
      }
    }

    console.log(`Workflow execution ${executionId} cancelled`);
    this.emit('workflow-cancelled', { executionId });
  }

  /**
   * Submit a task for execution
   */
  async submitTask(task: Omit<AgentTask, 'id' | 'status' | 'createdAt'>): Promise<TaskId> {
    const fullTask: AgentTask = {
      ...task,
      id: this.generateTaskId(),
      status: TaskStatus.PENDING,
      createdAt: new Date()
    };

    this._tasks.set(fullTask.id, fullTask);
    console.log(`Task ${fullTask.id} submitted`);
    this.emit('task-submitted', { taskId: fullTask.id, task: fullTask });
    
    return fullTask.id;
  }

  /**
   * Cancel a task
   */
  async cancelTask(taskId: TaskId): Promise<void> {
    const task = this._tasks.get(taskId);
    if (!task) {
      return;
    }

    if (task.assignedAgentId && task.status === TaskStatus.IN_PROGRESS) {
      const agent = this._agents.get(task.assignedAgentId);
      if (agent) {
        await agent.cancelTask(taskId);
      }
    }

    task.status = TaskStatus.CANCELLED;
    console.log(`Task ${taskId} cancelled`);
    this.emit('task-cancelled', { taskId });
  }

  /**
   * Get workflow execution status
   */
  getWorkflowExecution(executionId: string): WorkflowExecution | undefined {
    return this._workflowExecutions.get(executionId);
  }

  /**
   * Get all registered agents
   */
  getAgents(): AgentMetadata[] {
    return Array.from(this._agentMetadata.values());
  }

  /**
   * Get agents by capability
   */
  getAgentsByCapability(capability: AgentCapability): AgentMetadata[] {
    return Array.from(this._agentMetadata.values())
      .filter(metadata => metadata.capabilities.includes(capability));
  }

  /**
   * Send message between agents
   */
  async routeMessage(message: AgentMessage): Promise<void> {
    if (message.receiverId === 'broadcast') {
      // Broadcast to all agents
      for (const agent of this._agents.values()) {
        if (agent.id !== message.senderId) {
          await agent.receiveMessage(message);
        }
      }
    } else {
      // Send to specific agent
      const targetAgent = this._agents.get(message.receiverId);
      if (targetAgent) {
        await targetAgent.receiveMessage(message);
      } else {
        console.warn(`Target agent ${message.receiverId} not found for message ${message.id}`);
      }
    }
  }

  /**
   * Perform health checks on all agents
   */
  async performHealthChecks(): Promise<Map<AgentId, AgentHealthCheck>> {
    const healthChecks = new Map<AgentId, AgentHealthCheck>();
    
    for (const [agentId, agent] of this._agents) {
      try {
        const startTime = Date.now();
        const health = await agent.getHealthStatus();
        health.responseTime = Date.now() - startTime;
        healthChecks.set(agentId, health);
      } catch (error) {
        healthChecks.set(agentId, {
          isHealthy: false,
          lastCheck: new Date(),
          responseTime: -1,
          errorCount: -1,
          details: { error: error instanceof Error ? error.message : String(error) }
        });
      }
    }
    
    return healthChecks;
  }

  // Private methods

  private async executeWorkflowSteps(execution: WorkflowExecution): Promise<void> {
    const workflow = this._workflows.get(execution.workflowId)!;
    execution.status = 'running';
    
    try {
      // Execute steps in dependency order
      const executedSteps = new Set<string>();
      const pendingSteps = new Set(workflow.steps.map(step => step.id));
      
      while (pendingSteps.size > 0) {
        let progressMade = false;
        
        for (const step of workflow.steps) {
          if (executedSteps.has(step.id) || !pendingSteps.has(step.id)) {
            continue;
          }
          
          // Check if all dependencies are satisfied
          const dependenciesSatisfied = step.dependencies.every(dep => executedSteps.has(dep));
          
          if (dependenciesSatisfied) {
            await this.executeWorkflowStep(execution, step);
            executedSteps.add(step.id);
            pendingSteps.delete(step.id);
            progressMade = true;
          }
        }
        
        if (!progressMade) {
          throw new Error('Workflow has circular dependencies or unsatisfiable dependencies');
        }
      }
      
      execution.status = 'completed';
      execution.endTime = new Date();
      console.log(`Workflow execution ${execution.id} completed`);
      this.emit('workflow-completed', { executionId: execution.id, results: execution.stepResults });
      
    } catch (error) {
      execution.status = 'failed';
      execution.error = error instanceof Error ? error.message : String(error);
      execution.endTime = new Date();
      throw error;
    }
  }

  private async executeWorkflowStep(execution: WorkflowExecution, step: WorkflowStep): Promise<void> {
    execution.currentStep = step.id;
    
    // Find suitable agent
    const agent = this.selectAgent(step.agentCapability, step.priority);
    if (!agent) {
      throw new Error(`No available agent found for capability ${step.agentCapability}`);
    }
    
    // Create task
    const task: AgentTask = {
      id: this.generateTaskId(),
      type: step.taskType,
      priority: step.priority,
      status: TaskStatus.PENDING,
      requesterId: 'conductor',
      assignedAgentId: agent.id,
      payload: {
        ...step.parameters,
        workflowExecutionId: execution.id,
        stepId: step.id,
        context: execution.context,
        stepResults: execution.stepResults
      },
      dependencies: [],
      timeout: step.timeout,
      retryCount: 0,
      maxRetries: step.retries,
      createdAt: new Date()
    };
    
    this._tasks.set(task.id, task);
    
    // Execute task
    const context: AgentContext = {
      taskId: task.id,
      requesterId: 'conductor',
      startTime: new Date(),
      timeout: step.timeout,
      correlationId: execution.id,
      metadata: { workflowStep: step.id }
    };
    
    try {
      task.status = TaskStatus.IN_PROGRESS;
      task.startedAt = new Date();
      
      const result = await agent.executeTask(task, context);
      
      task.status = TaskStatus.COMPLETED;
      task.completedAt = new Date();
      task.result = result;
      
      // Store step result with output mapping
      execution.stepResults[step.id] = result;
      
      // Apply output mapping to context
      for (const [outputKey, contextKey] of Object.entries(step.outputMapping)) {
        if (result && typeof result === 'object' && outputKey in result) {
          execution.context[contextKey] = result[outputKey];
        }
      }
      
    } catch (error) {
      task.status = TaskStatus.FAILED;
      task.error = error instanceof Error ? error.message : String(error);
      throw error;
    }
  }

  private selectAgent(capability: AgentCapability, priority: TaskPriority): IAgent | null {
    const candidateAgents = Array.from(this._agents.values())
      .filter(agent => {
        const metadata = this._agentMetadata.get(agent.id);
        return metadata && 
               metadata.capabilities.includes(capability) &&
               metadata.status === AgentStatus.READY &&
               metadata.currentTaskCount < metadata.maxConcurrentTasks;
      });
    
    if (candidateAgents.length === 0) {
      return null;
    }
    
    switch (this._config.loadBalancingStrategy) {
      case LoadBalancingStrategy.ROUND_ROBIN:
        const agent = candidateAgents[this._roundRobinIndex % candidateAgents.length];
        this._roundRobinIndex++;
        return agent;
        
      case LoadBalancingStrategy.LEAST_LOADED:
        return candidateAgents.reduce((least, current) => {
          const leastMetadata = this._agentMetadata.get(least.id)!;
          const currentMetadata = this._agentMetadata.get(current.id)!;
          return currentMetadata.currentTaskCount < leastMetadata.currentTaskCount ? current : least;
        });
        
      case LoadBalancingStrategy.CAPABILITY_BASED:
      case LoadBalancingStrategy.PRIORITY_BASED:
      default:
        return candidateAgents[0];
    }
  }

  private handleAgentEvent(event: AgentEvent): void {
    // Update agent metadata
    if (event.type === AgentEventType.AGENT_STATUS_CHANGED) {
      const metadata = this._agentMetadata.get(event.agentId);
      if (metadata) {
        metadata.status = event.data.newStatus;
        metadata.lastHeartbeat = new Date();
      }
    }
    
    // Forward event
    this.emit('agent-event', event);
  }

  private startHeartbeat(): void {
    this._heartbeatInterval = setInterval(async () => {
      if (this._config.enableHealthChecks) {
        const healthChecks = await this.performHealthChecks();
        this.emit('health-check-completed', { healthChecks });
      }
    }, this._config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this._heartbeatInterval) {
      clearInterval(this._heartbeatInterval);
      this._heartbeatInterval = undefined;
    }
  }

  private startTaskProcessing(): void {
    this._taskProcessingInterval = setInterval(() => {
      this.processMessageQueue();
    }, 1000); // Process every second
  }

  private stopTaskProcessing(): void {
    if (this._taskProcessingInterval) {
      clearInterval(this._taskProcessingInterval);
      this._taskProcessingInterval = undefined;
    }
  }

  private processMessageQueue(): void {
    while (this._messageQueue.length > 0) {
      const message = this._messageQueue.shift()!;
      this.routeMessage(message).catch(error => {
        console.error('Failed to route message:', error);
      });
    }
  }

  private generateTaskId(): TaskId {
    return `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateExecutionId(): string {
    return `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}