/**
 * Advanced Export Module
 * Handles advanced export options including React, Vue, Angular components, and design tool integrations
 */

export class AdvancedExport {
    constructor() {
        this.exportFormats = new Map();
        this.componentTemplates = new Map();
        this.designToolAdapters = new Map();
        this.isInitialized = false;
        
        this.initializeExportFormats();
    }

    async initialize() {
        try {
            this.setupExportFormats();
            this.setupComponentTemplates();
            this.setupDesignToolAdapters();
            this.isInitialized = true;
            console.log('✅ Advanced Export system initialized');
        } catch (error) {
            console.error('❌ Failed to initialize Advanced Export:', error);
            throw error;
        }
    }

    initializeExportFormats() {
        // Define supported export formats
        this.exportFormats.set('react', {
            name: 'React Component',
            extension: '.jsx',
            description: 'React functional component with hooks',
            icon: '⚛️',
            category: 'framework'
        });

        this.exportFormats.set('vue', {
            name: 'Vue Component',
            extension: '.vue',
            description: 'Vue 3 composition API component',
            icon: '🟢',
            category: 'framework'
        });

        this.exportFormats.set('angular', {
            name: 'Angular Component',
            extension: '.component.ts',
            description: 'Angular component with TypeScript',
            icon: '🔺',
            category: 'framework'
        });

        this.exportFormats.set('svelte', {
            name: 'Svelte Component',
            extension: '.svelte',
            description: 'Svelte component with reactive state',
            icon: '🧡',
            category: 'framework'
        });

        this.exportFormats.set('figma', {
            name: 'Figma Plugin',
            extension: '.figma.js',
            description: 'Figma plugin integration',
            icon: '🎨',
            category: 'design-tool'
        });

        this.exportFormats.set('sketch', {
            name: 'Sketch File',
            extension: '.sketch',
            description: 'Sketch file format',
            icon: '💎',
            category: 'design-tool'
        });

        this.exportFormats.set('adobe-xd', {
            name: 'Adobe XD',
            extension: '.xd',
            description: 'Adobe XD format',
            icon: '🎭',
            category: 'design-tool'
        });

        this.exportFormats.set('tailwind', {
            name: 'Tailwind CSS',
            extension: '.html',
            description: 'HTML with Tailwind CSS classes',
            icon: '🌊',
            category: 'css-framework'
        });

        this.exportFormats.set('styled-components', {
            name: 'Styled Components',
            extension: '.jsx',
            description: 'React with styled-components',
            icon: '💅',
            category: 'css-in-js'
        });

        this.exportFormats.set('emotion', {
            name: 'Emotion CSS',
            extension: '.jsx',
            description: 'React with Emotion CSS-in-JS',
            icon: '😊',
            category: 'css-in-js'
        });
    }

    setupExportFormats() {
        // Additional setup for export formats
        console.log(`📦 Loaded ${this.exportFormats.size} export formats`);
    }

    setupComponentTemplates() {
        // Setup component templates for different frameworks
        this.componentTemplates.set('react-functional', this.getReactFunctionalTemplate());
        if (typeof this.getReactClassTemplate === 'function') {
            this.componentTemplates.set('react-class', this.getReactClassTemplate());
        } else {
            console.warn('⚠️ getReactClassTemplate() not available – falling back to functional template');
            this.componentTemplates.set('react-class', this.getReactFunctionalTemplate());
        }
        this.componentTemplates.set('vue-composition', this.getVueCompositionTemplate());
        this.componentTemplates.set('vue-options', this.getVueOptionsTemplate());
        this.componentTemplates.set('angular-component', this.getAngularComponentTemplate());
        this.componentTemplates.set('svelte-component', this.getSvelteComponentTemplate());
    }

    setupDesignToolAdapters() {
        // Setup adapters for design tools
        this.designToolAdapters.set('figma', new FigmaAdapter());
        this.designToolAdapters.set('sketch', new SketchAdapter());
        this.designToolAdapters.set('adobe-xd', new AdobeXDAdapter());
    }

    // React Component Generation
    async exportToReact(recreationData, options = {}) {
        try {
            const componentName = options.componentName || 'RecreatedComponent';
            const useHooks = options.useHooks !== false;
            const includeProps = options.includeProps !== false;
            const includeStyles = options.includeStyles !== false;

            const template = useHooks ? 
                this.componentTemplates.get('react-functional') : 
                this.componentTemplates.get('react-class');

            const componentCode = this.generateReactComponent(
                recreationData, 
                componentName, 
                template, 
                { useHooks, includeProps, includeStyles }
            );

            const files = {
                [`${componentName}.jsx`]: componentCode,
                [`${componentName}.module.css`]: this.generateReactStyles(recreationData),
                [`${componentName}.stories.jsx`]: this.generateStorybookStory(componentName, recreationData),
                [`${componentName}.test.jsx`]: this.generateReactTests(componentName),
                'package.json': this.generateReactPackageJson(componentName),
                'README.md': this.generateReactReadme(componentName, recreationData)
            };

            return {
                success: true,
                format: 'react',
                files,
                metadata: {
                    componentName,
                    framework: 'React',
                    version: '18.x',
                    dependencies: this.getReactDependencies(options)
                }
            };
        } catch (error) {
            console.error('React export failed:', error);
            throw error;
        }
    }

    generateReactComponent(recreationData, componentName, template, options) {
        const { elements, colors, metadata } = recreationData;
        const { useHooks, includeProps, includeStyles } = options;

        // Generate props interface
        const propsInterface = includeProps ? this.generateReactPropsInterface(elements) : '';
        
        // Generate component state
        const stateLogic = useHooks ? 
            this.generateReactHooksState(elements) : 
            this.generateReactClassState(elements);

        // Generate render logic
        const renderLogic = this.generateReactRenderLogic(elements, colors);

        // Generate styles import
        const stylesImport = includeStyles ? `import styles from './${componentName}.module.css';` : '';

        return template
            .replace('{{COMPONENT_NAME}}', componentName)
            .replace('{{PROPS_INTERFACE}}', propsInterface)
            .replace('{{STYLES_IMPORT}}', stylesImport)
            .replace('{{STATE_LOGIC}}', stateLogic)
            .replace('{{RENDER_LOGIC}}', renderLogic)
            .replace('{{METADATA_COMMENT}}', this.generateMetadataComment(metadata));
    }

    generateReactPropsInterface(elements) {
        const propTypes = new Set();
        
        elements.forEach(element => {
            if (element.interactive) propTypes.add('onElementClick?: (elementId: string) => void');
            if (element.type === 'text') propTypes.add('customText?: string');
            if (element.style?.color) propTypes.add('primaryColor?: string');
        });

        return `interface ${componentName}Props {
  className?: string;
  ${Array.from(propTypes).join(';\n  ')};
}`;
    }

    generateReactHooksState(elements) {
        const hasInteractiveElements = elements.some(el => el.interactive);
        const hasAnimations = elements.some(el => el.animations);

        let stateHooks = [];

        if (hasInteractiveElements) {
            stateHooks.push('const [selectedElement, setSelectedElement] = useState(null);');
        }

        if (hasAnimations) {
            stateHooks.push('const [isAnimating, setIsAnimating] = useState(false);');
        }

        stateHooks.push('const [isLoaded, setIsLoaded] = useState(false);');

        return stateHooks.join('\n  ');
    }

    generateReactRenderLogic(elements, colors) {
        const svgElements = elements.map(element => {
            return this.generateReactSVGElement(element);
        }).join('\n      ');

        return `<svg
      className={styles.canvas}
      viewBox="0 0 ${elements.canvasWidth || 800} ${elements.canvasHeight || 600}"
      xmlns="http://www.w3.org/2000/svg"
    >
      ${svgElements}
    </svg>`;
    }

    generateReactSVGElement(element) {
        const { type, properties, style } = element;
        
        switch (type) {
            case 'rectangle':
                return `<rect
        x="${properties.x}"
        y="${properties.y}"
        width="${properties.width}"
        height="${properties.height}"
        fill="${style.fill || '#000000'}"
        stroke="${style.stroke || 'none'}"
        strokeWidth="${style.strokeWidth || 0}"
        onClick={() => handleElementClick('${element.id}')}
      />`;
            
            case 'circle':
                return `<circle
        cx="${properties.cx}"
        cy="${properties.cy}"
        r="${properties.r}"
        fill="${style.fill || '#000000'}"
        stroke="${style.stroke || 'none'}"
        strokeWidth="${style.strokeWidth || 0}"
        onClick={() => handleElementClick('${element.id}')}
      />`;
            
            case 'text':
                return `<text
        x="${properties.x}"
        y="${properties.y}"
        fontSize="${style.fontSize || 16}"
        fill="${style.fill || '#000000'}"
        fontFamily="${style.fontFamily || 'Arial'}"
        onClick={() => handleElementClick('${element.id}')}
      >
        ${properties.text || 'Sample Text'}
      </text>`;
            
            default:
                return `<!-- Unsupported element type: ${type} -->`;
        }
    }

    // Vue Component Generation
    async exportToVue(recreationData, options = {}) {
        try {
            const componentName = options.componentName || 'RecreatedComponent';
            const useComposition = options.useComposition !== false;
            const includeTypeScript = options.includeTypeScript || false;

            const template = useComposition ? 
                this.componentTemplates.get('vue-composition') : 
                this.componentTemplates.get('vue-options');

            const componentCode = this.generateVueComponent(
                recreationData, 
                componentName, 
                template, 
                { useComposition, includeTypeScript }
            );

            const files = {
                [`${componentName}.vue`]: componentCode,
                [`${componentName}.stories.js`]: this.generateVueStorybookStory(componentName, recreationData),
                [`${componentName}.spec.js`]: this.generateVueTests(componentName),
                'package.json': this.generateVuePackageJson(componentName),
                'README.md': this.generateVueReadme(componentName, recreationData)
            };

            return {
                success: true,
                format: 'vue',
                files,
                metadata: {
                    componentName,
                    framework: 'Vue',
                    version: '3.x',
                    dependencies: this.getVueDependencies(options)
                }
            };
        } catch (error) {
            console.error('Vue export failed:', error);
            throw error;
        }
    }

    // Angular Component Generation
    async exportToAngular(recreationData, options = {}) {
        try {
            const componentName = options.componentName || 'RecreatedComponent';
            const includeModule = options.includeModule !== false;
            const includeService = options.includeService || false;

            const template = this.componentTemplates.get('angular-component');

            const componentCode = this.generateAngularComponent(
                recreationData, 
                componentName, 
                template, 
                { includeModule, includeService }
            );

            const files = {
                [`${componentName.toLowerCase()}.component.ts`]: componentCode,
                [`${componentName.toLowerCase()}.component.html`]: this.generateAngularTemplate(recreationData),
                [`${componentName.toLowerCase()}.component.scss`]: this.generateAngularStyles(recreationData),
                [`${componentName.toLowerCase()}.component.spec.ts`]: this.generateAngularTests(componentName),
                [`${componentName.toLowerCase()}.module.ts`]: includeModule ? this.generateAngularModule(componentName) : null,
                'package.json': this.generateAngularPackageJson(componentName),
                'README.md': this.generateAngularReadme(componentName, recreationData)
            };

            // Remove null files
            Object.keys(files).forEach(key => {
                if (files[key] === null) delete files[key];
            });

            return {
                success: true,
                format: 'angular',
                files,
                metadata: {
                    componentName,
                    framework: 'Angular',
                    version: '17.x',
                    dependencies: this.getAngularDependencies(options)
                }
            };
        } catch (error) {
            console.error('Angular export failed:', error);
            throw error;
        }
    }

    // Figma Plugin Integration
    async exportToFigma(recreationData, options = {}) {
        try {
            const adapter = this.designToolAdapters.get('figma');
            const figmaNodes = await adapter.convertToFigmaNodes(recreationData);
            
            const pluginCode = this.generateFigmaPlugin(figmaNodes, options);
            const manifestCode = this.generateFigmaManifest(options);

            const files = {
                'code.js': pluginCode,
                'manifest.json': manifestCode,
                'ui.html': this.generateFigmaUI(recreationData),
                'ui.js': this.generateFigmaUIScript(),
                'README.md': this.generateFigmaReadme(recreationData)
            };

            return {
                success: true,
                format: 'figma',
                files,
                metadata: {
                    pluginName: options.pluginName || 'Recreation Importer',
                    version: '1.0.0',
                    figmaAPI: 'Plugin API 1.0'
                }
            };
        } catch (error) {
            console.error('Figma export failed:', error);
            throw error;
        }
    }

    // Sketch File Export
    async exportToSketch(recreationData, options = {}) {
        try {
            const adapter = this.designToolAdapters.get('sketch');
            const sketchDocument = await adapter.convertToSketchDocument(recreationData);
            
            return {
                success: true,
                format: 'sketch',
                files: {
                    'document.json': JSON.stringify(sketchDocument, null, 2),
                    'meta.json': this.generateSketchMeta(recreationData),
                    'user.json': this.generateSketchUser()
                },
                metadata: {
                    format: 'Sketch',
                    version: '99.0',
                    compatibility: 'Sketch 70+'
                }
            };
        } catch (error) {
            console.error('Sketch export failed:', error);
            throw error;
        }
    }

    // Tailwind CSS Export
    async exportToTailwind(recreationData, options = {}) {
        try {
            const htmlCode = this.generateTailwindHTML(recreationData, options);
            const configCode = this.generateTailwindConfig(recreationData);
            
            const files = {
                'index.html': htmlCode,
                'tailwind.config.js': configCode,
                'styles.css': this.generateTailwindStyles(recreationData),
                'package.json': this.generateTailwindPackageJson(),
                'README.md': this.generateTailwindReadme(recreationData)
            };

            return {
                success: true,
                format: 'tailwind',
                files,
                metadata: {
                    framework: 'Tailwind CSS',
                    version: '3.x',
                    responsive: true
                }
            };
        } catch (error) {
            console.error('Tailwind export failed:', error);
            throw error;
        }
    }

    // Template Methods
    getReactFunctionalTemplate() {
        return `{{METADATA_COMMENT}}
import React, { useState, useEffect } from 'react';
{{STYLES_IMPORT}}

{{PROPS_INTERFACE}}

const {{COMPONENT_NAME}}: React.FC<{{COMPONENT_NAME}}Props> = (props) => {
  {{STATE_LOGIC}}

  const handleElementClick = (elementId: string) => {
    setSelectedElement(elementId);
    props.onElementClick?.(elementId);
  };

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className={props.className}>
      {{RENDER_LOGIC}}
    </div>
  );
};

export default {{COMPONENT_NAME}};`;
    }

    getVueCompositionTemplate() {
        return `{{METADATA_COMMENT}}
<template>
  <div :class="className">
    {{RENDER_LOGIC}}
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

{{PROPS_INTERFACE}}

const props = defineProps<{{COMPONENT_NAME}}Props>();
const emit = defineEmits<{
  elementClick: [elementId: string];
}>();

{{STATE_LOGIC}}

const handleElementClick = (elementId: string) => {
  selectedElement.value = elementId;
  emit('elementClick', elementId);
};

onMounted(() => {
  isLoaded.value = true;
});
</script>

<style scoped>
{{COMPONENT_STYLES}}
</style>`;
    }

    getAngularComponentTemplate() {
        return `{{METADATA_COMMENT}}
import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';

{{PROPS_INTERFACE}}

@Component({
  selector: 'app-{{COMPONENT_SELECTOR}}',
  templateUrl: './{{COMPONENT_SELECTOR}}.component.html',
  styleUrls: ['./{{COMPONENT_SELECTOR}}.component.scss']
})
export class {{COMPONENT_NAME}}Component implements OnInit {
  @Input() className?: string;
  @Output() elementClick = new EventEmitter<string>();

  {{STATE_LOGIC}}

  ngOnInit(): void {
    this.isLoaded = true;
  }

  handleElementClick(elementId: string): void {
    this.selectedElement = elementId;
    this.elementClick.emit(elementId);
  }
}`;
    }

    getReactClassTemplate() {
        return `{{METADATA_COMMENT}}
import React, { Component } from 'react';
{{STYLES_IMPORT}}

{{PROPS_INTERFACE}}

class {{COMPONENT_NAME}} extends Component {
  constructor(props) {
    super(props);
    {{STATE_LOGIC}}
  }

  handleElementClick = (elementId) => {
    this.setState({ selectedElement: elementId });
    this.props.onElementClick?.(elementId);
  };

  componentDidMount() {
    this.setState({ isLoaded: true });
  }

  render() {
    return (
      <div className={this.props.className}>
        {{RENDER_LOGIC}}
      </div>
    );
  }
}

export default {{COMPONENT_NAME}};`;
    }

    getVueOptionsTemplate() {
        return `{{METADATA_COMMENT}}
<template>
  <div :class="className">
    {{RENDER_LOGIC}}
  </div>
</template>

<script>
export default {
  name: '{{COMPONENT_NAME}}',
  props: {
    className: String,
    onElementClick: Function
  },
  data() {
    return {
      {{STATE_LOGIC}}
    };
  },
  methods: {
    handleElementClick(elementId) {
      this.selectedElement = elementId;
      this.$emit('elementClick', elementId);
    }
  },
  mounted() {
    this.isLoaded = true;
  }
};
</script>

<style scoped>
{{COMPONENT_STYLES}}
</style>`;
    }

    getSvelteComponentTemplate() {
        return `{{METADATA_COMMENT}}
<script>
  import { onMount } from 'svelte';
  
  export let className = '';
  export let onElementClick = () => {};
  
  {{STATE_LOGIC}}
  
  function handleElementClick(elementId) {
    selectedElement = elementId;
    onElementClick(elementId);
  }
  
  onMount(() => {
    isLoaded = true;
  });
</script>

<div class={className}>
  {{RENDER_LOGIC}}
</div>

<style>
  {{COMPONENT_STYLES}}
</style>`;
    }

    // Utility Methods
    generateMetadataComment(metadata) {
        return `/**
 * Auto-generated component from Image Recreation App
 * Generated: ${new Date().toISOString()}
 * Elements: ${metadata?.elements?.total || 0}
 * Colors: ${metadata?.colors || 0}
 */`;
    }

    generateReactStyles(recreationData) {
        const { colors } = recreationData;
        
        let cssVariables = colors.map((color, index) => 
            `  --color-${index + 1}: ${color.hex};`
        ).join('\n');

        return `.canvas {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: block;
}

:root {
${cssVariables}
}

.element {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.element:hover {
  opacity: 0.8;
}

.selected {
  filter: drop-shadow(0 0 5px rgba(0, 123, 255, 0.8));
}`;
    }

    async exportComponent(recreationData, format, options = {}) {
        switch (format) {
            case 'react':
                return await this.exportToReact(recreationData, options);
            case 'vue':
                return await this.exportToVue(recreationData, options);
            case 'angular':
                return await this.exportToAngular(recreationData, options);
            case 'svelte':
                return await this.exportToSvelte(recreationData, options);
            case 'figma':
                return await this.exportToFigma(recreationData, options);
            case 'sketch':
                return await this.exportToSketch(recreationData, options);
            case 'tailwind':
                return await this.exportToTailwind(recreationData, options);
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }

    getAvailableFormats() {
        return Array.from(this.exportFormats.entries()).map(([key, format]) => ({
            id: key,
            ...format
        }));
    }

    getFormatsByCategory(category) {
        return this.getAvailableFormats().filter(format => format.category === category);
    }
}

// Design Tool Adapters
class FigmaAdapter {
    async convertToFigmaNodes(recreationData) {
        const { elements } = recreationData;
        
        return elements.map(element => {
            switch (element.type) {
                case 'rectangle':
                    return {
                        type: 'RECTANGLE',
                        x: element.properties.x,
                        y: element.properties.y,
                        width: element.properties.width,
                        height: element.properties.height,
                        fills: [{
                            type: 'SOLID',
                            color: this.hexToRgb(element.style.fill)
                        }]
                    };
                case 'circle':
                    return {
                        type: 'ELLIPSE',
                        x: element.properties.cx - element.properties.r,
                        y: element.properties.cy - element.properties.r,
                        width: element.properties.r * 2,
                        height: element.properties.r * 2,
                        fills: [{
                            type: 'SOLID',
                            color: this.hexToRgb(element.style.fill)
                        }]
                    };
                case 'text':
                    return {
                        type: 'TEXT',
                        x: element.properties.x,
                        y: element.properties.y,
                        characters: element.properties.text,
                        fontSize: element.style.fontSize,
                        fills: [{
                            type: 'SOLID',
                            color: this.hexToRgb(element.style.fill)
                        }]
                    };
                default:
                    return null;
            }
        }).filter(node => node !== null);
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16) / 255,
            g: parseInt(result[2], 16) / 255,
            b: parseInt(result[3], 16) / 255
        } : { r: 0, g: 0, b: 0 };
    }
}

class SketchAdapter {
    async convertToSketchDocument(recreationData) {
        const { elements, metadata } = recreationData;
        
        return {
            _class: 'document',
            do_objectID: this.generateUUID(),
            assets: {
                _class: 'assetCollection',
                colors: [],
                gradients: [],
                images: []
            },
            colorSpace: 0,
            currentPageIndex: 0,
            foreignSymbols: [],
            layerStyles: {
                _class: 'sharedStyleContainer',
                objects: []
            },
            pages: [{
                _class: 'page',
                do_objectID: this.generateUUID(),
                name: 'Recreation',
                layers: elements.map(element => this.convertElementToSketchLayer(element))
            }]
        };
    }

    convertElementToSketchLayer(element) {
        const baseLayer = {
            do_objectID: this.generateUUID(),
            name: element.id,
            isVisible: true,
            isLocked: false,
            frame: {
                _class: 'rect',
                x: element.properties.x || 0,
                y: element.properties.y || 0,
                width: element.properties.width || 100,
                height: element.properties.height || 100
            }
        };

        switch (element.type) {
            case 'rectangle':
                return {
                    ...baseLayer,
                    _class: 'rectangle',
                    style: {
                        _class: 'style',
                        fills: [{
                            _class: 'fill',
                            isEnabled: true,
                            color: this.hexToSketchColor(element.style.fill)
                        }]
                    }
                };
            case 'circle':
                return {
                    ...baseLayer,
                    _class: 'oval',
                    style: {
                        _class: 'style',
                        fills: [{
                            _class: 'fill',
                            isEnabled: true,
                            color: this.hexToSketchColor(element.style.fill)
                        }]
                    }
                };
            case 'text':
                return {
                    ...baseLayer,
                    _class: 'text',
                    attributedString: {
                        _class: 'attributedString',
                        string: element.properties.text,
                        attributes: [{
                            _class: 'stringAttribute',
                            location: 0,
                            length: element.properties.text.length,
                            attributes: {
                                MSAttributedStringFontAttribute: {
                                    _class: 'fontDescriptor',
                                    attributes: {
                                        name: element.style.fontFamily || 'Arial',
                                        size: element.style.fontSize || 16
                                    }
                                },
                                MSAttributedStringColorAttribute: this.hexToSketchColor(element.style.fill)
                            }
                        }]
                    }
                };
            default:
                return baseLayer;
        }
    }

    hexToSketchColor(hex) {
        const rgb = this.hexToRgb(hex);
        return {
            _class: 'color',
            red: rgb.r / 255,
            green: rgb.g / 255,
            blue: rgb.b / 255,
            alpha: 1
        };
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : { r: 0, g: 0, b: 0 };
    }

    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
}

class AdobeXDAdapter {
    async convertToXDDocument(recreationData) {
        // Adobe XD format implementation
        // This would require specific XD format knowledge
        return {
            format: 'adobe-xd',
            version: '1.0',
            elements: recreationData.elements
        };
    }
}

// Create and export singleton instance
export const advancedExport = new AdvancedExport();

// Export the class as default
export default AdvancedExport;

// Make it globally available
if (typeof window !== 'undefined') {
    window.advancedExport = advancedExport;
}