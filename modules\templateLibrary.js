/**
 * Template Library System
 * Provides searchable template database, categorization, sharing, and recommendation features
 */

class TemplateLibrary {
    constructor() {
        this.templates = new Map();
        this.categories = new Map();
        this.tags = new Set();
        this.userTemplates = new Map();
        this.favorites = new Set();
        this.searchIndex = new Map();
        this.recommendations = [];
        this.isInitialized = false;
        this.analytics = {
            searches: 0,
            downloads: 0,
            uploads: 0,
            favorites: 0,
            views: 0
        };
        
        // Template metadata structure
        this.templateSchema = {
            id: '',
            name: '',
            description: '',
            category: '',
            tags: [],
            author: '',
            version: '1.0.0',
            created: null,
            updated: null,
            downloads: 0,
            rating: 0,
            reviews: [],
            thumbnail: '',
            preview: '',
            files: {
                html: '',
                css: '',
                js: '',
                assets: []
            },
            metadata: {
                elements: [],
                colors: [],
                fonts: [],
                layout: '',
                complexity: 'medium',
                responsive: true,
                accessibility: true
            },
            license: 'MIT',
            featured: false,
            verified: false
        };
    }

    async initialize() {
        try {
            console.log('🔧 Initializing Template Library System...');
            
            // Initialize default categories
            await this.initializeCategories();
            
            // Load built-in templates
            await this.loadBuiltInTemplates();
            
            // Load user templates from localStorage
            await this.loadUserTemplates();
            
            // Build search index
            await this.buildSearchIndex();
            
            // Initialize recommendation engine
            await this.initializeRecommendations();
            
            this.isInitialized = true;
            console.log('✅ Template Library System initialized successfully');
            
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Template Library:', error);
            throw error;
        }
    }

    async initializeCategories() {
        const defaultCategories = [
            {
                id: 'landing-pages',
                name: 'Landing Pages',
                description: 'Professional landing page templates',
                icon: '🚀',
                color: '#4CAF50'
            },
            {
                id: 'portfolios',
                name: 'Portfolios',
                description: 'Creative portfolio layouts',
                icon: '🎨',
                color: '#FF9800'
            },
            {
                id: 'dashboards',
                name: 'Dashboards',
                description: 'Data visualization and admin panels',
                icon: '📊',
                color: '#2196F3'
            },
            {
                id: 'e-commerce',
                name: 'E-commerce',
                description: 'Online store and product pages',
                icon: '🛒',
                color: '#9C27B0'
            },
            {
                id: 'blogs',
                name: 'Blogs',
                description: 'Blog and content layouts',
                icon: '📝',
                color: '#607D8B'
            },
            {
                id: 'forms',
                name: 'Forms',
                description: 'Contact and registration forms',
                icon: '📋',
                color: '#795548'
            },
            {
                id: 'navigation',
                name: 'Navigation',
                description: 'Headers, menus, and navigation components',
                icon: '🧭',
                color: '#3F51B5'
            },
            {
                id: 'cards',
                name: 'Cards',
                description: 'Card layouts and components',
                icon: '🃏',
                color: '#E91E63'
            },
            {
                id: 'mobile',
                name: 'Mobile',
                description: 'Mobile-first responsive designs',
                icon: '📱',
                color: '#00BCD4'
            },
            {
                id: 'experimental',
                name: 'Experimental',
                description: 'Cutting-edge and experimental designs',
                icon: '🧪',
                color: '#FF5722'
            }
        ];

        defaultCategories.forEach(category => {
            this.categories.set(category.id, category);
        });
    }

    async loadBuiltInTemplates() {
        const builtInTemplates = [
            {
                id: 'modern-landing',
                name: 'Modern Landing Page',
                description: 'Clean, modern landing page with hero section and features',
                category: 'landing-pages',
                tags: ['modern', 'clean', 'hero', 'features', 'responsive'],
                author: 'Template Library',
                version: '1.0.0',
                created: new Date('2024-01-01'),
                updated: new Date('2024-01-15'),
                downloads: 1250,
                rating: 4.8,
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjZjhmOWZhIi8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjI2MCIgaGVpZ2h0PSI2MCIgZmlsbD0iIzMzNzNkYyIgcng9IjgiLz4KPHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+',
                files: {
                    html: this.generateModernLandingHTML(),
                    css: this.generateModernLandingCSS(),
                    js: this.generateModernLandingJS(),
                    assets: []
                },
                metadata: {
                    elements: ['header', 'hero', 'features', 'footer'],
                    colors: ['#3373dc', '#f8f9fa', '#ffffff', '#6c757d'],
                    fonts: ['Inter', 'system-ui'],
                    layout: 'single-page',
                    complexity: 'medium',
                    responsive: true,
                    accessibility: true
                },
                license: 'MIT',
                featured: true,
                verified: true
            },
            {
                id: 'portfolio-grid',
                name: 'Portfolio Grid',
                description: 'Responsive portfolio grid with image gallery',
                category: 'portfolios',
                tags: ['portfolio', 'grid', 'gallery', 'responsive', 'masonry'],
                author: 'Template Library',
                version: '1.2.0',
                created: new Date('2024-01-05'),
                updated: new Date('2024-01-20'),
                downloads: 890,
                rating: 4.6,
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+',
                files: {
                    html: this.generatePortfolioGridHTML(),
                    css: this.generatePortfolioGridCSS(),
                    js: this.generatePortfolioGridJS(),
                    assets: []
                },
                metadata: {
                    elements: ['grid', 'cards', 'modal', 'filter'],
                    colors: ['#1a1a1a', '#ffffff', '#f0f0f0', '#ff6b6b'],
                    fonts: ['Poppins', 'sans-serif'],
                    layout: 'grid',
                    complexity: 'high',
                    responsive: true,
                    accessibility: true
                },
                license: 'MIT',
                featured: true,
                verified: true
            },
            {
                id: 'dashboard-analytics',
                name: 'Analytics Dashboard',
                description: 'Modern analytics dashboard with charts and metrics',
                category: 'dashboards',
                tags: ['dashboard', 'analytics', 'charts', 'metrics', 'admin'],
                author: 'Template Library',
                version: '1.1.0',
                created: new Date('2024-01-10'),
                updated: new Date('2024-01-25'),
                downloads: 650,
                rating: 4.9,
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+',
                files: {
                    html: this.generateDashboardHTML(),
                    css: this.generateDashboardCSS(),
                    js: this.generateDashboardJS(),
                    assets: []
                },
                metadata: {
                    elements: ['sidebar', 'charts', 'cards', 'tables'],
                    colors: ['#1e293b', '#0f172a', '#3b82f6', '#10b981'],
                    fonts: ['Inter', 'monospace'],
                    layout: 'sidebar',
                    complexity: 'high',
                    responsive: true,
                    accessibility: true
                },
                license: 'MIT',
                featured: false,
                verified: true
            }
        ];

        builtInTemplates.forEach(template => {
            this.templates.set(template.id, template);
            template.tags.forEach(tag => this.tags.add(tag));
        });
    }

    async loadUserTemplates() {
        try {
            const savedTemplates = localStorage.getItem('userTemplates');
            if (savedTemplates) {
                const userTemplates = JSON.parse(savedTemplates);
                userTemplates.forEach(template => {
                    this.userTemplates.set(template.id, template);
                    template.tags.forEach(tag => this.tags.add(tag));
                });
            }

            const savedFavorites = localStorage.getItem('favoriteTemplates');
            if (savedFavorites) {
                const favorites = JSON.parse(savedFavorites);
                favorites.forEach(id => this.favorites.add(id));
            }
        } catch (error) {
            console.warn('Failed to load user templates:', error);
        }
    }

    async buildSearchIndex() {
        const allTemplates = [...this.templates.values(), ...this.userTemplates.values()];
        
        allTemplates.forEach(template => {
            // Index by name
            const nameWords = template.name.toLowerCase().split(' ');
            nameWords.forEach(word => {
                if (!this.searchIndex.has(word)) {
                    this.searchIndex.set(word, new Set());
                }
                this.searchIndex.get(word).add(template.id);
            });

            // Index by description
            const descWords = template.description.toLowerCase().split(' ');
            descWords.forEach(word => {
                if (!this.searchIndex.has(word)) {
                    this.searchIndex.set(word, new Set());
                }
                this.searchIndex.get(word).add(template.id);
            });

            // Index by tags
            template.tags.forEach(tag => {
                const tagLower = tag.toLowerCase();
                if (!this.searchIndex.has(tagLower)) {
                    this.searchIndex.set(tagLower, new Set());
                }
                this.searchIndex.get(tagLower).add(template.id);
            });

            // Index by category
            const categoryLower = template.category.toLowerCase();
            if (!this.searchIndex.has(categoryLower)) {
                this.searchIndex.set(categoryLower, new Set());
            }
            this.searchIndex.get(categoryLower).add(template.id);
        });
    }

    async initializeRecommendations() {
        // Simple recommendation algorithm based on popularity and ratings
        const allTemplates = [...this.templates.values(), ...this.userTemplates.values()];
        
        this.recommendations = allTemplates
            .filter(template => template.featured || template.rating >= 4.5)
            .sort((a, b) => {
                const scoreA = (a.rating * 0.7) + (Math.log(a.downloads + 1) * 0.3);
                const scoreB = (b.rating * 0.7) + (Math.log(b.downloads + 1) * 0.3);
                return scoreB - scoreA;
            })
            .slice(0, 10);
    }

    // Search functionality
    searchTemplates(query, filters = {}) {
        this.analytics.searches++;
        
        if (!query && Object.keys(filters).length === 0) {
            return this.getAllTemplates();
        }

        let results = new Set();

        // Text search
        if (query) {
            const queryWords = query.toLowerCase().split(' ');
            queryWords.forEach(word => {
                if (this.searchIndex.has(word)) {
                    this.searchIndex.get(word).forEach(id => results.add(id));
                }
            });
        } else {
            // If no query, start with all templates
            [...this.templates.keys(), ...this.userTemplates.keys()].forEach(id => results.add(id));
        }

        // Apply filters
        let filteredResults = Array.from(results).map(id => 
            this.templates.get(id) || this.userTemplates.get(id)
        ).filter(template => template);

        if (filters.category) {
            filteredResults = filteredResults.filter(t => t.category === filters.category);
        }

        if (filters.tags && filters.tags.length > 0) {
            filteredResults = filteredResults.filter(t => 
                filters.tags.some(tag => t.tags.includes(tag))
            );
        }

        if (filters.author) {
            filteredResults = filteredResults.filter(t => 
                t.author.toLowerCase().includes(filters.author.toLowerCase())
            );
        }

        if (filters.minRating) {
            filteredResults = filteredResults.filter(t => t.rating >= filters.minRating);
        }

        if (filters.complexity) {
            filteredResults = filteredResults.filter(t => t.metadata.complexity === filters.complexity);
        }

        if (filters.responsive !== undefined) {
            filteredResults = filteredResults.filter(t => t.metadata.responsive === filters.responsive);
        }

        if (filters.accessibility !== undefined) {
            filteredResults = filteredResults.filter(t => t.metadata.accessibility === filters.accessibility);
        }

        // Sort results
        const sortBy = filters.sortBy || 'relevance';
        switch (sortBy) {
            case 'name':
                filteredResults.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'rating':
                filteredResults.sort((a, b) => b.rating - a.rating);
                break;
            case 'downloads':
                filteredResults.sort((a, b) => b.downloads - a.downloads);
                break;
            case 'newest':
                filteredResults.sort((a, b) => new Date(b.updated) - new Date(a.updated));
                break;
            case 'oldest':
                filteredResults.sort((a, b) => new Date(a.updated) - new Date(b.updated));
                break;
            default: // relevance
                filteredResults.sort((a, b) => {
                    const scoreA = this.calculateRelevanceScore(a, query);
                    const scoreB = this.calculateRelevanceScore(b, query);
                    return scoreB - scoreA;
                });
        }

        return filteredResults;
    }

    calculateRelevanceScore(template, query) {
        if (!query) return template.rating;

        let score = 0;
        const queryLower = query.toLowerCase();

        // Name match (highest weight)
        if (template.name.toLowerCase().includes(queryLower)) {
            score += 10;
        }

        // Tag match
        template.tags.forEach(tag => {
            if (tag.toLowerCase().includes(queryLower)) {
                score += 5;
            }
        });

        // Description match
        if (template.description.toLowerCase().includes(queryLower)) {
            score += 3;
        }

        // Category match
        if (template.category.toLowerCase().includes(queryLower)) {
            score += 2;
        }

        // Boost for rating and popularity
        score += template.rating;
        score += Math.log(template.downloads + 1) * 0.1;

        return score;
    }

    // Template management
    async saveTemplate(templateData) {
        try {
            const template = {
                ...this.templateSchema,
                ...templateData,
                id: templateData.id || this.generateTemplateId(),
                created: templateData.created || new Date(),
                updated: new Date(),
                author: templateData.author || 'User',
                version: templateData.version || '1.0.0',
                downloads: 0,
                rating: 0,
                reviews: []
            };

            this.userTemplates.set(template.id, template);
            
            // Update search index
            await this.buildSearchIndex();
            
            // Save to localStorage
            await this.saveUserTemplates();
            
            this.analytics.uploads++;
            
            console.log('✅ Template saved:', template.name);
            return template;
        } catch (error) {
            console.error('❌ Failed to save template:', error);
            throw error;
        }
    }

    async deleteTemplate(templateId) {
        try {
            if (this.userTemplates.has(templateId)) {
                this.userTemplates.delete(templateId);
                await this.buildSearchIndex();
                await this.saveUserTemplates();
                console.log('✅ Template deleted:', templateId);
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Failed to delete template:', error);
            throw error;
        }
    }

    async updateTemplate(templateId, updates) {
        try {
            const template = this.userTemplates.get(templateId);
            if (!template) {
                throw new Error('Template not found');
            }

            const updatedTemplate = {
                ...template,
                ...updates,
                updated: new Date(),
                version: this.incrementVersion(template.version)
            };

            this.userTemplates.set(templateId, updatedTemplate);
            await this.buildSearchIndex();
            await this.saveUserTemplates();
            
            console.log('✅ Template updated:', templateId);
            return updatedTemplate;
        } catch (error) {
            console.error('❌ Failed to update template:', error);
            throw error;
        }
    }

    // Favorites management
    async addToFavorites(templateId) {
        this.favorites.add(templateId);
        await this.saveFavorites();
        this.analytics.favorites++;
        console.log('❤️ Added to favorites:', templateId);
    }

    async removeFromFavorites(templateId) {
        this.favorites.delete(templateId);
        await this.saveFavorites();
        console.log('💔 Removed from favorites:', templateId);
    }

    getFavoriteTemplates() {
        return Array.from(this.favorites).map(id => 
            this.templates.get(id) || this.userTemplates.get(id)
        ).filter(template => template);
    }

    // Template download and usage
    async downloadTemplate(templateId) {
        const template = this.templates.get(templateId) || this.userTemplates.get(templateId);
        if (!template) {
            throw new Error('Template not found');
        }

        // Increment download count
        template.downloads++;
        this.analytics.downloads++;

        // Return template files
        return {
            id: template.id,
            name: template.name,
            files: template.files,
            metadata: template.metadata
        };
    }

    async previewTemplate(templateId) {
        const template = this.templates.get(templateId) || this.userTemplates.get(templateId);
        if (!template) {
            throw new Error('Template not found');
        }

        this.analytics.views++;
        return template;
    }

    // Recommendations
    getRecommendations(limit = 5) {
        return this.recommendations.slice(0, limit);
    }

    getRelatedTemplates(templateId, limit = 5) {
        const template = this.templates.get(templateId) || this.userTemplates.get(templateId);
        if (!template) return [];

        const allTemplates = [...this.templates.values(), ...this.userTemplates.values()];
        
        // Find templates with similar tags or category
        const related = allTemplates
            .filter(t => t.id !== templateId)
            .map(t => ({
                template: t,
                similarity: this.calculateSimilarity(template, t)
            }))
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, limit)
            .map(item => item.template);

        return related;
    }

    calculateSimilarity(template1, template2) {
        let similarity = 0;

        // Category match
        if (template1.category === template2.category) {
            similarity += 5;
        }

        // Tag overlap
        const commonTags = template1.tags.filter(tag => template2.tags.includes(tag));
        similarity += commonTags.length * 2;

        // Complexity match
        if (template1.metadata.complexity === template2.metadata.complexity) {
            similarity += 1;
        }

        return similarity;
    }

    // Utility methods
    getAllTemplates() {
        return [...this.templates.values(), ...this.userTemplates.values()];
    }

    getTemplateById(id) {
        return this.templates.get(id) || this.userTemplates.get(id);
    }

    getCategories() {
        return Array.from(this.categories.values());
    }

    getTags() {
        return Array.from(this.tags);
    }

    getAnalytics() {
        return {
            ...this.analytics,
            totalTemplates: this.templates.size + this.userTemplates.size,
            builtInTemplates: this.templates.size,
            userTemplates: this.userTemplates.size,
            categories: this.categories.size,
            tags: this.tags.size,
            favorites: this.favorites.size
        };
    }

    generateTemplateId() {
        return 'template_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    incrementVersion(version) {
        const parts = version.split('.');
        parts[2] = (parseInt(parts[2]) + 1).toString();
        return parts.join('.');
    }

    async saveUserTemplates() {
        try {
            const templates = Array.from(this.userTemplates.values());
            localStorage.setItem('userTemplates', JSON.stringify(templates));
        } catch (error) {
            console.warn('Failed to save user templates:', error);
        }
    }

    async saveFavorites() {
        try {
            const favorites = Array.from(this.favorites);
            localStorage.setItem('favoriteTemplates', JSON.stringify(favorites));
        } catch (error) {
            console.warn('Failed to save favorites:', error);
        }
    }

    // Template generators for built-in templates
    generateModernLandingHTML() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Landing Page</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="nav-brand">Brand</div>
            <ul class="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="#features">Features</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <section class="hero">
            <div class="hero-content">
                <h1>Welcome to the Future</h1>
                <p>Experience the next generation of web design</p>
                <button class="cta-button">Get Started</button>
            </div>
        </section>
        
        <section class="features">
            <div class="container">
                <h2>Features</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>Fast</h3>
                        <p>Lightning fast performance</p>
                    </div>
                    <div class="feature-card">
                        <h3>Secure</h3>
                        <p>Enterprise-grade security</p>
                    </div>
                    <div class="feature-card">
                        <h3>Scalable</h3>
                        <p>Grows with your business</p>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <footer class="footer">
        <p>&copy; 2024 Modern Landing. All rights reserved.</p>
    </footer>
    
    <script src="script.js"></script>
</body>
</html>`;
    }

    generateModernLandingCSS() {
        return `* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
    color: #333;
}

.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: #3373dc;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #3373dc;
}

.hero {
    background: linear-gradient(135deg, #3373dc, #1e40af);
    color: white;
    padding: 8rem 2rem 4rem;
    text-align: center;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background: #fff;
    color: #3373dc;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s;
}

.cta-button:hover {
    transform: translateY(-2px);
}

.features {
    padding: 4rem 2rem;
    background: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.features h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    text-align: center;
}

.feature-card h3 {
    color: #3373dc;
    margin-bottom: 1rem;
}

.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem;
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
}`;
    }

    generateModernLandingJS() {
        return `// Modern Landing Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-menu a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // CTA button interaction
    const ctaButton = document.querySelector('.cta-button');
    
    ctaButton.addEventListener('click', function() {
        alert('Welcome! This is a template demonstration.');
    });
    
    // Header scroll effect
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        } else {
            header.style.background = '#fff';
            header.style.backdropFilter = 'none';
        }
    });
});`;
    }

    generatePortfolioGridHTML() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Grid</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <h1>Creative Portfolio</h1>
        <nav class="filter-nav">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="web">Web</button>
            <button class="filter-btn" data-filter="mobile">Mobile</button>
            <button class="filter-btn" data-filter="design">Design</button>
        </nav>
    </header>
    
    <main class="portfolio-grid">
        <div class="portfolio-item" data-category="web">
            <img src="https://via.placeholder.com/400x300" alt="Project 1">
            <div class="item-overlay">
                <h3>Web Project 1</h3>
                <p>Modern web application</p>
            </div>
        </div>
        <div class="portfolio-item" data-category="mobile">
            <img src="https://via.placeholder.com/400x500" alt="Project 2">
            <div class="item-overlay">
                <h3>Mobile App</h3>
                <p>iOS & Android application</p>
            </div>
        </div>
        <div class="portfolio-item" data-category="design">
            <img src="https://via.placeholder.com/400x400" alt="Project 3">
            <div class="item-overlay">
                <h3>Brand Design</h3>
                <p>Complete brand identity</p>
            </div>
        </div>
    </main>
    
    <script src="script.js"></script>
</body>
</html>`;
    }

    generatePortfolioGridCSS() {
        return `* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #1a1a1a;
    color: #fff;
}

.header {
    text-align: center;
    padding: 3rem 2rem;
}

.header h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.filter-nav {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid #333;
    color: #fff;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s;
}

.filter-btn:hover,
.filter-btn.active {
    background: #ff6b6b;
    border-color: #ff6b6b;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.portfolio-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s;
}

.portfolio-item:hover {
    transform: translateY(-10px);
}

.portfolio-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 2rem;
    transform: translateY(100%);
    transition: transform 0.3s;
}

.portfolio-item:hover .item-overlay {
    transform: translateY(0);
}

.item-overlay h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.item-overlay p {
    opacity: 0.8;
}

@media (max-width: 768px) {
    .portfolio-grid {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 2rem;
    }
}`;
    }

    generatePortfolioGridJS() {
        return `document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter items
            portfolioItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    item.style.animation = 'fadeIn 0.5s';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
    
    // Add click handlers for portfolio items
    portfolioItems.forEach(item => {
        item.addEventListener('click', function() {
            const title = this.querySelector('h3').textContent;
            alert(\`Opening \${title} - This is a template demonstration.\`);
        });
    });
});

// CSS animation
const style = document.createElement('style');
style.textContent = \`
@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}
\`;
document.head.appendChild(style);`;
    }

    generateDashboardHTML() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Dashboard</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="dashboard">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>Dashboard</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="#" class="nav-item active">📊 Analytics</a>
                <a href="#" class="nav-item">👥 Users</a>
                <a href="#" class="nav-item">💰 Revenue</a>
                <a href="#" class="nav-item">⚙️ Settings</a>
            </nav>
        </aside>
        
        <main class="main-content">
            <header class="content-header">
                <h1>Analytics Overview</h1>
                <div class="date-range">Last 30 days</div>
            </header>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">12,345</div>
                    <div class="metric-label">Total Users</div>
                    <div class="metric-change positive">+12%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$45,678</div>
                    <div class="metric-label">Revenue</div>
                    <div class="metric-change positive">+8%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">89.2%</div>
                    <div class="metric-label">Conversion Rate</div>
                    <div class="metric-change negative">-2%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">2.4s</div>
                    <div class="metric-label">Avg. Load Time</div>
                    <div class="metric-change positive">+5%</div>
                </div>
            </div>
            
            <div class="charts-grid">
                <div class="chart-card">
                    <h3>Traffic Overview</h3>
                    <div class="chart-placeholder">📈 Chart Area</div>
                </div>
                <div class="chart-card">
                    <h3>Top Pages</h3>
                    <div class="chart-placeholder">📊 Chart Area</div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="script.js"></script>
</body>
</html>`;
    }

    generateDashboardCSS() {
        return `* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #0f172a;
    color: #e2e8f0;
}

.dashboard {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background: #1e293b;
    border-right: 1px solid #334155;
}

.sidebar-header {
    padding: 2rem;
    border-bottom: 1px solid #334155;
}

.sidebar-header h2 {
    color: #3b82f6;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    display: block;
    padding: 1rem 2rem;
    color: #94a3b8;
    text-decoration: none;
    transition: all 0.3s;
}

.nav-item:hover,
.nav-item.active {
    background: #334155;
    color: #3b82f6;
}

.main-content {
    flex: 1;
    padding: 2rem;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.content-header h1 {
    font-size: 2rem;
}

.date-range {
    background: #1e293b;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: #1e293b;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #334155;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #94a3b8;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.metric-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.metric-change.positive {
    color: #10b981;
}

.metric-change.negative {
    color: #ef4444;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: #1e293b;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #334155;
}

.chart-card h3 {
    margin-bottom: 1rem;
    color: #e2e8f0;
}

.chart-placeholder {
    height: 200px;
    background: #0f172a;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #64748b;
}

@media (max-width: 768px) {
    .dashboard {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
}`;
    }

    generateDashboardJS() {
        return `document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active state
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // Simulate page change
            const section = this.textContent.trim();
            console.log(\`Navigating to: \${section}\`);
            
            // Update header
            const header = document.querySelector('.content-header h1');
            header.textContent = section + ' Overview';
        });
    });
    
    // Simulate real-time updates
    function updateMetrics() {
        const metricValues = document.querySelectorAll('.metric-value');
        
        metricValues.forEach(value => {
            const current = value.textContent;
            // Add subtle animation
            value.style.transform = 'scale(1.05)';
            setTimeout(() => {
                value.style.transform = 'scale(1)';
            }, 200);
        });
    }
    
    // Update metrics every 30 seconds
    setInterval(updateMetrics, 30000);
    
    // Add hover effects to metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 25px rgba(59, 130, 246, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
});

// Add CSS transitions
const style = document.createElement('style');
style.textContent = \`
.metric-card {
    transition: all 0.3s ease;
}

.metric-value {
    transition: transform 0.2s ease;
}
\`;
document.head.appendChild(style);`;
    }

    // Event handling methods
    addEventListener(event, callback) {
        if (!this.eventListeners) {
            this.eventListeners = new Map();
        }
        
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        
        this.eventListeners.get(event).push(callback);
    }

    removeEventListener(event, callback) {
        if (!this.eventListeners || !this.eventListeners.has(event)) {
            return;
        }
        
        const listeners = this.eventListeners.get(event);
        const index = listeners.indexOf(callback);
        
        if (index > -1) {
            listeners.splice(index, 1);
        }
    }

    emit(event, data) {
        if (!this.eventListeners || !this.eventListeners.has(event)) {
            return;
        }
        
        this.eventListeners.get(event).forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('Event listener error:', error);
            }
        });
    }
}

// Create and export the template library instance
export const templateLibrary = new TemplateLibrary();
export default TemplateLibrary;