/**
 * Semantic MCP Integration
 * Integrates semantic analysis components with the MCP agent system
 */

import { SemanticAnalysisEngine, SemanticElement, UIComponent, SemanticAnalysisResult } from './SemanticAnalysisEngine.js';
import { KnowledgeGraphBuilder, KnowledgeGraph, GraphAnalysisResult } from './KnowledgeGraphBuilder.js';
import { PatternRecognitionSystem, PatternAnalysisResult } from './PatternRecognitionSystem.js';
import { BaseAgent } from '../agents/BaseAgent';
import { AgentTask, AgentContext, AgentConfig, AgentMetrics, AgentCapability } from '../agents/types.js';
import { AgentConductor } from '../conductor/AgentConductor.js';
import { CommunicationProtocol } from '../conductor/CommunicationProtocol.js';

/**
 * Semantic integration configuration
 */
export interface SemanticIntegrationConfig {
  enableSemanticAnalysis: boolean;
  enableKnowledgeGraph: boolean;
  enablePatternRecognition: boolean;
  enableRealTimeAnalysis: boolean;
  enableCaching: boolean;
  cacheTimeout: number;
  maxConcurrentAnalysis: number;
  analysisTimeout: number;
}

/**
 * Semantic analysis request
 */
export interface SemanticAnalysisRequest {
  id: string;
  type: 'full-analysis' | 'incremental-analysis' | 'pattern-only' | 'graph-only';
  data: {
    elements?: SemanticElement[];
    components?: UIComponent[];
    imageData?: string;
    url?: string;
    selector?: string;
  };
  options: {
    includeKnowledgeGraph?: boolean;
    includePatternAnalysis?: boolean;
    includeRecommendations?: boolean;
    includeInsights?: boolean;
    confidenceThreshold?: number;
  };
  context?: AgentContext;
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
}

/**
 * Semantic analysis response
 */
export interface SemanticAnalysisResponse {
  id: string;
  requestId: string;
  status: 'success' | 'error' | 'partial';
  data: {
    semanticAnalysis?: SemanticAnalysisResult;
    knowledgeGraph?: KnowledgeGraph;
    graphAnalysis?: GraphAnalysisResult;
    patternAnalysis?: PatternAnalysisResult;
  };
  metadata: {
    processingTime: number;
    agentsUsed: string[];
    cacheHit: boolean;
    confidence: number;
  };
  errors?: AnalysisError[];
  warnings?: AnalysisWarning[];
  timestamp: Date;
}

/**
 * Analysis error
 */
export interface AnalysisError {
  code: string;
  message: string;
  agent: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  details?: any;
}

/**
 * Analysis warning
 */
export interface AnalysisWarning {
  code: string;
  message: string;
  agent: string;
  recommendation?: string;
}

/**
 * Analysis cache entry
 */
export interface AnalysisCacheEntry {
  key: string;
  data: SemanticAnalysisResponse;
  timestamp: Date;
  expiresAt: Date;
  accessCount: number;
  lastAccessed: Date;
}

/**
 * Real-time analysis event
 */
export interface RealTimeAnalysisEvent {
  type: 'element-added' | 'element-removed' | 'element-modified' | 'pattern-detected' | 'anti-pattern-detected';
  data: any;
  timestamp: Date;
  source: string;
}

/**
 * Analysis workflow
 */
export interface AnalysisWorkflow {
  id: string;
  name: string;
  description: string;
  steps: AnalysisStep[];
  triggers: WorkflowTrigger[];
  enabled: boolean;
  priority: number;
}

/**
 * Analysis step
 */
export interface AnalysisStep {
  id: string;
  name: string;
  agent: string;
  task: string;
  inputs: string[];
  outputs: string[];
  conditions?: StepCondition[];
  timeout?: number;
  retries?: number;
}

/**
 * Step condition
 */
export interface StepCondition {
  type: 'data-available' | 'confidence-threshold' | 'error-count' | 'time-elapsed';
  operator: 'equals' | 'gt' | 'lt' | 'exists';
  value: any;
}

/**
 * Workflow trigger
 */
export interface WorkflowTrigger {
  type: 'manual' | 'scheduled' | 'event' | 'data-change';
  condition: string;
  enabled: boolean;
}

/**
 * Analysis metrics
 */
export interface AnalysisMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  errorCount: number;
  averageProcessingTime: number;
  cacheHitRate: number;
  agentUtilization: Record<string, number>;
  errorsByType: Record<string, number>;
  performanceMetrics: PerformanceMetrics;
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  memoryUsage: number;
  cpuUsage: number;
  networkLatency: number;
  throughput: number;
  queueLength: number;
  averageProcessingTime: number;
}

/**
 * Semantic MCP Integration Agent
 */
export class SemanticMCPIntegration extends BaseAgent {
  private config: SemanticIntegrationConfig;
  private semanticEngine: SemanticAnalysisEngine;
  private knowledgeGraphBuilder: KnowledgeGraphBuilder;
  private patternRecognitionSystem: PatternRecognitionSystem;
  private conductor: AgentConductor;
  private communicationProtocol: CommunicationProtocol;
  
  private analysisQueue: Map<string, SemanticAnalysisRequest> = new Map();
  private analysisCache: Map<string, AnalysisCacheEntry> = new Map();
  private workflows: Map<string, AnalysisWorkflow> = new Map();
  private analysisMetrics: AnalysisMetrics;
  private eventListeners: Map<string, Function[]> = new Map();
  private startTime: number = Date.now();

  constructor(
    config: Partial<SemanticIntegrationConfig> = {},
    conductor: AgentConductor,
    communicationProtocol: CommunicationProtocol
  ) {
    super({
      id: 'semantic-mcp-integration',
      name: 'Semantic MCP Integration',
      // description: 'Integrates semantic analysis with MCP agent system', // Removed as not part of AgentConfig
      capabilities: [
        AgentCapability.SEMANTIC_ANALYSIS,
        AgentCapability.PATTERN_RECOGNITION,
        AgentCapability.WORKFLOW_ORCHESTRATION,
        AgentCapability.AUTOMATION
      ],
      version: '1.0.0'
    });

    this.config = {
      enableSemanticAnalysis: true,
      enableKnowledgeGraph: true,
      enablePatternRecognition: true,
      enableRealTimeAnalysis: true,
      enableCaching: true,
      cacheTimeout: 300000, // 5 minutes
      maxConcurrentAnalysis: 5,
      analysisTimeout: 30000, // 30 seconds
      ...config
    };

    this.conductor = conductor;
    this.communicationProtocol = communicationProtocol;
    
    // Initialize semantic analysis components
    this.semanticEngine = new SemanticAnalysisEngine();
    this.knowledgeGraphBuilder = new KnowledgeGraphBuilder();
    this.patternRecognitionSystem = new PatternRecognitionSystem();
    
    // Initialize metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      errorCount: 0,
      averageProcessingTime: 0,
      cacheHitRate: 0,
      agentUtilization: {},
      errorsByType: {},
      performanceMetrics: {
        memoryUsage: 0,
        cpuUsage: 0,
        networkLatency: 0,
        throughput: 0,
        queueLength: 0,
        averageProcessingTime: 0
      }
    };

    this.initializeWorkflows();
    this.setupEventListeners();
    this.startCacheCleanup();
  }

  /**
   * Initialize default workflows
   */
  private initializeWorkflows(): void {
    // Full semantic analysis workflow
    this.workflows.set('full-semantic-analysis', {
      id: 'full-semantic-analysis',
      name: 'Full Semantic Analysis',
      description: 'Complete semantic analysis including knowledge graph and pattern recognition',
      steps: [
        {
          id: 'semantic-analysis',
          name: 'Semantic Analysis',
          agent: 'semantic-analysis-engine',
          task: 'analyze-ui',
          inputs: ['elements', 'imageData'],
          outputs: ['semanticResult'],
          timeout: 15000
        },
        {
          id: 'knowledge-graph',
          name: 'Knowledge Graph Building',
          agent: 'knowledge-graph-builder',
          task: 'build-graph',
          inputs: ['semanticResult'],
          outputs: ['knowledgeGraph'],
          conditions: [
            {
              type: 'data-available',
              operator: 'exists',
              value: 'semanticResult'
            }
          ],
          timeout: 10000
        },
        {
          id: 'pattern-recognition',
          name: 'Pattern Recognition',
          agent: 'pattern-recognition-system',
          task: 'analyze-patterns',
          inputs: ['semanticResult', 'knowledgeGraph'],
          outputs: ['patternAnalysis'],
          conditions: [
            {
              type: 'data-available',
              operator: 'exists',
              value: 'semanticResult'
            }
          ],
          timeout: 8000
        },
        {
          id: 'graph-analysis',
          name: 'Graph Analysis',
          agent: 'knowledge-graph-builder',
          task: 'analyze-graph',
          inputs: ['knowledgeGraph'],
          outputs: ['graphAnalysis'],
          conditions: [
            {
              type: 'data-available',
              operator: 'exists',
              value: 'knowledgeGraph'
            }
          ],
          timeout: 5000
        }
      ],
      triggers: [
        {
          type: 'manual',
          condition: 'user-request',
          enabled: true
        },
        {
          type: 'event',
          condition: 'ui-change-detected',
          enabled: true
        }
      ],
      enabled: true,
      priority: 1
    });

    // Quick pattern analysis workflow
    this.workflows.set('quick-pattern-analysis', {
      id: 'quick-pattern-analysis',
      name: 'Quick Pattern Analysis',
      description: 'Fast pattern recognition without full semantic analysis',
      steps: [
        {
          id: 'pattern-recognition-only',
          name: 'Pattern Recognition',
          agent: 'pattern-recognition-system',
          task: 'detect-patterns',
          inputs: ['elements'],
          outputs: ['patterns'],
          timeout: 5000
        }
      ],
      triggers: [
        {
          type: 'event',
          condition: 'quick-analysis-requested',
          enabled: true
        }
      ],
      enabled: true,
      priority: 2
    });
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Listen for agent communication events
    this.communicationProtocol.on('message', (message) => {
      this.handleAgentMessage(message);
    });

    // Listen for conductor events
    this.conductor.on('agent-registered', (agentId) => {
      console.log(`Agent registered: ${agentId}`);
    });

    this.conductor.on('agent-unregistered', (agentId) => {
      console.log(`Agent unregistered: ${agentId}`);
    });
  }

  /**
   * Handle agent messages
   */
  private handleAgentMessage(message: any): void {
    switch (message.type) {
      case 'analysis-request':
        this.processAnalysisRequest(message.data);
        break;
      case 'real-time-event':
        this.handleRealTimeEvent(message.data);
        break;
      case 'workflow-trigger':
        this.triggerWorkflow(message.data.workflowId, message.data.context);
        break;
    }
  }

  /**
   * Process semantic analysis request
   */
  async processAnalysisRequest(request: SemanticAnalysisRequest): Promise<SemanticAnalysisResponse> {
    console.log(`Processing semantic analysis request: ${request.id}`);
    
    const startTime = Date.now();
    this.metrics.totalRequests++;
    
    try {
      // Check cache first
      if (this.config.enableCaching) {
        const cacheKey = this.generateCacheKey(request);
        const cachedResult = this.getFromCache(cacheKey);
        if (cachedResult) {
          console.log(`Cache hit for request: ${request.id}`);
          this.metrics.cacheHitRate = (this.metrics.cacheHitRate * (this.metrics.totalRequests - 1) + 1) / this.metrics.totalRequests;
          return cachedResult.data;
        }
      }

      // Add to queue
      this.analysisQueue.set(request.id, request);
      this.metrics.performanceMetrics.queueLength = this.analysisQueue.size;

      // Execute analysis based on request type
      let response: SemanticAnalysisResponse;
      
      switch (request.type) {
        case 'full-analysis':
          response = await this.executeFullAnalysis(request);
          break;
        case 'incremental-analysis':
          response = await this.executeIncrementalAnalysis(request);
          break;
        case 'pattern-only':
          response = await this.executePatternOnlyAnalysis(request);
          break;
        case 'graph-only':
          response = await this.executeGraphOnlyAnalysis(request);
          break;
        default:
          throw new Error(`Unknown analysis type: ${request.type}`);
      }

      // Update metrics
      const processingTime = Date.now() - startTime;
      this.metrics.successfulRequests++;
      this.metrics.averageProcessingTime = (
        (this.metrics.averageProcessingTime * (this.metrics.successfulRequests - 1) + processingTime) /
        this.metrics.successfulRequests
      );

      response.metadata.processingTime = processingTime;

      // Cache result
      if (this.config.enableCaching) {
        const cacheKey = this.generateCacheKey(request);
        this.addToCache(cacheKey, response);
      }

      // Remove from queue
      this.analysisQueue.delete(request.id);
      this.metrics.performanceMetrics.queueLength = this.analysisQueue.size;

      // Emit completion event
      this.emit('analysis-completed', { request, response });

      console.log(`Analysis completed for request: ${request.id} in ${processingTime}ms`);
      return response;

    } catch (error) {
      console.error(`Analysis failed for request: ${request.id}`, error);
      
      this.metrics.failedRequests++;
      const errorCode = error instanceof Error ? error.message : 'unknown-error';
      this.metrics.errorsByType[errorCode] = (this.metrics.errorsByType[errorCode] || 0) + 1;

      // Remove from queue
      this.analysisQueue.delete(request.id);
      this.metrics.performanceMetrics.queueLength = this.analysisQueue.size;

      const errorResponse: SemanticAnalysisResponse = {
        id: `response-${Date.now()}`,
        requestId: request.id,
        status: 'error',
        data: {},
        metadata: {
          processingTime: Date.now() - startTime,
          agentsUsed: [],
          cacheHit: false,
          confidence: 0
        },
        errors: [
          {
            code: errorCode,
            message: error instanceof Error ? error.message : 'Unknown error occurred',
            agent: 'semantic-mcp-integration',
            severity: 'high'
          }
        ],
        timestamp: new Date()
      };

      this.emit('analysis-failed', { request, error: errorResponse });
      return errorResponse;
    }
  }

  /**
   * Execute full semantic analysis
   */
  private async executeFullAnalysis(request: SemanticAnalysisRequest): Promise<SemanticAnalysisResponse> {
    const workflow = this.workflows.get('full-semantic-analysis');
    if (!workflow) {
      throw new Error('Full analysis workflow not found');
    }

    return await this.executeWorkflow(workflow, request);
  }

  /**
   * Execute incremental analysis
   */
  private async executeIncrementalAnalysis(request: SemanticAnalysisRequest): Promise<SemanticAnalysisResponse> {
    // For incremental analysis, we only analyze changed elements
    const agentsUsed: string[] = [];
    const data: any = {};
    let totalConfidence = 0;
    let confidenceCount = 0;

    if (request.data.elements && this.config.enableSemanticAnalysis) {
      const semanticResult = await this.semanticEngine.analyzeSemantics(
        request.data.imageData as any, // Type conversion for compatibility
        request.context
      );
      data.semanticAnalysis = semanticResult;
      agentsUsed.push('semantic-analysis-engine');
      // Calculate average confidence from elements
      const avgConfidence = semanticResult.elements.reduce((sum, el) => sum + el.confidence, 0) / semanticResult.elements.length;
      totalConfidence += avgConfidence || 0;
      confidenceCount++;

      // Only build knowledge graph if we have enough elements
      if (request.options.includeKnowledgeGraph && semanticResult.elements.length > 5) {
        const knowledgeGraph = await this.knowledgeGraphBuilder.buildGraph(
          semanticResult.elements,
          semanticResult.components,
          request.context
        );
        data.knowledgeGraph = knowledgeGraph;
        agentsUsed.push('knowledge-graph-builder');
      }

      // Pattern recognition on changed elements only
      if (request.options.includePatternAnalysis) {
        const patternAnalysis = await this.patternRecognitionSystem.analyzePatterns(
          semanticResult.elements,
          semanticResult.components,
          data.knowledgeGraph,
          request.context
        );
        data.patternAnalysis = patternAnalysis;
        agentsUsed.push('pattern-recognition-system');
        totalConfidence += patternAnalysis.statistics.averageConfidence;
        confidenceCount++;
      }
    }

    const averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0;

    return {
      id: `response-${Date.now()}`,
      requestId: request.id,
      status: 'success',
      data,
      metadata: {
        processingTime: 0, // Will be set by caller
        agentsUsed,
        cacheHit: false,
        confidence: averageConfidence
      },
      timestamp: new Date()
    };
  }

  /**
   * Execute pattern-only analysis
   */
  private async executePatternOnlyAnalysis(request: SemanticAnalysisRequest): Promise<SemanticAnalysisResponse> {
    if (!request.data.elements) {
      throw new Error('Elements required for pattern analysis');
    }

    const patternAnalysis = await this.patternRecognitionSystem.analyzePatterns(
      request.data.elements,
      request.data.components || [],
      undefined,
      request.context
    );

    return {
      id: `response-${Date.now()}`,
      requestId: request.id,
      status: 'success',
      data: {
        patternAnalysis
      },
      metadata: {
        processingTime: 0,
        agentsUsed: ['pattern-recognition-system'],
        cacheHit: false,
        confidence: patternAnalysis.statistics.averageConfidence
      },
      timestamp: new Date()
    };
  }

  /**
   * Execute graph-only analysis
   */
  private async executeGraphOnlyAnalysis(request: SemanticAnalysisRequest): Promise<SemanticAnalysisResponse> {
    if (!request.data.elements) {
      throw new Error('Elements required for graph analysis');
    }

    const knowledgeGraph = await this.knowledgeGraphBuilder.buildGraph(
      request.data.elements,
      request.data.components || [],
      request.context
    );

    const graphAnalysis = await this.knowledgeGraphBuilder.analyzeGraph(knowledgeGraph.id);

    return {
      id: `response-${Date.now()}`,
      requestId: request.id,
      status: 'success',
      data: {
        knowledgeGraph,
        graphAnalysis
      },
      metadata: {
        processingTime: 0,
        agentsUsed: ['knowledge-graph-builder'],
        cacheHit: false,
        confidence: 0.9 // Default confidence for graph analysis
      },
      timestamp: new Date()
    };
  }

  /**
   * Execute workflow
   */
  private async executeWorkflow(
    workflow: AnalysisWorkflow,
    request: SemanticAnalysisRequest
  ): Promise<SemanticAnalysisResponse> {
    const agentsUsed: string[] = [];
    const data: any = {};
    const workflowData: Record<string, any> = {
      elements: request.data.elements,
      components: request.data.components,
      imageData: request.data.imageData
    };

    for (const step of workflow.steps) {
      try {
        // Check step conditions
        if (step.conditions && !this.evaluateStepConditions(step.conditions, workflowData)) {
          console.log(`Skipping step ${step.id} - conditions not met`);
          continue;
        }

        // Execute step
        console.log(`Executing workflow step: ${step.id}`);
        const stepResult = await this.executeWorkflowStep(step, workflowData, request.context);
        
        // Store step outputs
        for (const output of step.outputs) {
          workflowData[output] = stepResult;
          data[output] = stepResult;
        }

        agentsUsed.push(step.agent);
        
      } catch (error) {
        console.error(`Workflow step ${step.id} failed:`, error);
        
        // Continue with other steps if possible
        if (step.retries && step.retries > 0) {
          // Implement retry logic here
        }
      }
    }

    // Calculate overall confidence
    let totalConfidence = 0;
    let confidenceCount = 0;
    
    if (data.semanticResult) {
      totalConfidence += data.semanticResult.confidence;
      confidenceCount++;
    }
    
    if (data.patternAnalysis) {
      totalConfidence += data.patternAnalysis.statistics.averageConfidence;
      confidenceCount++;
    }

    const averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0.8;

    return {
      id: `response-${Date.now()}`,
      requestId: request.id,
      status: 'success',
      data,
      metadata: {
        processingTime: 0,
        agentsUsed,
        cacheHit: false,
        confidence: averageConfidence
      },
      timestamp: new Date()
    };
  }

  /**
   * Execute workflow step
   */
  private async executeWorkflowStep(
    step: AnalysisStep,
    workflowData: Record<string, any>,
    context?: AgentContext
  ): Promise<any> {
    const stepInputs: Record<string, any> = {};
    
    // Gather step inputs
    for (const input of step.inputs) {
      if (workflowData[input] !== undefined) {
        stepInputs[input] = workflowData[input];
      }
    }

    // Execute based on agent and task
    switch (step.agent) {
      case 'semantic-analysis-engine':
        if (step.task === 'analyze-ui') {
          return await this.semanticEngine.analyzeSemantics(
            stepInputs.imageData,
            workflowData as any // Type conversion for compatibility
          );
        }
        break;
        
      case 'knowledge-graph-builder':
        if (step.task === 'build-graph') {
          const semanticResult = stepInputs.semanticResult;
          return await this.knowledgeGraphBuilder.buildGraph(
            semanticResult.elements,
            semanticResult.components,
            context
          );
        } else if (step.task === 'analyze-graph') {
          const knowledgeGraph = stepInputs.knowledgeGraph;
          return await this.knowledgeGraphBuilder.analyzeGraph(knowledgeGraph.id);
        }
        break;
        
      case 'pattern-recognition-system':
        if (step.task === 'analyze-patterns') {
          const semanticResult = stepInputs.semanticResult;
          const knowledgeGraph = stepInputs.knowledgeGraph;
          return await this.patternRecognitionSystem.analyzePatterns(
            semanticResult.elements,
            semanticResult.components,
            knowledgeGraph,
            context
          );
        } else if (step.task === 'detect-patterns') {
          return await this.patternRecognitionSystem.analyzePatterns(
            stepInputs.elements,
            stepInputs.components || [],
            undefined,
            context
          );
        }
        break;
    }

    throw new Error(`Unknown agent/task combination: ${step.agent}/${step.task}`);
  }

  /**
   * Evaluate step conditions
   */
  private evaluateStepConditions(
    conditions: StepCondition[],
    workflowData: Record<string, any>
  ): boolean {
    for (const condition of conditions) {
      if (!this.evaluateStepCondition(condition, workflowData)) {
        return false;
      }
    }
    return true;
  }

  /**
   * Evaluate single step condition
   */
  private evaluateStepCondition(
    condition: StepCondition,
    workflowData: Record<string, any>
  ): boolean {
    switch (condition.type) {
      case 'data-available':
        return condition.operator === 'exists' ? 
          workflowData[condition.value] !== undefined :
          false;
      case 'confidence-threshold':
        const confidence = workflowData.confidence || 0;
        return condition.operator === 'gt' ? 
          confidence > condition.value :
          confidence < condition.value;
      default:
        return true;
    }
  }

  /**
   * Handle real-time events
   */
  private handleRealTimeEvent(event: RealTimeAnalysisEvent): void {
    if (!this.config.enableRealTimeAnalysis) return;

    console.log(`Handling real-time event: ${event.type}`);
    
    // Trigger appropriate workflows based on event type
    switch (event.type) {
      case 'element-added':
      case 'element-modified':
        this.triggerWorkflow('quick-pattern-analysis', {
          source: 'real-time-event',
          event
        });
        break;
      case 'pattern-detected':
      case 'anti-pattern-detected':
        this.emit('pattern-event', event);
        break;
    }
  }

  /**
   * Trigger workflow
   */
  async triggerWorkflow(workflowId: string, context: any): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow || !workflow.enabled) {
      console.warn(`Workflow not found or disabled: ${workflowId}`);
      return;
    }

    // Create analysis request for workflow
    const request: SemanticAnalysisRequest = {
      id: `workflow-${Date.now()}`,
      type: 'full-analysis',
      data: context.data || {},
      options: {
        includeKnowledgeGraph: true,
        includePatternAnalysis: true,
        includeRecommendations: true,
        includeInsights: true
      },
      context: context,
      priority: 'medium',
      timestamp: new Date()
    };

    await this.processAnalysisRequest(request);
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(request: SemanticAnalysisRequest): string {
    const keyData = {
      type: request.type,
      elements: request.data.elements?.map(e => e.id).sort(),
      options: request.options
    };
    
    return `cache-${JSON.stringify(keyData).replace(/\s/g, '')}`;
  }

  /**
   * Get from cache
   */
  private getFromCache(key: string): AnalysisCacheEntry | null {
    const entry = this.analysisCache.get(key);
    if (!entry) return null;
    
    if (entry.expiresAt < new Date()) {
      this.analysisCache.delete(key);
      return null;
    }
    
    entry.accessCount++;
    entry.lastAccessed = new Date();
    return entry;
  }

  /**
   * Add to cache
   */
  private addToCache(key: string, response: SemanticAnalysisResponse): void {
    const entry: AnalysisCacheEntry = {
      key,
      data: response,
      timestamp: new Date(),
      expiresAt: new Date(Date.now() + this.config.cacheTimeout),
      accessCount: 0,
      lastAccessed: new Date()
    };
    
    this.analysisCache.set(key, entry);
  }

  /**
   * Start cache cleanup
   */
  private startCacheCleanup(): void {
    setInterval(() => {
      const now = new Date();
      for (const [key, entry] of this.analysisCache) {
        if (entry.expiresAt < now) {
          this.analysisCache.delete(key);
        }
      }
    }, 60000); // Clean every minute
  }

  /**
   * Event emitter functionality
   */
  on(event: string, listener: (...args: any[]) => void): this {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
    return this;
  }

  emit(event: string, data: any): boolean {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(data));
      return true;
    }
    return false;
  }

  /**
   * Execute agent task
   */
  async executeTask(task: AgentTask, context?: AgentContext): Promise<any> {
    console.log(`Executing semantic integration task: ${task.type}`);
    
    switch (task.type) {
      case 'process-analysis-request':
        return await this.processAnalysisRequest(task.payload as SemanticAnalysisRequest);
      case 'trigger-workflow':
        return await this.triggerWorkflow(task.payload.workflowId, task.payload.context);
      case 'get-metrics':
        return this.getAnalysisMetrics();
      case 'clear-cache':
        return this.clearCache();
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  /**
   * Get analysis metrics
   */
  getAnalysisMetrics(): AnalysisMetrics {
    // Update performance metrics
    this.metrics.performanceMetrics.queueLength = this.analysisQueue.size;
    this.metrics.cacheHitRate = this.metrics.totalRequests > 0 ? 
      this.metrics.cacheHitRate : 0;
    
    return { ...this.metrics };
  }

  /**
   * Get agent metrics (required by BaseAgent)
   */
  getMetrics(): AgentMetrics {
    return {
      tasksCompleted: this.metrics.totalRequests,
      tasksFailedCount: this.metrics.errorCount,
      averageExecutionTime: this.metrics.performanceMetrics.averageProcessingTime,
      lastExecutionTime: Date.now(),
      uptime: Date.now() - this.startTime,
      memoryUsage: process.memoryUsage().heapUsed,
      cpuUsage: 0 // Would need process monitoring for accurate CPU usage
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.analysisCache.clear();
    console.log('Analysis cache cleared');
  }

  /**
   * Get analysis queue status
   */
  getQueueStatus(): { size: number; requests: string[] } {
    return {
      size: this.analysisQueue.size,
      requests: Array.from(this.analysisQueue.keys())
    };
  }

  /**
   * Get workflows
   */
  getWorkflows(): AnalysisWorkflow[] {
    return Array.from(this.workflows.values());
  }

  /**
   * Add workflow
   */
  addWorkflow(workflow: AnalysisWorkflow): void {
    this.workflows.set(workflow.id, workflow);
  }

  /**
   * Remove workflow
   */
  removeWorkflow(workflowId: string): boolean {
    return this.workflows.delete(workflowId);
  }

  /**
   * Update workflow
   */
  updateWorkflow(workflowId: string, updates: Partial<AnalysisWorkflow>): boolean {
    const workflow = this.workflows.get(workflowId);
    if (workflow) {
      Object.assign(workflow, updates);
      return true;
    }
    return false;
  }
}

// Types are already exported as interfaces above