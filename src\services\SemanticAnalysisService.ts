/**
 * Semantic Analysis Service
 * Processes extracted text and generates semantic insights
 */

import { PDFProcessingResult } from './PDFProcessingService';

export interface Entity {
  text: string;
  type: 'PERSON' | 'ORGANIZATION' | 'LOCATION' | 'DATE' | 'MONEY' | 'PERCENT' | 'OTHER';
  confidence: number;
  frequency: number;
  positions: number[];
}

export interface Topic {
  name: string;
  weight: number;
  keywords: string[];
  relevance: number;
}

export interface SentimentAnalysis {
  positive: number;
  negative: number;
  neutral: number;
  overall: 'positive' | 'negative' | 'neutral';
  confidence: number;
}

export interface ComplexityMetrics {
  readability: number;
  vocabulary: number;
  structure: number;
  averageSentenceLength: number;
  averageWordLength: number;
  uniqueWords: number;
}

export interface SemanticAnalysisResult {
  entities: Entity[];
  topics: Topic[];
  sentiment: SentimentAnalysis;
  complexity: ComplexityMetrics;
  keywords: string[];
  summary: string;
  language: string;
  processingTime: number;
}

export class SemanticAnalysisService {
  private static instance: SemanticAnalysisService;
  private stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
    'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
    'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
    'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
    'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
  ]);

  public static getInstance(): SemanticAnalysisService {
    if (!SemanticAnalysisService.instance) {
      SemanticAnalysisService.instance = new SemanticAnalysisService();
    }
    return SemanticAnalysisService.instance;
  }

  /**
   * Perform comprehensive semantic analysis on PDF content
   */
  public async analyzeContent(pdfResult: PDFProcessingResult): Promise<SemanticAnalysisResult> {
    const startTime = Date.now();
    const text = pdfResult.fullText;

    try {
      const [entities, topics, sentiment, complexity, keywords] = await Promise.all([
        this.extractEntities(text),
        this.extractTopics(text),
        this.analyzeSentiment(text),
        this.calculateComplexity(text),
        this.extractKeywords(text)
      ]);

      const summary = this.generateSummary(text, topics, entities);
      const language = this.detectLanguage(text);
      const processingTime = Date.now() - startTime;

      return {
        entities,
        topics,
        sentiment,
        complexity,
        keywords,
        summary,
        language,
        processingTime
      };
    } catch (error) {
      console.error('Error in semantic analysis:', error);
      throw new Error(`Semantic analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract entities from text
   */
  private async extractEntities(text: string): Promise<Entity[]> {
    const entities: Entity[] = [];
    const words = text.toLowerCase().split(/\W+/);
    const wordFreq = new Map<string, number>();

    // Count word frequencies
    words.forEach(word => {
      if (word.length > 2 && !this.stopWords.has(word)) {
        wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
      }
    });

    // Simple entity extraction based on patterns
    const patterns = {
      PERSON: /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g,
      ORGANIZATION: /\b[A-Z][a-z]+ (?:Inc|Corp|LLC|Ltd|Company|Corporation)\b/g,
      DATE: /\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b|\b(?:January|February|March|April|May|June|July|August|September|October|November|December) \d{1,2}, \d{4}\b/g,
      MONEY: /\$[\d,]+(?:\.\d{2})?\b/g,
      PERCENT: /\d+(?:\.\d+)?%/g
    };

    Object.entries(patterns).forEach(([type, pattern]) => {
      const matches = text.match(pattern) || [];
      const entityFreq = new Map<string, number>();
      
      matches.forEach(match => {
        entityFreq.set(match, (entityFreq.get(match) || 0) + 1);
      });

      entityFreq.forEach((frequency, entityText) => {
        entities.push({
          text: entityText,
          type: type as Entity['type'],
          confidence: Math.min(0.9, frequency / matches.length + 0.3),
          frequency,
          positions: this.findPositions(text, entityText)
        });
      });
    });

    return entities.sort((a, b) => b.frequency - a.frequency).slice(0, 20);
  }

  /**
   * Extract topics from text
   */
  private async extractTopics(text: string): Promise<Topic[]> {
    const words = text.toLowerCase().split(/\W+/);
    const wordFreq = new Map<string, number>();
    const totalWords = words.length;

    // Count significant words
    words.forEach(word => {
      if (word.length > 3 && !this.stopWords.has(word)) {
        wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
      }
    });

    // Group related words into topics
    const topics: Topic[] = [];
    const sortedWords = Array.from(wordFreq.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 50);

    // Simple topic clustering based on co-occurrence
    const topicGroups = this.clusterWords(sortedWords, text);
    
    topicGroups.forEach((keywords, topicName) => {
      const weight = keywords.reduce((sum, [, freq]) => sum + freq, 0) / totalWords;
      topics.push({
        name: topicName,
        weight,
        keywords: keywords.map(([word]) => word),
        relevance: weight * keywords.length
      });
    });

    return topics.sort((a, b) => b.relevance - a.relevance).slice(0, 10);
  }

  /**
   * Analyze sentiment of text
   */
  private async analyzeSentiment(text: string): Promise<SentimentAnalysis> {
    const positiveWords = new Set([
      'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'positive',
      'success', 'successful', 'effective', 'efficient', 'beneficial', 'advantage',
      'improve', 'better', 'best', 'outstanding', 'superior', 'quality'
    ]);

    const negativeWords = new Set([
      'bad', 'terrible', 'awful', 'horrible', 'negative', 'problem', 'issue',
      'failure', 'failed', 'error', 'wrong', 'difficult', 'challenge', 'risk',
      'disadvantage', 'worse', 'worst', 'poor', 'inferior', 'defect'
    ]);

    const words = text.toLowerCase().split(/\W+/);
    let positiveCount = 0;
    let negativeCount = 0;
    let totalSentimentWords = 0;

    words.forEach(word => {
      if (positiveWords.has(word)) {
        positiveCount++;
        totalSentimentWords++;
      } else if (negativeWords.has(word)) {
        negativeCount++;
        totalSentimentWords++;
      }
    });

    const neutralCount = Math.max(0, words.length - totalSentimentWords);
    const total = positiveCount + negativeCount + neutralCount;

    const positive = total > 0 ? positiveCount / total : 0;
    const negative = total > 0 ? negativeCount / total : 0;
    const neutral = total > 0 ? neutralCount / total : 1;

    let overall: 'positive' | 'negative' | 'neutral' = 'neutral';
    let confidence = 0.5;

    if (positive > negative && positive > 0.1) {
      overall = 'positive';
      confidence = positive;
    } else if (negative > positive && negative > 0.1) {
      overall = 'negative';
      confidence = negative;
    }

    return {
      positive,
      negative,
      neutral,
      overall,
      confidence
    };
  }

  /**
   * Calculate text complexity metrics
   */
  private async calculateComplexity(text: string): Promise<ComplexityMetrics> {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\W+/).filter(w => w.length > 0);
    const uniqueWords = new Set(words.map(w => w.toLowerCase())).size;

    const averageSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0;
    const averageWordLength = words.length > 0 ? words.reduce((sum, word) => sum + word.length, 0) / words.length : 0;

    // Simple readability score (Flesch-like)
    const readability = Math.max(0, Math.min(100, 
      206.835 - (1.015 * averageSentenceLength) - (84.6 * (averageWordLength / 4.7))
    ));

    // Vocabulary complexity
    const vocabulary = Math.min(100, (uniqueWords / words.length) * 100);

    // Structure complexity (based on sentence length variation)
    const sentenceLengths = sentences.map(s => s.split(/\W+/).length);
    const avgSentenceLength = sentenceLengths.reduce((a, b) => a + b, 0) / sentenceLengths.length;
    const variance = sentenceLengths.reduce((sum, len) => sum + Math.pow(len - avgSentenceLength, 2), 0) / sentenceLengths.length;
    const structure = Math.min(100, Math.sqrt(variance) * 5);

    return {
      readability,
      vocabulary,
      structure,
      averageSentenceLength,
      averageWordLength,
      uniqueWords
    };
  }

  /**
   * Extract keywords from text
   */
  private async extractKeywords(text: string): Promise<string[]> {
    const words = text.toLowerCase().split(/\W+/);
    const wordFreq = new Map<string, number>();

    words.forEach(word => {
      if (word.length > 3 && !this.stopWords.has(word)) {
        wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
      }
    });

    return Array.from(wordFreq.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([word]) => word);
  }

  /**
   * Generate a summary of the text
   */
  private generateSummary(text: string, topics: Topic[], entities: Entity[]): string {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const topKeywords = topics.slice(0, 3).flatMap(t => t.keywords.slice(0, 2));
    const topEntities = entities.slice(0, 3).map(e => e.text.toLowerCase());
    
    // Score sentences based on keyword and entity presence
    const scoredSentences = sentences.map(sentence => {
      const lowerSentence = sentence.toLowerCase();
      let score = 0;
      
      topKeywords.forEach(keyword => {
        if (lowerSentence.includes(keyword)) score += 2;
      });
      
      topEntities.forEach(entity => {
        if (lowerSentence.includes(entity)) score += 3;
      });
      
      return { sentence: sentence.trim(), score };
    });
    
    // Select top 3 sentences for summary
    const topSentences = scoredSentences
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(s => s.sentence);
    
    return topSentences.join('. ') + '.';
  }

  /**
   * Simple language detection
   */
  private detectLanguage(text: string): string {
    const sample = text.toLowerCase().slice(0, 1000);
    
    // Simple heuristics for common languages
    if (/\b(the|and|or|but|in|on|at|to|for|of|with|by)\b/.test(sample)) {
      return 'English';
    } else if (/\b(el|la|los|las|de|en|con|por|para|que|es|son)\b/.test(sample)) {
      return 'Spanish';
    } else if (/\b(le|la|les|de|du|des|et|ou|mais|dans|sur|avec|par)\b/.test(sample)) {
      return 'French';
    } else if (/\b(der|die|das|den|dem|des|und|oder|aber|in|auf|mit|von)\b/.test(sample)) {
      return 'German';
    }
    
    return 'Unknown';
  }

  /**
   * Find positions of text in document
   */
  private findPositions(text: string, searchText: string): number[] {
    const positions: number[] = [];
    let index = text.indexOf(searchText);
    
    while (index !== -1) {
      positions.push(index);
      index = text.indexOf(searchText, index + 1);
    }
    
    return positions;
  }

  /**
   * Simple word clustering for topic extraction
   */
  private clusterWords(words: [string, number][], text: string): Map<string, [string, number][]> {
    const clusters = new Map<string, [string, number][]>();
    
    // Simple clustering based on word co-occurrence
    words.slice(0, 20).forEach(([word, freq], index) => {
      const clusterName = `Topic ${index + 1}`;
      const relatedWords = words.filter(([w]) => 
        w !== word && this.areWordsRelated(word, w, text)
      ).slice(0, 4);
      
      clusters.set(clusterName, [[word, freq], ...relatedWords]);
    });
    
    return clusters;
  }

  /**
   * Check if two words are related based on co-occurrence
   */
  private areWordsRelated(word1: string, word2: string, text: string): boolean {
    const sentences = text.toLowerCase().split(/[.!?]+/);
    let coOccurrence = 0;
    
    sentences.forEach(sentence => {
      if (sentence.includes(word1) && sentence.includes(word2)) {
        coOccurrence++;
      }
    });
    
    return coOccurrence > 0;
  }
}

export const semanticAnalysisService = SemanticAnalysisService.getInstance();