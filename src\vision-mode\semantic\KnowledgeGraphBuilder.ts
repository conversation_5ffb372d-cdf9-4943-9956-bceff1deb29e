/**
 * Knowledge Graph Builder
 * Creates comprehensive knowledge graphs for application architecture and relationships
 */

import { SemanticElement, UIComponent, ComponentRelationship } from './SemanticAnalysisEngine.js';
import { BaseAgent } from '../agents/BaseAgent';
import { AgentTask, AgentContext, AgentConfig } from '../agents/types.js';

/**
 * Knowledge graph configuration
 */
export interface KnowledgeGraphConfig {
  enableSemanticRelations: boolean;
  enableDataFlowAnalysis: boolean;
  enableDependencyTracking: boolean;
  enableArchitecturalPatterns: boolean;
  maxGraphDepth: number;
  relationshipThreshold: number;
}

/**
 * Knowledge graph node
 */
export interface KnowledgeNode {
  id: string;
  type: NodeType;
  label: string;
  properties: Record<string, any>;
  metadata: NodeMetadata;
  coordinates?: GraphCoordinates;
  cluster?: string;
}

/**
 * Node types in the knowledge graph
 */
export enum NodeType {
  COMPONENT = 'component',
  MODULE = 'module',
  SERVICE = 'service',
  DATA_STORE = 'data-store',
  API_ENDPOINT = 'api-endpoint',
  USER_FLOW = 'user-flow',
  BUSINESS_LOGIC = 'business-logic',
  UI_PATTERN = 'ui-pattern',
  DEPENDENCY = 'dependency',
  CONFIGURATION = 'configuration',
  ASSET = 'asset',
  EVENT = 'event',
  STATE = 'state'
}

/**
 * Node metadata
 */
export interface NodeMetadata {
  createdAt: Date;
  updatedAt: Date;
  confidence: number;
  source: string;
  tags: string[];
  importance: number;
  complexity: number;
}

/**
 * Graph coordinates for visualization
 */
export interface GraphCoordinates {
  x: number;
  y: number;
  z?: number;
}

/**
 * Knowledge graph edge (relationship)
 */
export interface KnowledgeEdge {
  id: string;
  source: string;
  target: string;
  type: EdgeType;
  label: string;
  weight: number;
  properties: Record<string, any>;
  metadata: EdgeMetadata;
  bidirectional: boolean;
}

/**
 * Edge types in the knowledge graph
 */
export enum EdgeType {
  CONTAINS = 'contains',
  DEPENDS_ON = 'depends-on',
  IMPLEMENTS = 'implements',
  EXTENDS = 'extends',
  USES = 'uses',
  CALLS = 'calls',
  TRIGGERS = 'triggers',
  FLOWS_TO = 'flows-to',
  SIMILAR_TO = 'similar-to',
  PART_OF = 'part-of',
  CONFIGURES = 'configures',
  VALIDATES = 'validates',
  TRANSFORMS = 'transforms',
  STORES = 'stores',
  LOADS = 'loads'
}

/**
 * Edge metadata
 */
export interface EdgeMetadata {
  createdAt: Date;
  confidence: number;
  source: string;
  frequency?: number;
  lastUsed?: Date;
}

/**
 * Complete knowledge graph
 */
export interface KnowledgeGraph {
  id: string;
  name: string;
  description: string;
  nodes: Map<string, KnowledgeNode>;
  edges: Map<string, KnowledgeEdge>;
  clusters: Map<string, GraphCluster>;
  metadata: GraphMetadata;
  statistics: GraphStatistics;
}

/**
 * Graph cluster for grouping related nodes
 */
export interface GraphCluster {
  id: string;
  name: string;
  description: string;
  nodes: string[];
  color: string;
  importance: number;
}

/**
 * Graph metadata
 */
export interface GraphMetadata {
  version: string;
  createdAt: Date;
  updatedAt: Date;
  source: string;
  tags: string[];
  schema: string;
}

/**
 * Graph statistics
 */
export interface GraphStatistics {
  nodeCount: number;
  edgeCount: number;
  clusterCount: number;
  averageDegree: number;
  density: number;
  diameter: number;
  components: number;
  centralityScores: Map<string, number>;
}

/**
 * Architectural pattern detection
 */
export interface ArchitecturalPattern {
  name: string;
  type: 'structural' | 'behavioral' | 'creational';
  confidence: number;
  nodes: string[];
  edges: string[];
  description: string;
  benefits: string[];
  drawbacks: string[];
  alternatives: string[];
}

/**
 * Data flow analysis
 */
export interface DataFlow {
  id: string;
  name: string;
  source: string;
  target: string;
  path: string[];
  dataType: string;
  transformations: DataTransformation[];
  validations: DataValidation[];
  performance: FlowPerformance;
}

/**
 * Data transformation
 */
export interface DataTransformation {
  id: string;
  type: 'filter' | 'map' | 'reduce' | 'validate' | 'format' | 'aggregate';
  description: string;
  input: string;
  output: string;
  function?: string;
}

/**
 * Data validation
 */
export interface DataValidation {
  type: 'required' | 'type' | 'format' | 'range' | 'custom';
  rule: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

/**
 * Flow performance metrics
 */
export interface FlowPerformance {
  averageLatency: number;
  throughput: number;
  errorRate: number;
  bottlenecks: string[];
}

/**
 * Dependency analysis
 */
export interface DependencyAnalysis {
  dependencies: Dependency[];
  circularDependencies: CircularDependency[];
  criticalPath: string[];
  vulnerabilities: DependencyVulnerability[];
  recommendations: DependencyRecommendation[];
}

/**
 * Dependency information
 */
export interface Dependency {
  id: string;
  name: string;
  version?: string;
  type: 'direct' | 'transitive' | 'dev' | 'peer';
  source: string;
  target: string;
  optional: boolean;
  size?: number;
  license?: string;
}

/**
 * Circular dependency
 */
export interface CircularDependency {
  cycle: string[];
  severity: 'low' | 'medium' | 'high';
  impact: string;
  suggestions: string[];
}

/**
 * Dependency vulnerability
 */
export interface DependencyVulnerability {
  dependency: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  cve?: string;
  fixVersion?: string;
}

/**
 * Dependency recommendation
 */
export interface DependencyRecommendation {
  type: 'update' | 'remove' | 'replace' | 'optimize';
  dependency: string;
  description: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
}

/**
 * Graph query interface
 */
export interface GraphQuery {
  type: 'node' | 'edge' | 'path' | 'pattern' | 'cluster';
  filters: QueryFilter[];
  limit?: number;
  orderBy?: string;
  direction?: 'asc' | 'desc';
}

/**
 * Query filter
 */
export interface QueryFilter {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'in';
  value: any;
}

/**
 * Graph analysis result
 */
export interface GraphAnalysisResult {
  patterns: ArchitecturalPattern[];
  dataFlows: DataFlow[];
  dependencies: DependencyAnalysis;
  recommendations: AnalysisRecommendation[];
  insights: AnalysisInsight[];
}

/**
 * Analysis recommendation
 */
export interface AnalysisRecommendation {
  type: 'architecture' | 'performance' | 'security' | 'maintainability';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  implementation: string;
  impact: string;
  effort: string;
}

/**
 * Analysis insight
 */
export interface AnalysisInsight {
  type: 'observation' | 'trend' | 'anomaly' | 'opportunity';
  title: string;
  description: string;
  confidence: number;
  evidence: string[];
  implications: string[];
}

/**
 * Knowledge Graph Builder
 */
export class KnowledgeGraphBuilder extends BaseAgent {
  private config: KnowledgeGraphConfig;
  private graphs: Map<string, KnowledgeGraph> = new Map();
  private patterns: Map<string, ArchitecturalPattern> = new Map();

  constructor(config: Partial<KnowledgeGraphConfig> = {}) {
    super({
      id: 'knowledge-graph-builder',
      name: 'Knowledge Graph Builder',
      description: 'Builds comprehensive knowledge graphs for application architecture',
      capabilities: [
        'graph-construction',
        'relationship-analysis',
        'pattern-detection',
        'dependency-analysis',
        'data-flow-analysis'
      ],
      version: '1.0.0'
    });

    this.config = {
      enableSemanticRelations: true,
      enableDataFlowAnalysis: true,
      enableDependencyTracking: true,
      enableArchitecturalPatterns: true,
      maxGraphDepth: 10,
      relationshipThreshold: 0.5,
      ...config
    };

    this.initializePatterns();
  }

  /**
   * Initialize architectural patterns
   */
  private initializePatterns(): void {
    this.patterns.set('mvc', {
      name: 'Model-View-Controller',
      type: 'structural',
      confidence: 0.9,
      nodes: [],
      edges: [],
      description: 'Separates application logic into three interconnected components',
      benefits: ['Separation of concerns', 'Testability', 'Maintainability'],
      drawbacks: ['Complexity for simple apps', 'Potential for tight coupling'],
      alternatives: ['MVP', 'MVVM', 'Component-based']
    });

    this.patterns.set('observer', {
      name: 'Observer Pattern',
      type: 'behavioral',
      confidence: 0.8,
      nodes: [],
      edges: [],
      description: 'Defines a one-to-many dependency between objects',
      benefits: ['Loose coupling', 'Dynamic relationships', 'Broadcast communication'],
      drawbacks: ['Memory leaks if not managed', 'Debugging complexity'],
      alternatives: ['Event bus', 'Mediator', 'Publish-subscribe']
    });

    this.patterns.set('singleton', {
      name: 'Singleton Pattern',
      type: 'creational',
      confidence: 0.7,
      nodes: [],
      edges: [],
      description: 'Ensures a class has only one instance',
      benefits: ['Controlled access', 'Reduced memory footprint'],
      drawbacks: ['Global state', 'Testing difficulties', 'Tight coupling'],
      alternatives: ['Dependency injection', 'Factory pattern']
    });
  }

  /**
   * Build knowledge graph from semantic elements
   */
  async buildGraph(
    elements: SemanticElement[],
    components: UIComponent[],
    context?: AgentContext
  ): Promise<KnowledgeGraph> {
    console.log('Building knowledge graph...');

    const graph: KnowledgeGraph = {
      id: `graph-${Date.now()}`,
      name: 'Application Knowledge Graph',
      description: 'Comprehensive graph of application architecture and relationships',
      nodes: new Map(),
      edges: new Map(),
      clusters: new Map(),
      metadata: {
        version: '1.0.0',
        createdAt: new Date(),
        updatedAt: new Date(),
        source: 'semantic-analysis',
        tags: ['ui', 'architecture', 'components'],
        schema: 'knowledge-graph-v1'
      },
      statistics: {
        nodeCount: 0,
        edgeCount: 0,
        clusterCount: 0,
        averageDegree: 0,
        density: 0,
        diameter: 0,
        components: 0,
        centralityScores: new Map()
      }
    };

    // Create nodes from elements and components
    await this.createNodes(graph, elements, components);

    // Create edges from relationships
    await this.createEdges(graph, elements, components);

    // Create clusters
    await this.createClusters(graph);

    // Calculate statistics
    this.calculateStatistics(graph);

    // Store graph
    this.graphs.set(graph.id, graph);

    console.log(`Knowledge graph built with ${graph.nodes.size} nodes and ${graph.edges.size} edges`);
    return graph;
  }

  /**
   * Create nodes from elements and components
   */
  private async createNodes(
    graph: KnowledgeGraph,
    elements: SemanticElement[],
    components: UIComponent[]
  ): Promise<void> {
    // Create nodes from semantic elements
    for (const element of elements) {
      const node: KnowledgeNode = {
        id: element.id,
        type: this.mapElementToNodeType(element),
        label: this.generateNodeLabel(element),
        properties: {
          elementType: element.type,
          semanticRole: element.semanticRole,
          bounds: element.bounds,
          confidence: element.confidence,
          ...element.properties
        },
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          confidence: element.confidence,
          source: 'semantic-element',
          tags: [element.type, element.semanticRole],
          importance: this.calculateNodeImportance(element),
          complexity: this.calculateNodeComplexity(element)
        }
      };

      graph.nodes.set(node.id, node);
    }

    // Create nodes from UI components
    for (const component of components) {
      const existingNode = graph.nodes.get(component.id);
      if (existingNode) {
        // Enhance existing node with component data
        existingNode.properties = {
          ...existingNode.properties,
          componentType: component.type,
          library: component.library,
          state: component.state,
          events: component.events,
          accessibility: component.accessibility,
          performance: component.performance
        };
        existingNode.metadata.tags.push('ui-component');
      } else {
        // Create new node for component
        const node: KnowledgeNode = {
          id: component.id,
          type: NodeType.COMPONENT,
          label: this.generateComponentLabel(component),
          properties: {
            componentType: component.type,
            library: component.library,
            props: component.props,
            state: component.state,
            events: component.events,
            accessibility: component.accessibility,
            performance: component.performance,
            semanticMeaning: component.semanticMeaning
          },
          metadata: {
            createdAt: new Date(),
            updatedAt: new Date(),
            confidence: 0.9,
            source: 'ui-component',
            tags: ['ui-component', component.type],
            importance: component.semanticMeaning.criticalityScore,
            complexity: component.performance.complexity
          }
        };

        graph.nodes.set(node.id, node);
      }
    }
  }

  /**
   * Map semantic element to node type
   */
  private mapElementToNodeType(element: SemanticElement): NodeType {
    switch (element.type) {
      case 'button':
      case 'input':
      case 'form':
        return NodeType.COMPONENT;
      case 'navigation':
        return NodeType.MODULE;
      case 'container':
        return NodeType.MODULE;
      case 'image':
        return NodeType.ASSET;
      default:
        return NodeType.COMPONENT;
    }
  }

  /**
   * Generate node label
   */
  private generateNodeLabel(element: SemanticElement): string {
    const type = element.type.charAt(0).toUpperCase() + element.type.slice(1);
    const role = element.semanticRole ? ` (${element.semanticRole})` : '';
    return `${type}${role}`;
  }

  /**
   * Generate component label
   */
  private generateComponentLabel(component: UIComponent): string {
    const type = component.type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    const library = component.library ? ` [${component.library.name}]` : '';
    return `${type}${library}`;
  }

  /**
   * Calculate node importance
   */
  private calculateNodeImportance(element: SemanticElement): number {
    let importance = 0.5;

    // Increase importance for interactive elements
    if (['button', 'input', 'form', 'navigation'].includes(element.type)) {
      importance += 0.3;
    }

    // Increase importance for elements with many children
    importance += Math.min(element.children.length * 0.1, 0.3);

    // Increase importance based on confidence
    importance += element.confidence * 0.2;

    return Math.min(importance, 1.0);
  }

  /**
   * Calculate node complexity
   */
  private calculateNodeComplexity(element: SemanticElement): number {
    let complexity = 1;

    // Add complexity for children
    complexity += element.children.length;

    // Add complexity for properties
    complexity += Object.keys(element.properties).length * 0.5;

    // Add complexity for specific types
    if (element.type === 'form') complexity += 5;
    if (element.type === 'table') complexity += 3;
    if (element.type === 'navigation') complexity += 2;

    return complexity;
  }

  /**
   * Create edges from relationships
   */
  private async createEdges(
    graph: KnowledgeGraph,
    elements: SemanticElement[],
    components: UIComponent[]
  ): Promise<void> {
    // Create parent-child relationships
    for (const element of elements) {
      for (const childId of element.children) {
        const edge: KnowledgeEdge = {
          id: `${element.id}-contains-${childId}`,
          source: element.id,
          target: childId,
          type: EdgeType.CONTAINS,
          label: 'contains',
          weight: 1.0,
          properties: {
            relationship: 'parent-child'
          },
          metadata: {
            createdAt: new Date(),
            confidence: 1.0,
            source: 'semantic-hierarchy'
          },
          bidirectional: false
        };

        graph.edges.set(edge.id, edge);
      }
    }

    // Create component relationships
    for (const component of components) {
      for (const relationship of component.relationships) {
        const edge: KnowledgeEdge = {
          id: `${component.id}-${relationship.type}-${relationship.target}`,
          source: component.id,
          target: relationship.target,
          type: this.mapRelationshipToEdgeType(relationship.type),
          label: relationship.description,
          weight: relationship.strength,
          properties: {
            relationshipType: relationship.type,
            description: relationship.description
          },
          metadata: {
            createdAt: new Date(),
            confidence: relationship.strength,
            source: 'component-analysis'
          },
          bidirectional: relationship.type === 'sibling'
        };

        graph.edges.set(edge.id, edge);
      }
    }

    // Create semantic relationships
    if (this.config.enableSemanticRelations) {
      await this.createSemanticRelationships(graph, elements, components);
    }

    // Create data flow relationships
    if (this.config.enableDataFlowAnalysis) {
      await this.createDataFlowRelationships(graph, elements, components);
    }
  }

  /**
   * Map component relationship to edge type
   */
  private mapRelationshipToEdgeType(relationshipType: string): EdgeType {
    switch (relationshipType) {
      case 'parent': return EdgeType.CONTAINS;
      case 'child': return EdgeType.PART_OF;
      case 'dependency': return EdgeType.DEPENDS_ON;
      case 'composition': return EdgeType.CONTAINS;
      case 'aggregation': return EdgeType.USES;
      default: return EdgeType.USES;
    }
  }

  /**
   * Create semantic relationships
   */
  private async createSemanticRelationships(
    graph: KnowledgeGraph,
    elements: SemanticElement[],
    components: UIComponent[]
  ): Promise<void> {
    // Find similar components
    for (let i = 0; i < components.length; i++) {
      for (let j = i + 1; j < components.length; j++) {
        const similarity = this.calculateSimilarity(components[i], components[j]);
        
        if (similarity > this.config.relationshipThreshold) {
          const edge: KnowledgeEdge = {
            id: `${components[i].id}-similar-${components[j].id}`,
            source: components[i].id,
            target: components[j].id,
            type: EdgeType.SIMILAR_TO,
            label: `Similar (${Math.round(similarity * 100)}%)`,
            weight: similarity,
            properties: {
              similarity,
              reasons: this.getSimilarityReasons(components[i], components[j])
            },
            metadata: {
              createdAt: new Date(),
              confidence: similarity,
              source: 'semantic-analysis'
            },
            bidirectional: true
          };

          graph.edges.set(edge.id, edge);
        }
      }
    }
  }

  /**
   * Calculate similarity between components
   */
  private calculateSimilarity(comp1: UIComponent, comp2: UIComponent): number {
    let similarity = 0;
    let factors = 0;

    // Type similarity
    if (comp1.type === comp2.type) {
      similarity += 0.4;
    }
    factors++;

    // Library similarity
    if (comp1.library && comp2.library && comp1.library.name === comp2.library.name) {
      similarity += 0.2;
    }
    factors++;

    // Purpose similarity
    if (comp1.semanticMeaning.purpose === comp2.semanticMeaning.purpose) {
      similarity += 0.3;
    }
    factors++;

    // Pattern similarity
    const commonPatterns = comp1.patterns.filter(p1 => 
      comp2.patterns.some(p2 => p1.name === p2.name)
    );
    if (commonPatterns.length > 0) {
      similarity += 0.1 * commonPatterns.length;
    }
    factors++;

    return similarity / factors;
  }

  /**
   * Get similarity reasons
   */
  private getSimilarityReasons(comp1: UIComponent, comp2: UIComponent): string[] {
    const reasons: string[] = [];

    if (comp1.type === comp2.type) {
      reasons.push('Same component type');
    }

    if (comp1.library && comp2.library && comp1.library.name === comp2.library.name) {
      reasons.push('Same component library');
    }

    if (comp1.semanticMeaning.purpose === comp2.semanticMeaning.purpose) {
      reasons.push('Same purpose');
    }

    const commonPatterns = comp1.patterns.filter(p1 => 
      comp2.patterns.some(p2 => p1.name === p2.name)
    );
    if (commonPatterns.length > 0) {
      reasons.push(`Common patterns: ${commonPatterns.map(p => p.name).join(', ')}`);
    }

    return reasons;
  }

  /**
   * Create data flow relationships
   */
  private async createDataFlowRelationships(
    graph: KnowledgeGraph,
    elements: SemanticElement[],
    components: UIComponent[]
  ): Promise<void> {
    // Find form inputs and their targets
    const forms = elements.filter(e => e.type === 'form');
    
    for (const form of forms) {
      const inputs = form.children
        .map(childId => elements.find(e => e.id === childId))
        .filter(e => e && e.type === 'input');
      
      const submitButton = form.children
        .map(childId => elements.find(e => e.id === childId))
        .find(e => e && e.type === 'button' && e.properties.type === 'submit');
      
      // Create data flow from inputs to submit button
      for (const input of inputs) {
        if (input && submitButton) {
          const edge: KnowledgeEdge = {
            id: `${input.id}-flows-to-${submitButton.id}`,
            source: input.id,
            target: submitButton.id,
            type: EdgeType.FLOWS_TO,
            label: 'data flows to',
            weight: 0.8,
            properties: {
              dataType: input.properties.type || 'text',
              required: input.properties.required || false
            },
            metadata: {
              createdAt: new Date(),
              confidence: 0.8,
              source: 'data-flow-analysis'
            },
            bidirectional: false
          };

          graph.edges.set(edge.id, edge);
        }
      }
    }
  }

  /**
   * Create clusters
   */
  private async createClusters(graph: KnowledgeGraph): Promise<void> {
    // Create clusters by component type
    const typeGroups = new Map<string, string[]>();
    
    for (const [nodeId, node] of graph.nodes) {
      const type = node.properties.componentType || node.type;
      if (!typeGroups.has(type)) {
        typeGroups.set(type, []);
      }
      typeGroups.get(type)!.push(nodeId);
    }

    let clusterIndex = 0;
    for (const [type, nodeIds] of typeGroups) {
      if (nodeIds.length > 1) {
        const cluster: GraphCluster = {
          id: `cluster-${clusterIndex++}`,
          name: `${type.charAt(0).toUpperCase() + type.slice(1)} Components`,
          description: `All components of type ${type}`,
          nodes: nodeIds,
          color: this.generateClusterColor(type),
          importance: this.calculateClusterImportance(nodeIds, graph)
        };

        graph.clusters.set(cluster.id, cluster);
        
        // Update nodes with cluster information
        for (const nodeId of nodeIds) {
          const node = graph.nodes.get(nodeId);
          if (node) {
            node.cluster = cluster.id;
          }
        }
      }
    }

    // Create clusters by semantic role
    const roleGroups = new Map<string, string[]>();
    
    for (const [nodeId, node] of graph.nodes) {
      const role = node.properties.semanticRole;
      if (role) {
        if (!roleGroups.has(role)) {
          roleGroups.set(role, []);
        }
        roleGroups.get(role)!.push(nodeId);
      }
    }

    for (const [role, nodeIds] of roleGroups) {
      if (nodeIds.length > 1) {
        const cluster: GraphCluster = {
          id: `cluster-${clusterIndex++}`,
          name: `${role.charAt(0).toUpperCase() + role.slice(1)} Elements`,
          description: `All elements with semantic role ${role}`,
          nodes: nodeIds,
          color: this.generateClusterColor(role),
          importance: this.calculateClusterImportance(nodeIds, graph)
        };

        graph.clusters.set(cluster.id, cluster);
      }
    }
  }

  /**
   * Generate cluster color
   */
  private generateClusterColor(type: string): string {
    const colors = {
      'button': '#3B82F6',
      'input': '#10B981',
      'form': '#F59E0B',
      'navigation': '#8B5CF6',
      'container': '#6B7280',
      'text': '#374151',
      'image': '#EC4899',
      'default': '#9CA3AF'
    };

    return colors[type as keyof typeof colors] || colors.default;
  }

  /**
   * Calculate cluster importance
   */
  private calculateClusterImportance(nodeIds: string[], graph: KnowledgeGraph): number {
    let totalImportance = 0;
    let nodeCount = 0;

    for (const nodeId of nodeIds) {
      const node = graph.nodes.get(nodeId);
      if (node) {
        totalImportance += node.metadata.importance;
        nodeCount++;
      }
    }

    return nodeCount > 0 ? totalImportance / nodeCount : 0;
  }

  /**
   * Calculate graph statistics
   */
  private calculateStatistics(graph: KnowledgeGraph): void {
    const stats = graph.statistics;
    
    stats.nodeCount = graph.nodes.size;
    stats.edgeCount = graph.edges.size;
    stats.clusterCount = graph.clusters.size;
    
    // Calculate average degree
    const degrees = new Map<string, number>();
    for (const [edgeId, edge] of graph.edges) {
      degrees.set(edge.source, (degrees.get(edge.source) || 0) + 1);
      degrees.set(edge.target, (degrees.get(edge.target) || 0) + 1);
    }
    
    const totalDegree = Array.from(degrees.values()).reduce((sum, degree) => sum + degree, 0);
    stats.averageDegree = stats.nodeCount > 0 ? totalDegree / stats.nodeCount : 0;
    
    // Calculate density
    const maxEdges = stats.nodeCount * (stats.nodeCount - 1) / 2;
    stats.density = maxEdges > 0 ? stats.edgeCount / maxEdges : 0;
    
    // Calculate centrality scores (simplified)
    for (const [nodeId, node] of graph.nodes) {
      const degree = degrees.get(nodeId) || 0;
      const centrality = stats.nodeCount > 1 ? degree / (stats.nodeCount - 1) : 0;
      stats.centralityScores.set(nodeId, centrality);
    }
  }

  /**
   * Analyze graph for patterns and insights
   */
  async analyzeGraph(graphId: string): Promise<GraphAnalysisResult> {
    const graph = this.graphs.get(graphId);
    if (!graph) {
      throw new Error(`Graph not found: ${graphId}`);
    }

    console.log('Analyzing knowledge graph...');

    const patterns = await this.detectArchitecturalPatterns(graph);
    const dataFlows = await this.analyzeDataFlows(graph);
    const dependencies = await this.analyzeDependencies(graph);
    const recommendations = await this.generateRecommendations(graph, patterns, dependencies);
    const insights = await this.generateInsights(graph, patterns, dataFlows);

    return {
      patterns,
      dataFlows,
      dependencies,
      recommendations,
      insights
    };
  }

  /**
   * Detect architectural patterns
   */
  private async detectArchitecturalPatterns(graph: KnowledgeGraph): Promise<ArchitecturalPattern[]> {
    const detectedPatterns: ArchitecturalPattern[] = [];

    // Detect MVC pattern
    const mvcPattern = await this.detectMVCPattern(graph);
    if (mvcPattern) {
      detectedPatterns.push(mvcPattern);
    }

    // Detect Observer pattern
    const observerPattern = await this.detectObserverPattern(graph);
    if (observerPattern) {
      detectedPatterns.push(observerPattern);
    }

    // Detect Singleton pattern
    const singletonPattern = await this.detectSingletonPattern(graph);
    if (singletonPattern) {
      detectedPatterns.push(singletonPattern);
    }

    return detectedPatterns;
  }

  /**
   * Detect MVC pattern
   */
  private async detectMVCPattern(graph: KnowledgeGraph): Promise<ArchitecturalPattern | null> {
    const models: string[] = [];
    const views: string[] = [];
    const controllers: string[] = [];

    for (const [nodeId, node] of graph.nodes) {
      const role = node.properties.semanticRole;
      const type = node.properties.componentType;
      
      if (role === 'data' || type === 'data-store') {
        models.push(nodeId);
      } else if (role === 'presentation' || type === 'view') {
        views.push(nodeId);
      } else if (role === 'control' || type === 'controller') {
        controllers.push(nodeId);
      }
    }

    if (models.length > 0 && views.length > 0 && controllers.length > 0) {
      const pattern = { ...this.patterns.get('mvc')! };
      pattern.nodes = [...models, ...views, ...controllers];
      pattern.confidence = Math.min(models.length, views.length, controllers.length) / Math.max(models.length, views.length, controllers.length);
      
      return pattern;
    }

    return null;
  }

  /**
   * Detect Observer pattern
   */
  private async detectObserverPattern(graph: KnowledgeGraph): Promise<ArchitecturalPattern | null> {
    const observers: string[] = [];
    const subjects: string[] = [];
    const observerEdges: string[] = [];

    // Look for nodes with many outgoing edges (potential subjects)
    for (const [nodeId, node] of graph.nodes) {
      const outgoingEdges = Array.from(graph.edges.values()).filter(e => e.source === nodeId);
      
      if (outgoingEdges.length > 2) {
        subjects.push(nodeId);
        
        // Check if edges represent observer relationships
        const notificationEdges = outgoingEdges.filter(e => 
          e.type === EdgeType.TRIGGERS || 
          e.label.includes('notify') || 
          e.label.includes('update')
        );
        
        if (notificationEdges.length > 1) {
          observers.push(...notificationEdges.map(e => e.target));
          observerEdges.push(...notificationEdges.map(e => e.id));
        }
      }
    }

    if (subjects.length > 0 && observers.length > 1) {
      const pattern = { ...this.patterns.get('observer')! };
      pattern.nodes = [...subjects, ...observers];
      pattern.edges = observerEdges;
      pattern.confidence = Math.min(observers.length / subjects.length, 1.0);
      
      return pattern;
    }

    return null;
  }

  /**
   * Detect Singleton pattern
   */
  private async detectSingletonPattern(graph: KnowledgeGraph): Promise<ArchitecturalPattern | null> {
    const singletons: string[] = [];

    for (const [nodeId, node] of graph.nodes) {
      // Look for nodes with global access patterns
      const incomingEdges = Array.from(graph.edges.values()).filter(e => e.target === nodeId);
      const outgoingEdges = Array.from(graph.edges.values()).filter(e => e.source === nodeId);
      
      // Heuristic: many incoming edges, few outgoing edges, high importance
      if (incomingEdges.length > 3 && outgoingEdges.length < 2 && node.metadata.importance > 0.8) {
        singletons.push(nodeId);
      }
    }

    if (singletons.length > 0) {
      const pattern = { ...this.patterns.get('singleton')! };
      pattern.nodes = singletons;
      pattern.confidence = 0.6; // Lower confidence as this is heuristic-based
      
      return pattern;
    }

    return null;
  }

  /**
   * Analyze data flows
   */
  private async analyzeDataFlows(graph: KnowledgeGraph): Promise<DataFlow[]> {
    const dataFlows: DataFlow[] = [];

    // Find flow edges
    const flowEdges = Array.from(graph.edges.values()).filter(e => 
      e.type === EdgeType.FLOWS_TO || e.type === EdgeType.TRANSFORMS
    );

    // Group edges into flows
    const flowPaths = this.findFlowPaths(flowEdges, graph);

    for (const path of flowPaths) {
      const flow: DataFlow = {
        id: `flow-${path.join('-')}`,
        name: `Data Flow: ${path[0]} → ${path[path.length - 1]}`,
        source: path[0],
        target: path[path.length - 1],
        path,
        dataType: 'unknown',
        transformations: [],
        validations: [],
        performance: {
          averageLatency: 100, // Estimated
          throughput: 1000, // Estimated
          errorRate: 0.01, // Estimated
          bottlenecks: []
        }
      };

      dataFlows.push(flow);
    }

    return dataFlows;
  }

  /**
   * Find flow paths in the graph
   */
  private findFlowPaths(flowEdges: KnowledgeEdge[], graph: KnowledgeGraph): string[][] {
    const paths: string[][] = [];
    const visited = new Set<string>();

    for (const edge of flowEdges) {
      if (!visited.has(edge.id)) {
        const path = this.tracePath(edge.source, flowEdges, visited);
        if (path.length > 1) {
          paths.push(path);
        }
      }
    }

    return paths;
  }

  /**
   * Trace a path from a starting node
   */
  private tracePath(startNode: string, flowEdges: KnowledgeEdge[], visited: Set<string>): string[] {
    const path = [startNode];
    let currentNode = startNode;

    while (true) {
      const nextEdge = flowEdges.find(e => 
        e.source === currentNode && !visited.has(e.id)
      );

      if (!nextEdge) break;

      visited.add(nextEdge.id);
      currentNode = nextEdge.target;
      path.push(currentNode);

      // Prevent infinite loops
      if (path.length > 10) break;
    }

    return path;
  }

  /**
   * Analyze dependencies
   */
  private async analyzeDependencies(graph: KnowledgeGraph): Promise<DependencyAnalysis> {
    const dependencies: Dependency[] = [];
    const circularDependencies: CircularDependency[] = [];
    const vulnerabilities: DependencyVulnerability[] = [];
    const recommendations: DependencyRecommendation[] = [];

    // Find dependency edges
    const dependencyEdges = Array.from(graph.edges.values()).filter(e => 
      e.type === EdgeType.DEPENDS_ON || e.type === EdgeType.USES
    );

    // Create dependency objects
    for (const edge of dependencyEdges) {
      const dependency: Dependency = {
        id: edge.id,
        name: edge.label,
        type: 'direct',
        source: edge.source,
        target: edge.target,
        optional: edge.weight < 0.5
      };

      dependencies.push(dependency);
    }

    // Detect circular dependencies
    const cycles = this.detectCycles(dependencyEdges);
    for (const cycle of cycles) {
      const circularDep: CircularDependency = {
        cycle,
        severity: cycle.length > 3 ? 'high' : 'medium',
        impact: 'May cause initialization issues and tight coupling',
        suggestions: ['Introduce dependency injection', 'Use interfaces', 'Refactor architecture']
      };

      circularDependencies.push(circularDep);
    }

    // Find critical path
    const criticalPath = this.findCriticalPath(dependencyEdges, graph);

    return {
      dependencies,
      circularDependencies,
      criticalPath,
      vulnerabilities,
      recommendations
    };
  }

  /**
   * Detect cycles in dependency graph
   */
  private detectCycles(dependencyEdges: KnowledgeEdge[]): string[][] {
    const cycles: string[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    // Build adjacency list
    const adjacencyList = new Map<string, string[]>();
    for (const edge of dependencyEdges) {
      if (!adjacencyList.has(edge.source)) {
        adjacencyList.set(edge.source, []);
      }
      adjacencyList.get(edge.source)!.push(edge.target);
    }

    // DFS to detect cycles
    const dfs = (node: string, path: string[]): void => {
      visited.add(node);
      recursionStack.add(node);
      path.push(node);

      const neighbors = adjacencyList.get(node) || [];
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          dfs(neighbor, [...path]);
        } else if (recursionStack.has(neighbor)) {
          // Found a cycle
          const cycleStart = path.indexOf(neighbor);
          if (cycleStart !== -1) {
            cycles.push(path.slice(cycleStart));
          }
        }
      }

      recursionStack.delete(node);
    };

    // Start DFS from all unvisited nodes
    for (const edge of dependencyEdges) {
      if (!visited.has(edge.source)) {
        dfs(edge.source, []);
      }
    }

    return cycles;
  }

  /**
   * Find critical path
   */
  private findCriticalPath(dependencyEdges: KnowledgeEdge[], graph: KnowledgeGraph): string[] {
    // Find the longest path in the dependency graph
    const paths: string[][] = [];
    const visited = new Set<string>();

    // Build adjacency list
    const adjacencyList = new Map<string, string[]>();
    for (const edge of dependencyEdges) {
      if (!adjacencyList.has(edge.source)) {
        adjacencyList.set(edge.source, []);
      }
      adjacencyList.get(edge.source)!.push(edge.target);
    }

    // Find all paths
    const findPaths = (node: string, path: string[]): void => {
      const neighbors = adjacencyList.get(node) || [];
      
      if (neighbors.length === 0) {
        // End of path
        paths.push([...path, node]);
        return;
      }

      for (const neighbor of neighbors) {
        if (!path.includes(neighbor)) { // Avoid cycles
          findPaths(neighbor, [...path, node]);
        }
      }
    };

    // Start from nodes with no incoming edges
    const nodesWithIncoming = new Set(dependencyEdges.map(e => e.target));
    const startNodes = Array.from(new Set(dependencyEdges.map(e => e.source)))
      .filter(node => !nodesWithIncoming.has(node));

    for (const startNode of startNodes) {
      findPaths(startNode, []);
    }

    // Return the longest path
    return paths.reduce((longest, current) => 
      current.length > longest.length ? current : longest, []
    );
  }

  /**
   * Generate recommendations
   */
  private async generateRecommendations(
    graph: KnowledgeGraph,
    patterns: ArchitecturalPattern[],
    dependencies: DependencyAnalysis
  ): Promise<AnalysisRecommendation[]> {
    const recommendations: AnalysisRecommendation[] = [];

    // Architecture recommendations
    if (patterns.length === 0) {
      recommendations.push({
        type: 'architecture',
        priority: 'medium',
        title: 'Consider Implementing Architectural Patterns',
        description: 'No clear architectural patterns detected. Consider implementing MVC, Observer, or other patterns.',
        implementation: 'Refactor code to follow established architectural patterns',
        impact: 'Improved maintainability and code organization',
        effort: 'high'
      });
    }

    // Dependency recommendations
    if (dependencies.circularDependencies.length > 0) {
      recommendations.push({
        type: 'architecture',
        priority: 'high',
        title: 'Resolve Circular Dependencies',
        description: `Found ${dependencies.circularDependencies.length} circular dependencies that may cause issues.`,
        implementation: 'Use dependency injection, interfaces, or refactor architecture',
        impact: 'Reduced coupling and improved testability',
        effort: 'medium'
      });
    }

    // Performance recommendations
    const highComplexityNodes = Array.from(graph.nodes.values())
      .filter(node => node.metadata.complexity > 10);
    
    if (highComplexityNodes.length > 0) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        title: 'Reduce Component Complexity',
        description: `${highComplexityNodes.length} components have high complexity and may impact performance.`,
        implementation: 'Break down complex components into smaller, focused components',
        impact: 'Improved performance and maintainability',
        effort: 'medium'
      });
    }

    // Maintainability recommendations
    if (graph.statistics.averageDegree > 5) {
      recommendations.push({
        type: 'maintainability',
        priority: 'low',
        title: 'Reduce Component Coupling',
        description: 'High average degree suggests tight coupling between components.',
        implementation: 'Use interfaces, events, or dependency injection to reduce coupling',
        impact: 'Improved maintainability and testability',
        effort: 'medium'
      });
    }

    return recommendations;
  }

  /**
   * Generate insights
   */
  private async generateInsights(
    graph: KnowledgeGraph,
    patterns: ArchitecturalPattern[],
    dataFlows: DataFlow[]
  ): Promise<AnalysisInsight[]> {
    const insights: AnalysisInsight[] = [];

    // Graph structure insights
    insights.push({
      type: 'observation',
      title: 'Graph Structure Analysis',
      description: `The application has ${graph.nodes.size} components with ${graph.edges.size} relationships, organized in ${graph.clusters.size} clusters.`,
      confidence: 1.0,
      evidence: [
        `Node count: ${graph.nodes.size}`,
        `Edge count: ${graph.edges.size}`,
        `Cluster count: ${graph.clusters.size}`,
        `Average degree: ${graph.statistics.averageDegree.toFixed(2)}`,
        `Density: ${graph.statistics.density.toFixed(3)}`
      ],
      implications: [
        'Component organization and relationships are well-defined',
        'Graph density indicates level of coupling between components'
      ]
    });

    // Pattern insights
    if (patterns.length > 0) {
      insights.push({
        type: 'observation',
        title: 'Architectural Patterns Detected',
        description: `Found ${patterns.length} architectural patterns: ${patterns.map(p => p.name).join(', ')}.`,
        confidence: patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length,
        evidence: patterns.map(p => `${p.name} (${Math.round(p.confidence * 100)}% confidence)`),
        implications: [
          'Good architectural structure is in place',
          'Code follows established design patterns',
          'Maintainability and extensibility are likely good'
        ]
      });
    }

    // Data flow insights
    if (dataFlows.length > 0) {
      insights.push({
        type: 'observation',
        title: 'Data Flow Analysis',
        description: `Identified ${dataFlows.length} data flows in the application.`,
        confidence: 0.8,
        evidence: dataFlows.map(f => `${f.name}: ${f.path.length} steps`),
        implications: [
          'Data movement through the application is traceable',
          'Potential optimization opportunities exist in data flows'
        ]
      });
    }

    // Centrality insights
    const centralNodes = Array.from(graph.statistics.centralityScores.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);
    
    if (centralNodes.length > 0) {
      insights.push({
        type: 'observation',
        title: 'Central Components Identified',
        description: 'Found components with high centrality that are critical to the application.',
        confidence: 0.9,
        evidence: centralNodes.map(([nodeId, centrality]) => {
          const node = graph.nodes.get(nodeId);
          return `${node?.label || nodeId}: ${Math.round(centrality * 100)}% centrality`;
        }),
        implications: [
          'These components are critical to application functionality',
          'Changes to these components may have wide-reaching effects',
          'Extra attention should be paid to testing and maintenance'
        ]
      });
    }

    return insights;
  }

  /**
   * Query the knowledge graph
   */
  async queryGraph(graphId: string, query: GraphQuery): Promise<any[]> {
    const graph = this.graphs.get(graphId);
    if (!graph) {
      throw new Error(`Graph not found: ${graphId}`);
    }

    let results: any[] = [];

    switch (query.type) {
      case 'node':
        results = Array.from(graph.nodes.values());
        break;
      case 'edge':
        results = Array.from(graph.edges.values());
        break;
      case 'cluster':
        results = Array.from(graph.clusters.values());
        break;
      case 'path':
        // Implement path finding
        results = this.findPaths(graph, query);
        break;
      case 'pattern':
        // Implement pattern matching
        results = await this.matchPatterns(graph, query);
        break;
    }

    // Apply filters
    for (const filter of query.filters) {
      results = results.filter(item => this.applyFilter(item, filter));
    }

    // Apply ordering
    if (query.orderBy) {
      results.sort((a, b) => {
        const aVal = this.getNestedProperty(a, query.orderBy!);
        const bVal = this.getNestedProperty(b, query.orderBy!);
        
        if (query.direction === 'desc') {
          return bVal > aVal ? 1 : -1;
        } else {
          return aVal > bVal ? 1 : -1;
        }
      });
    }

    // Apply limit
    if (query.limit) {
      results = results.slice(0, query.limit);
    }

    return results;
  }

  /**
   * Apply filter to item
   */
  private applyFilter(item: any, filter: QueryFilter): boolean {
    const value = this.getNestedProperty(item, filter.field);
    
    switch (filter.operator) {
      case 'equals':
        return value === filter.value;
      case 'contains':
        return String(value).includes(String(filter.value));
      case 'startsWith':
        return String(value).startsWith(String(filter.value));
      case 'endsWith':
        return String(value).endsWith(String(filter.value));
      case 'gt':
        return Number(value) > Number(filter.value);
      case 'lt':
        return Number(value) < Number(filter.value);
      case 'in':
        return Array.isArray(filter.value) && filter.value.includes(value);
      default:
        return true;
    }
  }

  /**
   * Get nested property value
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Find paths in graph
   */
  private findPaths(graph: KnowledgeGraph, query: GraphQuery): any[] {
    // Simplified path finding implementation
    const paths: any[] = [];
    
    // This would implement more sophisticated path finding algorithms
    // For now, return empty array
    
    return paths;
  }

  /**
   * Match patterns in graph
   */
  private async matchPatterns(graph: KnowledgeGraph, query: GraphQuery): Promise<any[]> {
    // Simplified pattern matching implementation
    const matches: any[] = [];
    
    // This would implement graph pattern matching algorithms
    // For now, return empty array
    
    return matches;
  }

  /**
   * Execute agent task
   */
  async executeTask(task: AgentTask, context?: AgentContext): Promise<any> {
    console.log(`Executing knowledge graph task: ${task.type}`);
    
    switch (task.type) {
      case 'build-graph':
        return await this.buildGraph(task.data.elements, task.data.components, context);
      case 'analyze-graph':
        return await this.analyzeGraph(task.data.graphId);
      case 'query-graph':
        return await this.queryGraph(task.data.graphId, task.data.query);
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  /**
   * Get available graphs
   */
  getGraphs(): KnowledgeGraph[] {
    return Array.from(this.graphs.values());
  }

  /**
   * Get graph by ID
   */
  getGraph(id: string): KnowledgeGraph | undefined {
    return this.graphs.get(id);
  }

  /**
   * Delete graph
   */
  deleteGraph(id: string): boolean {
    return this.graphs.delete(id);
  }
}

// Export types for external use
export type {
  KnowledgeGraphConfig,
  KnowledgeNode,
  NodeType,
  NodeMetadata,
  GraphCoordinates,
  KnowledgeEdge,
  EdgeType,
  EdgeMetadata,
  KnowledgeGraph,
  GraphCluster,
  GraphMetadata,
  GraphStatistics,
  ArchitecturalPattern,
  DataFlow,
  DataTransformation,
  DataValidation,
  FlowPerformance,
  DependencyAnalysis,
  Dependency,
  CircularDependency,
  DependencyVulnerability,
  DependencyRecommendation,
  GraphQuery,
  QueryFilter,
  GraphAnalysisResult,
  AnalysisRecommendation,
  AnalysisInsight
};