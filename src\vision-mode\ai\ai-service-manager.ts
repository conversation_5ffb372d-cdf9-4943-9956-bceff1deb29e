/**
 * AI Service Manager
 * Coordinates between OpenRouter external AI and local AI capabilities
 * Provides intelligent routing and fallback mechanisms
 */

import { OpenRouterService, OpenRouterConfig, AIAnalysisResult, openRouterService } from './openrouter-service.js';
import { getEnvironmentConfig } from '../../config/environment.js';
import { AIErrorHandler, FallbackStrategy, AIErrorType } from './error-handler.js';

export interface AIServiceConfig {
  preferExternal: boolean;
  fallbackToLocal: boolean;
  maxRetries: number;
  timeoutMs: number;
  costThreshold: number;
}

export interface AICapability {
  name: string;
  type: 'external' | 'local' | 'hybrid';
  confidence: number;
  cost: number;
  speed: number;
}

export interface AITask {
  id: string;
  type: 'image_analysis' | 'code_analysis' | 'semantic_analysis' | 'documentation' | 'insights';
  priority: 'low' | 'medium' | 'high' | 'critical';
  data: any;
  context?: string;
  requirements?: {
    accuracy?: number;
    speed?: number;
    cost?: number;
  };
}

export interface AIResult extends AIAnalysisResult {
  source: 'external' | 'local' | 'hybrid';
  processingTime: number;
  taskId: string;
}

class AIServiceManager {
  private config: AIServiceConfig;
  private taskQueue: AITask[] = [];
  private processingTasks: Map<string, Promise<AIResult>> = new Map();
  private capabilities: Map<string, AICapability> = new Map();
  private errorHandler: AIErrorHandler;
  private stats = {
    totalTasks: 0,
    successfulTasks: 0,
    failedTasks: 0,
    externalRequests: 0,
    localRequests: 0,
    totalCost: 0,
    averageProcessingTime: 0
  };

  constructor(config: Partial<AIServiceConfig> = {}) {
    const envConfig = getEnvironmentConfig();
    
    this.config = {
      preferExternal: config.preferExternal ?? true,
      fallbackToLocal: config.fallbackToLocal ?? envConfig.ai?.fallbackToLocal ?? true,
      maxRetries: config.maxRetries ?? envConfig.ai?.maxRetries ?? 3,
      timeoutMs: config.timeoutMs ?? envConfig.ai?.timeout ?? 30000,
      costThreshold: config.costThreshold ?? 0.10 // $0.10 per request
    };

    this.errorHandler = new AIErrorHandler();
    this.initializeCapabilities();
    
    if (envConfig.ai?.enableLogging) {
      console.log('AI Service Manager initialized with config:', this.config);
    }
  }

  /**
   * Initialize AI capabilities mapping
   */
  private initializeCapabilities(): void {
    this.capabilities.set('image_analysis_external', {
      name: 'OpenRouter Image Analysis',
      type: 'external',
      confidence: 0.95,
      cost: 0.02,
      speed: 0.6
    });

    this.capabilities.set('image_analysis_local', {
      name: 'Local TensorFlow Image Analysis',
      type: 'local',
      confidence: 0.75,
      cost: 0.001,
      speed: 0.9
    });

    this.capabilities.set('code_analysis_external', {
      name: 'OpenRouter Code Analysis',
      type: 'external',
      confidence: 0.92,
      cost: 0.015,
      speed: 0.7
    });

    this.capabilities.set('semantic_analysis_external', {
      name: 'OpenRouter Semantic Analysis',
      type: 'external',
      confidence: 0.90,
      cost: 0.018,
      speed: 0.65
    });

    this.capabilities.set('documentation_external', {
      name: 'OpenRouter Documentation Generation',
      type: 'external',
      confidence: 0.88,
      cost: 0.025,
      speed: 0.5
    });
  }

  /**
   * Process AI task with intelligent routing and comprehensive error handling
   */
  async processTask(task: AITask): Promise<AIResult> {
    const startTime = Date.now();
    this.stats.totalTasks++;

    try {
      // Check if task is already being processed
      if (this.processingTasks.has(task.id)) {
        return await this.processingTasks.get(task.id)!;
      }

      // Create processing promise
      const processingPromise = this.executeTask(task, startTime);
      this.processingTasks.set(task.id, processingPromise);

      const result = await processingPromise;
      this.processingTasks.delete(task.id);

      // Update statistics
      this.updateStats(result, startTime);

      return result;
    } catch (error) {
      this.stats.failedTasks++;
      this.processingTasks.delete(task.id);
      
      // Handle final error with fallback strategies
      const aiError = this.errorHandler.handleError(error, 'AIServiceManager', 'processTask');
      const fallbackStrategy = this.errorHandler.getFallbackStrategy(aiError);
      
      if (fallbackStrategy === FallbackStrategy.SIMPLIFIED_ANALYSIS) {
        return {
          success: true,
          data: {
            analysis: `Basic ${task.type} completed with limited functionality`,
            confidence: 0.2,
            metadata: {
              simplified: true,
              fallbackStrategy,
              originalError: aiError.message,
              timestamp: new Date().toISOString()
            }
          },
          source: 'local',
          processingTime: Date.now() - startTime,
          taskId: task.id
        };
      }
      
      throw error;
    }
  }

  /**
   * Execute AI task with routing logic
   */
  private async executeTask(task: AITask, startTime: number): Promise<AIResult> {
    const route = this.determineRoute(task);
    
    try {
      let result: AIAnalysisResult;
      let source: 'external' | 'local' | 'hybrid';

      if (route === 'external') {
        result = await this.executeExternalTask(task);
        source = 'external';
        this.stats.externalRequests++;
      } else if (route === 'local') {
        result = await this.executeLocalTask(task);
        source = 'local';
        this.stats.localRequests++;
      } else {
        // Hybrid approach - try external first, fallback to local
        try {
          result = await this.executeExternalTask(task);
          source = 'external';
          this.stats.externalRequests++;
        } catch (error) {
          console.warn('External AI failed, falling back to local:', error);
          result = await this.executeLocalTask(task);
          source = 'local';
          this.stats.localRequests++;
        }
      }

      return {
        ...result,
        source,
        processingTime: Date.now() - startTime,
        taskId: task.id
      };
    } catch (error) {
      // Final fallback
      if (this.config.fallbackToLocal && route !== 'local') {
        try {
          const result = await this.executeLocalTask(task);
          return {
            ...result,
            source: 'local',
            processingTime: Date.now() - startTime,
            taskId: task.id
          };
        } catch (fallbackError) {
          throw new Error(`Both external and local AI failed: ${error.message} | ${fallbackError.message}`);
        }
      }
      throw error;
    }
  }

  /**
   * Determine optimal routing for AI task
   */
  private determineRoute(task: AITask): 'external' | 'local' | 'hybrid' {
    // High priority tasks prefer external AI for better accuracy
    if (task.priority === 'critical' || task.priority === 'high') {
      return this.config.preferExternal ? 'external' : 'hybrid';
    }

    // Cost-sensitive tasks
    if (task.requirements?.cost && task.requirements.cost < 0.005) {
      return 'local';
    }

    // Speed-sensitive tasks
    if (task.requirements?.speed && task.requirements.speed > 0.8) {
      return 'local';
    }

    // Accuracy-sensitive tasks
    if (task.requirements?.accuracy && task.requirements.accuracy > 0.9) {
      return 'external';
    }

    // Complex tasks that benefit from external AI
    const complexTasks = ['image_analysis', 'code_analysis', 'documentation'];
    if (complexTasks.includes(task.type)) {
      return this.config.preferExternal ? 'external' : 'hybrid';
    }

    return 'hybrid';
  }

  /**
   * Execute task using external AI (OpenRouter)
   */
  private async executeExternalTask(task: AITask): Promise<AIAnalysisResult> {
    switch (task.type) {
      case 'image_analysis':
        return await openRouterService.analyzeImage(task.data.imageUrl, task.data.prompt);
      
      case 'code_analysis':
        return await openRouterService.analyzeCode(task.data.code, task.data.language, task.context);
      
      case 'semantic_analysis':
      case 'insights':
        return await openRouterService.generateInsights(task.data, task.data.type || 'general');
      
      case 'documentation':
        return await openRouterService.generateDocumentation(task.data, task.data.format || 'markdown');
      
      default:
        throw new Error(`Unsupported external task type: ${task.type}`);
    }
  }

  /**
   * Execute task using local AI capabilities
   */
  private async executeLocalTask(task: AITask): Promise<AIAnalysisResult> {
    // Simulate local AI processing with basic analysis
    // In a real implementation, this would call local TensorFlow.js models
    
    await new Promise(resolve => setTimeout(resolve, 100)); // Simulate processing time
    
    const localAnalysis = {
      success: true,
      data: {
        analysis: `Local AI analysis for ${task.type}`,
        insights: [`Local insight 1 for ${task.type}`, `Local insight 2 for ${task.type}`],
        recommendations: [`Local recommendation for ${task.type}`],
        confidence: 0.75,
        metadata: {
          model: 'local-tensorflow',
          created: Date.now(),
          id: `local-${task.id}`
        }
      },
      usage: {
        tokens: 0,
        cost: 0.001
      }
    };

    return localAnalysis;
  }

  /**
   * Analyze image with intelligent routing and error handling
   */
  async analyzeImage(imageUrl: string, prompt?: string, options?: Partial<AITask>): Promise<AIResult> {
    return await this.errorHandler.executeWithRetry(
      async () => {
        const task: AITask = {
          id: `img-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: 'image_analysis',
          priority: options?.priority || 'medium',
          data: { imageUrl, prompt: prompt || 'Analyze this image in detail' },
          context: options?.context,
          requirements: options?.requirements
        };

        return await this.processTask(task);
      },
      'AIServiceManager',
      'analyzeImage'
    );
  }

  /**
   * Analyze code with intelligent routing and error handling
   */
  async analyzeCode(code: string, language: string, context?: string, options?: Partial<AITask>): Promise<AIResult> {
    return await this.errorHandler.executeWithRetry(
      async () => {
        const task: AITask = {
          id: `code-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: 'code_analysis',
          priority: options?.priority || 'medium',
          data: { code, language },
          context,
          requirements: options?.requirements
        };

        return await this.processTask(task);
      },
      'AIServiceManager',
      'analyzeCode'
    );
  }

  /**
   * Generate insights with intelligent routing and error handling
   */
  async generateInsights(data: any, type: string, options?: Partial<AITask>): Promise<AIResult> {
    return await this.errorHandler.executeWithRetry(
      async () => {
        const task: AITask = {
          id: `insights-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: 'insights',
          priority: options?.priority || 'medium',
          data: { ...data, type },
          context: options?.context,
          requirements: options?.requirements
        };

        return await this.processTask(task);
      },
      'AIServiceManager',
      'generateInsights'
    );
  }

  /**
   * Generate documentation with intelligent routing and error handling
   */
  async generateDocumentation(content: any, format: string, options?: Partial<AITask>): Promise<AIResult> {
    return await this.errorHandler.executeWithRetry(
      async () => {
        const task: AITask = {
          id: `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: 'documentation',
          priority: options?.priority || 'low',
          data: { ...content, format },
          context: options?.context,
          requirements: options?.requirements
        };

        return await this.processTask(task);
      },
      'AIServiceManager',
      'generateDocumentation'
    );
  }

  /**
   * Update statistics
   */
  private updateStats(result: AIResult, startTime: number): void {
    if (result.success) {
      this.stats.successfulTasks++;
    } else {
      this.stats.failedTasks++;
    }

    if (result.usage?.cost) {
      this.stats.totalCost += result.usage.cost;
    }

    const processingTime = Date.now() - startTime;
    this.stats.averageProcessingTime = 
      (this.stats.averageProcessingTime * (this.stats.totalTasks - 1) + processingTime) / this.stats.totalTasks;
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalTasks > 0 ? this.stats.successfulTasks / this.stats.totalTasks : 0,
      externalUsageRate: this.stats.totalTasks > 0 ? this.stats.externalRequests / this.stats.totalTasks : 0,
      queueLength: this.taskQueue.length,
      activeProcessing: this.processingTasks.size
    };
  }

  /**
   * Get available capabilities
   */
  getCapabilities(): AICapability[] {
    return Array.from(this.capabilities.values());
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Test all AI services
   */
  async testServices(): Promise<{ external: boolean; local: boolean }> {
    const results = {
      external: false,
      local: false
    };

    try {
      results.external = await openRouterService.testConnection();
    } catch (error) {
      console.error('External AI test failed:', error);
    }

    try {
      // Test local AI with a simple task
      const localResult = await this.executeLocalTask({
        id: 'test',
        type: 'insights',
        priority: 'low',
        data: { test: true }
      });
      results.local = localResult.success;
    } catch (error) {
      console.error('Local AI test failed:', error);
    }

    return results;
  }
}

// Export singleton instance
export const aiServiceManager = new AIServiceManager();
export default AIServiceManager;