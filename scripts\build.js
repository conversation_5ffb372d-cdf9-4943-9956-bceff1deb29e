#!/usr/bin/env node

/**
 * Build Script for Image Recreation App
 * Creates optimized production build
 */

const fs = require('fs').promises;
const path = require('path');

class BuildScript {
    constructor() {
        this.buildDir = 'dist';
        this.sourceDir = '.';
        this.startTime = Date.now();
    }

    async run() {
        console.log('🏗️ Starting build process...');
        
        try {
            // Clean build directory
            await this.cleanBuildDir();
            
            // Copy and optimize files
            await this.copyAndOptimizeFiles();
            
            // Generate build manifest
            await this.generateBuildManifest();
            
            const buildTime = Date.now() - this.startTime;
            console.log(`✅ Build completed in ${buildTime}ms`);
            
        } catch (error) {
            console.error('❌ Build failed:', error);
            process.exit(1);
        }
    }

    async cleanBuildDir() {
        console.log('🧹 Cleaning build directory...');
        
        try {
            await fs.rmdir(this.buildDir, { recursive: true });
        } catch (error) {
            // Directory doesn't exist, that's fine
        }
        
        await fs.mkdir(this.buildDir, { recursive: true });
    }

    async copyAndOptimizeFiles() {
        console.log('📁 Copying and optimizing files...');
        
        const filesToCopy = [
            'index.html',
            'styles.css',
            'recreationApp.js',
            'modules/',
            'package.json',
            'README.md'
        ];
        
        for (const file of filesToCopy) {
            await this.copyFile(file);
        }
    }

    async copyFile(filename) {
        const sourcePath = path.join(this.sourceDir, filename);
        const destPath = path.join(this.buildDir, filename);
        
        try {
            const stats = await fs.stat(sourcePath);
            
            if (stats.isDirectory()) {
                await this.copyDirectory(sourcePath, destPath);
            } else {
                await fs.mkdir(path.dirname(destPath), { recursive: true });
                await fs.copyFile(sourcePath, destPath);
                console.log(`📄 Copied: ${filename}`);
            }
        } catch (error) {
            console.warn(`⚠️ Could not copy ${filename}:`, error.message);
        }
    }

    async copyDirectory(sourceDir, destDir) {
        await fs.mkdir(destDir, { recursive: true });
        
        const files = await fs.readdir(sourceDir);
        
        for (const file of files) {
            const sourcePath = path.join(sourceDir, file);
            const destPath = path.join(destDir, file);
            
            const stats = await fs.stat(sourcePath);
            
            if (stats.isDirectory()) {
                await this.copyDirectory(sourcePath, destPath);
            } else {
                await fs.copyFile(sourcePath, destPath);
            }
        }
        
        console.log(`📁 Copied directory: ${path.basename(sourceDir)}`);
    }

    async generateBuildManifest() {
        console.log('📋 Generating build manifest...');
        
        const manifest = {
            buildTime: new Date().toISOString(),
            version: '1.0.0',
            buildNumber: Date.now().toString(36),
            files: await this.getFileList(),
            size: await this.calculateTotalSize()
        };
        
        const manifestPath = path.join(this.buildDir, 'build-manifest.json');
        await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));
        
        console.log('✅ Build manifest generated');
    }

    async getFileList() {
        const files = [];
        
        async function walkDir(dir) {
            const items = await fs.readdir(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stats = await fs.stat(fullPath);
                
                if (stats.isDirectory()) {
                    await walkDir(fullPath);
                } else {
                    files.push({
                        path: path.relative(this.buildDir, fullPath),
                        size: stats.size,
                        modified: stats.mtime.toISOString()
                    });
                }
            }
        }
        
        await walkDir.call(this, this.buildDir);
        return files;
    }

    async calculateTotalSize() {
        const files = await this.getFileList();
        return files.reduce((total, file) => total + file.size, 0);
    }
}

// Run build if called directly
if (require.main === module) {
    const build = new BuildScript();
    build.run();
}

module.exports = BuildScript;