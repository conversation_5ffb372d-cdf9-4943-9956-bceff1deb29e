/**
 * Communication Protocol for MCP Agent System
 * Handles message routing, event distribution, and real-time communication
 */

import { EventEmitter } from 'events';
import {
  AgentId,
  MessageId,
  AgentMessage,
  MessageType,
  AgentEvent,
  AgentEventType
} from '../agents/types.js';

/**
 * Message delivery status
 */
export enum DeliveryStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

/**
 * Message delivery receipt
 */
export interface MessageReceipt {
  messageId: MessageId;
  status: DeliveryStatus;
  timestamp: Date;
  error?: string;
  retryCount: number;
}

/**
 * Communication channel for agent groups
 */
export interface CommunicationChannel {
  id: string;
  name: string;
  description: string;
  participants: AgentId[];
  isPrivate: boolean;
  messageHistory: AgentMessage[];
  createdAt: Date;
  lastActivity: Date;
}

/**
 * Real-time subscription for agent events
 */
export interface EventSubscription {
  id: string;
  subscriberId: AgentId;
  eventTypes: AgentEventType[];
  filter?: (event: AgentEvent) => boolean;
  callback: (event: AgentEvent) => void;
  createdAt: Date;
}

/**
 * Message routing rule
 */
export interface RoutingRule {
  id: string;
  name: string;
  condition: (message: AgentMessage) => boolean;
  action: 'route' | 'broadcast' | 'filter' | 'transform';
  target?: AgentId | AgentId[];
  transform?: (message: AgentMessage) => AgentMessage;
  priority: number;
}

/**
 * Communication protocol configuration
 */
export interface CommunicationConfig {
  messageTimeout: number;
  maxRetries: number;
  retryDelay: number;
  enableMessageHistory: boolean;
  maxHistorySize: number;
  enableDeliveryReceipts: boolean;
  enableBroadcastThrottling: boolean;
  broadcastThrottleMs: number;
}

/**
 * Advanced Communication Protocol for MCP Agent System
 */
export class CommunicationProtocol extends EventEmitter {
  private _config: CommunicationConfig;
  private _messageQueue: Map<MessageId, AgentMessage> = new Map();
  private _deliveryReceipts: Map<MessageId, MessageReceipt> = new Map();
  private _channels: Map<string, CommunicationChannel> = new Map();
  private _subscriptions: Map<string, EventSubscription> = new Map();
  private _routingRules: RoutingRule[] = [];
  private _messageHistory: AgentMessage[] = [];
  private _broadcastThrottle: Map<AgentId, number> = new Map();
  private _isRunning = false;
  private _processingInterval?: NodeJS.Timeout;

  constructor(config: Partial<CommunicationConfig> = {}) {
    super();
    this._config = {
      messageTimeout: 30000, // 30 seconds
      maxRetries: 3,
      retryDelay: 1000, // 1 second
      enableMessageHistory: true,
      maxHistorySize: 1000,
      enableDeliveryReceipts: true,
      enableBroadcastThrottling: true,
      broadcastThrottleMs: 100, // 100ms between broadcasts
      ...config
    };
  }

  /**
   * Start the communication protocol
   */
  start(): void {
    if (this._isRunning) {
      return;
    }

    this._isRunning = true;
    this.startMessageProcessing();
    console.log('Communication Protocol started');
    this.emit('protocol-started');
  }

  /**
   * Stop the communication protocol
   */
  stop(): void {
    if (!this._isRunning) {
      return;
    }

    this._isRunning = false;
    this.stopMessageProcessing();
    console.log('Communication Protocol stopped');
    this.emit('protocol-stopped');
  }

  /**
   * Send a message through the protocol
   */
  async sendMessage(message: AgentMessage): Promise<MessageReceipt> {
    // Apply routing rules
    const processedMessage = this.applyRoutingRules(message);
    if (!processedMessage) {
      // Message was filtered out
      return {
        messageId: message.id,
        status: DeliveryStatus.FAILED,
        timestamp: new Date(),
        error: 'Message filtered by routing rules',
        retryCount: 0
      };
    }

    // Check broadcast throttling
    if (message.receiverId === 'broadcast' && this._config.enableBroadcastThrottling) {
      const lastBroadcast = this._broadcastThrottle.get(message.senderId) || 0;
      const now = Date.now();
      if (now - lastBroadcast < this._config.broadcastThrottleMs) {
        // Throttle the broadcast
        await new Promise(resolve => 
          setTimeout(resolve, this._config.broadcastThrottleMs - (now - lastBroadcast))
        );
      }
      this._broadcastThrottle.set(message.senderId, Date.now());
    }

    // Add to message queue
    this._messageQueue.set(message.id, processedMessage);

    // Create delivery receipt
    const receipt: MessageReceipt = {
      messageId: message.id,
      status: DeliveryStatus.PENDING,
      timestamp: new Date(),
      retryCount: 0
    };

    if (this._config.enableDeliveryReceipts) {
      this._deliveryReceipts.set(message.id, receipt);
    }

    // Add to message history
    if (this._config.enableMessageHistory) {
      this.addToHistory(processedMessage);
    }

    // Emit message sent event
    this.emit('message-queued', { message: processedMessage, receipt });

    return receipt;
  }

  /**
   * Create a communication channel
   */
  createChannel(
    id: string,
    name: string,
    description: string,
    participants: AgentId[],
    isPrivate = false
  ): CommunicationChannel {
    if (this._channels.has(id)) {
      throw new Error(`Channel ${id} already exists`);
    }

    const channel: CommunicationChannel = {
      id,
      name,
      description,
      participants: [...participants],
      isPrivate,
      messageHistory: [],
      createdAt: new Date(),
      lastActivity: new Date()
    };

    this._channels.set(id, channel);
    console.log(`Channel ${id} created with ${participants.length} participants`);
    this.emit('channel-created', { channel });

    return channel;
  }

  /**
   * Join a communication channel
   */
  joinChannel(channelId: string, agentId: AgentId): boolean {
    const channel = this._channels.get(channelId);
    if (!channel) {
      return false;
    }

    if (!channel.participants.includes(agentId)) {
      channel.participants.push(agentId);
      channel.lastActivity = new Date();
      this.emit('agent-joined-channel', { channelId, agentId });
    }

    return true;
  }

  /**
   * Leave a communication channel
   */
  leaveChannel(channelId: string, agentId: AgentId): boolean {
    const channel = this._channels.get(channelId);
    if (!channel) {
      return false;
    }

    const index = channel.participants.indexOf(agentId);
    if (index !== -1) {
      channel.participants.splice(index, 1);
      channel.lastActivity = new Date();
      this.emit('agent-left-channel', { channelId, agentId });
    }

    return true;
  }

  /**
   * Send message to a channel
   */
  async sendChannelMessage(
    channelId: string,
    senderId: AgentId,
    payload: Record<string, any>,
    messageType: MessageType = MessageType.TASK_REQUEST
  ): Promise<MessageReceipt[]> {
    const channel = this._channels.get(channelId);
    if (!channel) {
      throw new Error(`Channel ${channelId} not found`);
    }

    if (!channel.participants.includes(senderId)) {
      throw new Error(`Agent ${senderId} is not a participant in channel ${channelId}`);
    }

    const receipts: MessageReceipt[] = [];
    const timestamp = new Date();

    // Send to all participants except sender
    for (const participantId of channel.participants) {
      if (participantId !== senderId) {
        const message: AgentMessage = {
          id: this.generateMessageId(),
          type: messageType,
          senderId,
          receiverId: participantId,
          payload: { ...payload, channelId },
          timestamp
        };

        const receipt = await this.sendMessage(message);
        receipts.push(receipt);

        // Add to channel history
        channel.messageHistory.push(message);
      }
    }

    channel.lastActivity = new Date();
    return receipts;
  }

  /**
   * Subscribe to agent events
   */
  subscribeToEvents(
    subscriberId: AgentId,
    eventTypes: AgentEventType[],
    callback: (event: AgentEvent) => void,
    filter?: (event: AgentEvent) => boolean
  ): string {
    const subscription: EventSubscription = {
      id: this.generateSubscriptionId(),
      subscriberId,
      eventTypes,
      callback,
      filter,
      createdAt: new Date()
    };

    this._subscriptions.set(subscription.id, subscription);
    console.log(`Agent ${subscriberId} subscribed to events: ${eventTypes.join(', ')}`);
    this.emit('event-subscription-created', { subscription });

    return subscription.id;
  }

  /**
   * Unsubscribe from events
   */
  unsubscribeFromEvents(subscriptionId: string): boolean {
    const subscription = this._subscriptions.get(subscriptionId);
    if (!subscription) {
      return false;
    }

    this._subscriptions.delete(subscriptionId);
    console.log(`Subscription ${subscriptionId} removed`);
    this.emit('event-subscription-removed', { subscriptionId });

    return true;
  }

  /**
   * Publish an agent event
   */
  publishEvent(event: AgentEvent): void {
    // Find matching subscriptions
    for (const subscription of this._subscriptions.values()) {
      if (subscription.eventTypes.includes(event.type)) {
        // Apply filter if present
        if (!subscription.filter || subscription.filter(event)) {
          try {
            subscription.callback(event);
          } catch (error) {
            console.error(`Error in event callback for subscription ${subscription.id}:`, error);
          }
        }
      }
    }

    this.emit('event-published', { event });
  }

  /**
   * Add a routing rule
   */
  addRoutingRule(rule: RoutingRule): void {
    this._routingRules.push(rule);
    this._routingRules.sort((a, b) => b.priority - a.priority); // Higher priority first
    console.log(`Routing rule ${rule.id} added with priority ${rule.priority}`);
    this.emit('routing-rule-added', { rule });
  }

  /**
   * Remove a routing rule
   */
  removeRoutingRule(ruleId: string): boolean {
    const index = this._routingRules.findIndex(rule => rule.id === ruleId);
    if (index === -1) {
      return false;
    }

    this._routingRules.splice(index, 1);
    console.log(`Routing rule ${ruleId} removed`);
    this.emit('routing-rule-removed', { ruleId });

    return true;
  }

  /**
   * Get message delivery status
   */
  getDeliveryStatus(messageId: MessageId): MessageReceipt | undefined {
    return this._deliveryReceipts.get(messageId);
  }

  /**
   * Get channel information
   */
  getChannel(channelId: string): CommunicationChannel | undefined {
    return this._channels.get(channelId);
  }

  /**
   * Get all channels for an agent
   */
  getAgentChannels(agentId: AgentId): CommunicationChannel[] {
    return Array.from(this._channels.values())
      .filter(channel => channel.participants.includes(agentId));
  }

  /**
   * Get message history
   */
  getMessageHistory(limit?: number): AgentMessage[] {
    if (limit) {
      return this._messageHistory.slice(-limit);
    }
    return [...this._messageHistory];
  }

  /**
   * Get channel message history
   */
  getChannelHistory(channelId: string, limit?: number): AgentMessage[] {
    const channel = this._channels.get(channelId);
    if (!channel) {
      return [];
    }

    if (limit) {
      return channel.messageHistory.slice(-limit);
    }
    return [...channel.messageHistory];
  }

  // Private methods

  private applyRoutingRules(message: AgentMessage): AgentMessage | null {
    let processedMessage = { ...message };

    for (const rule of this._routingRules) {
      if (rule.condition(processedMessage)) {
        switch (rule.action) {
          case 'filter':
            return null; // Message is filtered out

          case 'transform':
            if (rule.transform) {
              processedMessage = rule.transform(processedMessage);
            }
            break;

          case 'route':
            if (rule.target) {
              if (Array.isArray(rule.target)) {
                // Multiple targets - create multiple messages
                // For now, just use the first target
                processedMessage.receiverId = rule.target[0];
              } else {
                processedMessage.receiverId = rule.target;
              }
            }
            break;

          case 'broadcast':
            processedMessage.receiverId = 'broadcast';
            break;
        }
      }
    }

    return processedMessage;
  }

  private addToHistory(message: AgentMessage): void {
    this._messageHistory.push(message);
    
    // Trim history if it exceeds max size
    if (this._messageHistory.length > this._config.maxHistorySize) {
      this._messageHistory = this._messageHistory.slice(-this._config.maxHistorySize);
    }
  }

  private startMessageProcessing(): void {
    this._processingInterval = setInterval(() => {
      this.processMessageQueue();
      this.cleanupExpiredReceipts();
    }, 100); // Process every 100ms
  }

  private stopMessageProcessing(): void {
    if (this._processingInterval) {
      clearInterval(this._processingInterval);
      this._processingInterval = undefined;
    }
  }

  private processMessageQueue(): void {
    for (const [messageId, message] of this._messageQueue) {
      this.deliverMessage(message);
      this._messageQueue.delete(messageId);
    }
  }

  private deliverMessage(message: AgentMessage): void {
    try {
      // Emit message for delivery
      this.emit('deliver-message', message);
      
      // Update delivery receipt
      const receipt = this._deliveryReceipts.get(message.id);
      if (receipt) {
        receipt.status = DeliveryStatus.DELIVERED;
        receipt.timestamp = new Date();
      }
    } catch (error) {
      console.error(`Failed to deliver message ${message.id}:`, error);
      
      // Update delivery receipt
      const receipt = this._deliveryReceipts.get(message.id);
      if (receipt) {
        receipt.status = DeliveryStatus.FAILED;
        receipt.error = error instanceof Error ? error.message : String(error);
        receipt.timestamp = new Date();
      }
    }
  }

  private cleanupExpiredReceipts(): void {
    const now = Date.now();
    const timeout = this._config.messageTimeout;
    
    for (const [messageId, receipt] of this._deliveryReceipts) {
      if (receipt.status === DeliveryStatus.PENDING && 
          now - receipt.timestamp.getTime() > timeout) {
        receipt.status = DeliveryStatus.TIMEOUT;
        receipt.timestamp = new Date();
        console.warn(`Message ${messageId} delivery timeout`);
      }
    }
  }

  private generateMessageId(): MessageId {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSubscriptionId(): string {
    return `sub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}