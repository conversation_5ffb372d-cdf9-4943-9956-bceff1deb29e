/**
 * OpenRouter AI Service Integration
 * Provides secure API access to horizon-beta model for enhanced Vision Mode capabilities
 */

import { getEnvironmentConfig, validate<PERSON><PERSON><PERSON><PERSON>, maskSensitiveData } from '../../config/environment.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rror, FallbackStrategy } from './error-handler.js';

export interface OpenRouterConfig {
  apiKey: string;
  baseUrl: string;
  model: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
  maxRetries: number;
  rateLimitRequests: number;
  rateLimitWindow: number;
}

export interface AIRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string | Array<{
      type: 'text' | 'image_url';
      text?: string;
      image_url?: {
        url: string;
        detail?: 'low' | 'high' | 'auto';
      };
    }>;
  }>;
  max_tokens?: number;
  temperature?: number;
  stream?: boolean;
}

export interface AIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface AIAnalysisResult {
  success: boolean;
  data?: {
    analysis: string;
    insights: string[];
    recommendations: string[];
    confidence: number;
    metadata: Record<string, any>;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  usage?: {
    tokens: number;
    cost: number;
  };
}

class OpenRouterService {
  private config: OpenRouterConfig;
  private errorHandler: AIErrorHandler;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;
  private rateLimitDelay: number = 1000; // 1 second between requests

  constructor(config: Partial<OpenRouterConfig> = {}) {
    const envConfig = getEnvironmentConfig();
    
    this.config = {
      apiKey: config.apiKey || envConfig.openrouter.apiKey,
      baseUrl: config.baseUrl || envConfig.openrouter.baseUrl,
      model: config.model || envConfig.openrouter.model,
      maxTokens: config.maxTokens || envConfig.openrouter.maxTokens,
      temperature: config.temperature || envConfig.openrouter.temperature,
      timeout: config.timeout || envConfig.openrouter.timeout,
      maxRetries: config.maxRetries || envConfig.openrouter.maxRetries,
      rateLimitRequests: config.rateLimitRequests || envConfig.openrouter.rateLimitRequests,
      rateLimitWindow: config.rateLimitWindow || envConfig.openrouter.rateLimitWindow
    };
    
    this.errorHandler = new AIErrorHandler();
    
    // Validate API key
    if (!validateApiKey(this.config.apiKey)) {
      throw new Error('Invalid or missing OpenRouter API key. Please check your environment configuration.');
    }

    // Log configuration (with masked sensitive data)
    if (envConfig.ai.enableLogging) {
      console.log('OpenRouter service initialized with config:', maskSensitiveData(this.config));
    }
  }

  /**
   * Analyze image with AI-powered insights
   */
  async analyzeImage(imageUrl: string, prompt: string = 'Analyze this image in detail'): Promise<AIAnalysisResult> {
    return await this.errorHandler.executeWithRetry(
      async () => {
        await this.enforceRateLimit();

        const request: AIRequest = {
          messages: [
            {
              role: 'system',
              content: 'You are an expert AI assistant specialized in image analysis, UI/UX evaluation, and technical documentation. Provide detailed, actionable insights.'
            },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageUrl,
                    detail: 'high'
                  }
                }
              ]
            }
          ],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature
        };

        const response = await this.makeRequest('/chat/completions', request);
        return this.processResponse(response);
      },
      'OpenRouter',
      'analyzeImage'
    ).catch(error => this.handleError(error));
  }

  /**
   * Analyze code with semantic understanding
   */
  async analyzeCode(code: string, language: string, context?: string): Promise<AIAnalysisResult> {
    return await this.errorHandler.executeWithRetry(
      async () => {
        await this.enforceRateLimit();

        const prompt = `Analyze this ${language} code and provide:
1. Code quality assessment
2. Performance optimization suggestions
3. Security considerations
4. Best practices recommendations
5. Potential bugs or issues

${context ? `Context: ${context}\n\n` : ''}Code:
\`\`\`${language}
${code}
\`\`\``;

        const request: AIRequest = {
          messages: [
            {
              role: 'system',
              content: 'You are a senior software engineer and code reviewer. Provide comprehensive, actionable code analysis with specific recommendations.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: this.config.maxTokens,
          temperature: 0.3 // Lower temperature for code analysis
        };

        const response = await this.makeRequest('/chat/completions', request);
        return this.processResponse(response);
      },
      'OpenRouter',
      'analyzeCode'
    ).catch(error => this.handleError(error));
  }

  /**
   * Generate automated insights and recommendations
   */
  async generateInsights(data: any, type: 'ui' | 'performance' | 'accessibility' | 'general'): Promise<AIAnalysisResult> {
    return await this.errorHandler.executeWithRetry(
      async () => {
        await this.enforceRateLimit();

        const prompts = {
          ui: 'Analyze this UI data and provide UX/UI improvement recommendations, design patterns, and user experience insights.',
          performance: 'Analyze this performance data and provide optimization recommendations, bottleneck identification, and performance improvement strategies.',
          accessibility: 'Analyze this accessibility data and provide WCAG compliance recommendations, accessibility improvements, and inclusive design suggestions.',
          general: 'Analyze this data and provide comprehensive insights, patterns, and actionable recommendations.'
        };

        const request: AIRequest = {
          messages: [
            {
              role: 'system',
              content: `You are an expert analyst specializing in ${type} analysis. Provide detailed, actionable insights with specific recommendations.`
            },
            {
              role: 'user',
              content: `${prompts[type]}\n\nData: ${JSON.stringify(data, null, 2)}`
            }
          ],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature
        };

        const response = await this.makeRequest('/chat/completions', request);
        return this.processResponse(response);
      },
      'OpenRouter',
      'generateInsights'
    ).catch(error => this.handleError(error));
  }

  /**
   * Generate automated documentation
   */
  async generateDocumentation(content: any, format: 'markdown' | 'html' | 'json'): Promise<AIAnalysisResult> {
    return await this.errorHandler.executeWithRetry(
      async () => {
        await this.enforceRateLimit();

        const prompt = `Generate comprehensive technical documentation in ${format} format for the following content. Include:
1. Overview and purpose
2. Key features and capabilities
3. Technical specifications
4. Usage examples
5. Best practices
6. Troubleshooting guide

Content: ${JSON.stringify(content, null, 2)}`;

        const request: AIRequest = {
          messages: [
            {
              role: 'system',
              content: 'You are a technical writer specializing in creating clear, comprehensive documentation. Focus on clarity, completeness, and practical value.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: this.config.maxTokens,
          temperature: 0.4
        };

        const response = await this.makeRequest('/chat/completions', request);
        return this.processResponse(response);
      },
      'OpenRouter',
      'generateDocumentation'
    ).catch(error => this.handleError(error));
  }

  /**
   * Make HTTP request to OpenRouter API
   */
  private async makeRequest(endpoint: string, data: any): Promise<AIResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
    
    try {
      const response = await fetch(`${this.config.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://vision-mode.ai',
          'X-Title': 'Vision Mode AI Analysis'
        },
        body: JSON.stringify({
          ...data,
          model: this.config.model
        }),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
        (error as any).response = {
          status: response.status,
          statusText: response.statusText,
          data: errorData,
          headers: Object.fromEntries(response.headers.entries())
        };
        throw error;
      }
      
      return await response.json();
      
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        const timeoutError = new Error('Request timeout');
        (timeoutError as any).code = 'ETIMEDOUT';
        throw timeoutError;
      }
      
      throw error;
    }
  }

  /**
   * Process AI response and extract insights
   */
  private processResponse(response: AIResponse): AIAnalysisResult {
    try {
      const content = response.choices[0]?.message?.content || '';
      
      // Extract structured insights from the response
      const insights = this.extractInsights(content);
      const recommendations = this.extractRecommendations(content);
      const confidence = this.calculateConfidence(response);

      return {
        success: true,
        data: {
          analysis: content,
          insights,
          recommendations,
          confidence,
          metadata: {
            model: response.model,
            created: response.created,
            id: response.id
          }
        },
        usage: {
          tokens: response.usage?.total_tokens || 0,
          cost: this.calculateCost(response.usage?.total_tokens || 0)
        }
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Extract insights from AI response
   */
  private extractInsights(content: string): string[] {
    const insights: string[] = [];
    
    // Look for numbered lists, bullet points, or key insights
    const patterns = [
      /(?:^|\n)\d+\.\s*(.+?)(?=\n\d+\.|\n[^\d]|$)/g,
      /(?:^|\n)[-*]\s*(.+?)(?=\n[-*]|\n[^-*]|$)/g,
      /(?:insight|key point|important|notable):\s*(.+?)(?=\n|$)/gi
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const insight = match[1].trim();
        if (insight.length > 10 && !insights.includes(insight)) {
          insights.push(insight);
        }
      }
    });

    return insights.slice(0, 10); // Limit to top 10 insights
  }

  /**
   * Extract recommendations from AI response
   */
  private extractRecommendations(content: string): string[] {
    const recommendations: string[] = [];
    
    // Look for recommendation patterns
    const patterns = [
      /(?:recommend|suggest|should|consider|improve)\s+(.+?)(?=\n|\.|$)/gi,
      /(?:^|\n)(?:recommendation|suggestion):\s*(.+?)(?=\n|$)/gi
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const recommendation = match[1].trim();
        if (recommendation.length > 10 && !recommendations.includes(recommendation)) {
          recommendations.push(recommendation);
        }
      }
    });

    return recommendations.slice(0, 8); // Limit to top 8 recommendations
  }

  /**
   * Calculate confidence score based on response quality
   */
  private calculateConfidence(response: AIResponse): number {
    const content = response.choices[0]?.message?.content || '';
    const contentLength = content.length;
    const hasStructure = /\d+\.|[-*]\s/.test(content);
    const hasSpecifics = /\b(?:specifically|exactly|precisely|detailed?)\b/i.test(content);
    
    let confidence = 0.5; // Base confidence
    
    if (contentLength > 200) confidence += 0.2;
    if (hasStructure) confidence += 0.2;
    if (hasSpecifics) confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }

  /**
   * Calculate estimated cost based on token usage
   */
  private calculateCost(tokens: number): number {
    // Estimated cost per 1K tokens for horizon-beta model
    const costPer1K = 0.002; // $0.002 per 1K tokens (estimate)
    return (tokens / 1000) * costPer1K;
  }

  /**
   * Enforce rate limiting
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.rateLimitDelay) {
      const delay = this.rateLimitDelay - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  /**
   * Handle errors and return structured error response
   */
  private handleError(error: any): AIAnalysisResult {
    const aiError = this.errorHandler.handleError(error, 'OpenRouter', 'API_CALL');
    const fallbackStrategy = this.errorHandler.getFallbackStrategy(aiError);
    
    console.error('OpenRouter AI Service Error:', {
      type: aiError.type,
      message: aiError.message,
      fallbackStrategy
    });
    
    // Apply fallback strategy
    switch (fallbackStrategy) {
      case FallbackStrategy.GRACEFUL_DEGRADATION:
        return {
          success: false,
          error: {
            code: aiError.code || 'GRACEFUL_DEGRADATION',
            message: 'Analysis temporarily unavailable. Please try again later.',
            details: aiError
          }
        };
      
      case FallbackStrategy.SIMPLIFIED_ANALYSIS:
        return {
          success: true,
          data: {
            analysis: 'Basic analysis completed. Enhanced features temporarily unavailable.',
            insights: ['Service operating in simplified mode'],
            recommendations: ['Try again later for enhanced analysis'],
            confidence: 0.3,
            metadata: {
              simplified: true,
              fallbackStrategy,
              timestamp: new Date().toISOString(),
              model: 'fallback'
            }
          }
        };
      
      default:
        return {
          success: false,
          error: {
            code: error.code || 'UNKNOWN_ERROR',
            message: error.message || 'An unexpected error occurred',
            details: error
          }
        };
    }
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      requestCount: this.requestCount,
      lastRequestTime: this.lastRequestTime,
      model: this.config.model,
      apiKeyConfigured: !!this.config.apiKey
    };
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const result = await this.generateInsights({ test: 'connection' }, 'general');
      return result.success;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const openRouterService = new OpenRouterService();
export default OpenRouterService;