/**
 * Agent Control Panel Component
 * Interactive control panels for managing MCP agents and workflows
 */

import React, { useState, useEffect } from 'react';
import {
  Play,
  Pause,
  Square,
  RefreshCw,
  Settings,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Cpu,
  Network,
  Trash2,
  Edit,
  Plus,
  Filter,
  Search,
  MoreVertical,
  Power,
  PowerOff,
  Zap,
  Users,
  FileText,
  Database,
  Brain
} from 'lucide-react';
import { dataStore } from '../../../services/DataStoreService';

// Agent types and status
interface VisionModeAgent {
  initialize(): Promise<void>;
  getStatus(): any;
}

enum AgentStatus {
  INITIALIZING = 'initializing',
  READY = 'ready',
  BUSY = 'busy',
  WAITING = 'waiting',
  ERROR = 'error',
  OFFLINE = 'offline'
}

/**
 * Agent control panel props
 */
export interface AgentControlPanelProps {
  visionAgent: VisionModeAgent | null;
  systemStatus: any;
  onStatusUpdate?: (status: any) => void;
  className?: string;
}

/**
 * Agent configuration
 */
export interface AgentConfig {
  id: string;
  name: string;
  type: 'semantic' | 'behavioral' | 'knowledge' | 'pattern' | 'automation';
  status: AgentStatus;
  enabled: boolean;
  autoRestart: boolean;
  maxRetries: number;
  timeout: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  resources: {
    maxCpu: number;
    maxMemory: number;
    maxConnections: number;
  };
  configuration: Record<string, any>;
  createdAt: Date;
  lastModified: Date;
}

/**
 * Workflow definition
 */
export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'error';
  trigger: 'manual' | 'scheduled' | 'event' | 'webhook';
  schedule?: string;
  steps: WorkflowStep[];
  agents: string[];
  createdAt: Date;
  lastRun?: Date;
  nextRun?: Date;
  runCount: number;
  successRate: number;
}

/**
 * Workflow step
 */
export interface WorkflowStep {
  id: string;
  name: string;
  type: 'agent' | 'condition' | 'transform' | 'output';
  agentId?: string;
  configuration: Record<string, any>;
  dependencies: string[];
  timeout: number;
  retries: number;
}

/**
 * Agent Control Panel Component
 */
export const AgentControlPanel: React.FC<AgentControlPanelProps> = ({
  visionAgent,
  systemStatus,
  onStatusUpdate,
  className = ''
}) => {
  const [agents, setAgents] = useState<AgentConfig[]>([]);
  const [workflows, setWorkflows] = useState<WorkflowDefinition[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'agents' | 'workflows'>('agents');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showConfigModal, setShowConfigModal] = useState(false);

  // Initialize agents based on available data
  useEffect(() => {
    const documents = dataStore.getAllDocuments();
    const hasData = documents.length > 0;
    
    const agents: AgentConfig[] = [
      {
        id: 'semantic-analyzer',
        name: 'Semantic Analyzer',
        type: 'semantic',
        status: hasData ? AgentStatus.READY : AgentStatus.WAITING,
        enabled: true,
        autoRestart: true,
        maxRetries: 3,
        timeout: 30000,
        priority: 'high',
        resources: {
          maxCpu: 50,
          maxMemory: 512,
          maxConnections: 10
        },
        configuration: {
          modelType: 'transformer',
          batchSize: 32,
          threshold: 0.8,
          documentsProcessed: documents.length
        },
        createdAt: new Date(Date.now() - 86400000),
        lastModified: new Date()
      },
      {
        id: 'knowledge-graph',
        name: 'Knowledge Graph Builder',
        type: 'knowledge',
        status: hasData ? AgentStatus.READY : AgentStatus.WAITING,
        enabled: true,
        autoRestart: true,
        maxRetries: 2,
        timeout: 45000,
        priority: 'medium',
        resources: {
          maxCpu: 30,
          maxMemory: 256,
          maxConnections: 5
        },
        configuration: {
          graphType: 'neo4j',
          maxNodes: 10000,
          relationshipTypes: ['RELATED_TO', 'CONTAINS', 'REFERENCES'],
          entitiesCount: documents.reduce((sum, doc) => sum + doc.semanticResult.entities.length, 0)
        },
        createdAt: new Date(Date.now() - 172800000),
        lastModified: new Date()
      },
      {
        id: 'pattern-recognition',
        name: 'Pattern Recognition',
        type: 'pattern',
        status: hasData ? AgentStatus.READY : AgentStatus.WAITING,
        enabled: true,
        autoRestart: false,
        maxRetries: 1,
        timeout: 20000,
        priority: 'low',
        resources: {
          maxCpu: 20,
          maxMemory: 128,
          maxConnections: 3
        },
        configuration: {
          algorithm: 'clustering',
          minSupport: 0.1,
          maxPatterns: 100,
          patternsFound: documents.reduce((sum, doc) => sum + doc.semanticResult.topics.length, 0)
        },
        createdAt: new Date(Date.now() - 259200000),
        lastModified: new Date()
      },
      {
        id: 'behavioral-analyzer',
        name: 'Behavioral Analyzer',
        type: 'behavioral',
        status: hasData ? AgentStatus.READY : AgentStatus.WAITING,
        enabled: true,
        autoRestart: true,
        maxRetries: 3,
        timeout: 60000,
        priority: 'critical',
        resources: {
          maxCpu: 40,
          maxMemory: 384,
          maxConnections: 8
        },
        configuration: {
          trackingMode: 'comprehensive',
          privacyLevel: 'medium',
          sessionTimeout: 1800,
          behaviorPatternsDetected: documents.length * 3 // Simulated patterns
        },
        createdAt: new Date(Date.now() - 345600000),
        lastModified: new Date()
      }
    ];

    setAgents(agents);
    
    // Subscribe to data changes
    const unsubscribe = dataStore.subscribe((updatedDocuments) => {
      const hasNewData = updatedDocuments.length > 0;
      setAgents(prev => prev.map(agent => ({
        ...agent,
        status: hasNewData ? AgentStatus.READY : AgentStatus.WAITING,
        lastModified: new Date(),
        configuration: {
          ...agent.configuration,
          documentsProcessed: updatedDocuments.length,
          entitiesCount: agent.id === 'knowledge-graph' ? 
            updatedDocuments.reduce((sum, doc) => sum + doc.semanticResult.entities.length, 0) : 
            agent.configuration.entitiesCount,
          patternsFound: agent.id === 'pattern-recognition' ? 
            updatedDocuments.reduce((sum, doc) => sum + doc.semanticResult.topics.length, 0) : 
            agent.configuration.patternsFound,
          behaviorPatternsDetected: agent.id === 'behavioral-analyzer' ? 
            updatedDocuments.length * 3 : 
            agent.configuration.behaviorPatternsDetected
        }
      })));
    });
    
    return unsubscribe;
  }, []);

  // Initialize workflows based on available data
  useEffect(() => {
    const documents = dataStore.getAllDocuments();
    const hasData = documents.length > 0;
    const totalDocuments = documents.length;
    
    const workflows: WorkflowDefinition[] = [
      {
        id: 'document-analysis',
        name: 'Document Analysis Pipeline',
        description: `Complete document analysis including semantic extraction and knowledge graph building. Processed ${totalDocuments} documents.`,
        status: hasData ? 'active' : 'draft',
        trigger: 'event',
        steps: [
          {
            id: 'step-1',
            name: 'Document Upload',
            type: 'agent',
            agentId: 'semantic-analyzer',
            configuration: { extractText: true, detectLanguage: true },
            dependencies: [],
            timeout: 30000,
            retries: 2
          },
          {
            id: 'step-2',
            name: 'Semantic Analysis',
            type: 'agent',
            agentId: 'semantic-analyzer',
            configuration: { deepAnalysis: true, extractEntities: true },
            dependencies: ['step-1'],
            timeout: 60000,
            retries: 3
          },
          {
            id: 'step-3',
            name: 'Knowledge Graph Update',
            type: 'agent',
            agentId: 'knowledge-graph',
            configuration: { updateExisting: true, createRelations: true },
            dependencies: ['step-2'],
            timeout: 45000,
            retries: 2
          }
        ],
        agents: ['semantic-analyzer', 'knowledge-graph'],
        createdAt: new Date(Date.now() - 86400000),
        lastRun: hasData ? new Date(Date.now() - 3600000) : undefined,
        nextRun: hasData ? new Date(Date.now() + 3600000) : undefined,
        runCount: totalDocuments,
        successRate: hasData ? 94.2 : 0
      },
      {
        id: 'behavioral-tracking',
        name: 'User Behavior Tracking',
        description: `Track and analyze user interactions for pattern recognition. Found ${documents.reduce((sum, doc) => sum + doc.semanticResult.topics.length, 0)} patterns.`,
        status: hasData ? 'active' : 'draft',
        trigger: 'scheduled',
        schedule: '0 */6 * * *', // Every 6 hours
        steps: [
          {
            id: 'step-1',
            name: 'Collect Interactions',
            type: 'agent',
            agentId: 'behavioral-analyzer',
            configuration: { collectAll: true, anonymize: true },
            dependencies: [],
            timeout: 120000,
            retries: 1
          },
          {
            id: 'step-2',
            name: 'Pattern Analysis',
            type: 'agent',
            agentId: 'pattern-recognition',
            configuration: { findNewPatterns: true, updateExisting: true },
            dependencies: ['step-1'],
            timeout: 180000,
            retries: 2
          }
        ],
        agents: ['behavioral-analyzer', 'pattern-recognition'],
        createdAt: new Date(Date.now() - 172800000),
        lastRun: hasData ? new Date(Date.now() - 21600000) : undefined,
        nextRun: hasData ? new Date(Date.now() + 3600000) : undefined,
        runCount: Math.floor(totalDocuments * 0.6),
        successRate: hasData ? 89.6 : 0
      }
    ];

    setWorkflows(workflows);
  }, []);

  const handleAgentAction = (agentId: string, action: 'start' | 'stop' | 'restart' | 'configure') => {
    console.log(`Agent ${agentId}: ${action}`);
    
    setAgents(prev => prev.map(agent => {
      if (agent.id === agentId) {
        switch (action) {
          case 'start':
            return { ...agent, status: AgentStatus.BUSY };
          case 'stop':
            return { ...agent, status: AgentStatus.OFFLINE };
          case 'restart':
            return { ...agent, status: AgentStatus.BUSY };
          case 'configure':
            setSelectedAgent(agentId);
            setShowConfigModal(true);
            return agent;
          default:
            return agent;
        }
      }
      return agent;
    }));
  };

  const handleWorkflowAction = (workflowId: string, action: 'start' | 'pause' | 'stop' | 'configure') => {
    console.log(`Workflow ${workflowId}: ${action}`);
    
    setWorkflows(prev => prev.map(workflow => {
      if (workflow.id === workflowId) {
        switch (action) {
          case 'start':
            return { ...workflow, status: 'active' };
          case 'pause':
            return { ...workflow, status: 'paused' };
          case 'stop':
            return { ...workflow, status: 'draft' };
          case 'configure':
            setSelectedWorkflow(workflowId);
            setShowConfigModal(true);
            return workflow;
          default:
            return workflow;
        }
      }
      return workflow;
    }));
  };

  const getStatusColor = (status: AgentStatus | string) => {
    switch (status) {
      case AgentStatus.BUSY:
      case 'active':
        return 'text-green-500';
      case AgentStatus.ERROR:
        return 'text-red-500';
      case AgentStatus.READY:
      case 'paused':
        return 'text-yellow-500';
      case AgentStatus.OFFLINE:
      case 'draft':
        return 'text-gray-500';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: AgentStatus | string) => {
    switch (status) {
      case AgentStatus.BUSY:
      case 'active':
        return <RefreshCw className="w-4 h-4 animate-spin" />;
      case AgentStatus.ERROR:
        return <AlertTriangle className="w-4 h-4" />;
      case AgentStatus.READY:
      case 'paused':
        return <Clock className="w-4 h-4" />;
      case AgentStatus.OFFLINE:
      case 'draft':
        return <Square className="w-4 h-4" />;
      default:
        return <Square className="w-4 h-4" />;
    }
  };

  const getAgentTypeIcon = (type: string) => {
    switch (type) {
      case 'semantic': return <Brain className="w-5 h-5" />;
      case 'behavioral': return <Users className="w-5 h-5" />;
      case 'knowledge': return <Database className="w-5 h-5" />;
      case 'pattern': return <Activity className="w-5 h-5" />;
      case 'automation': return <Zap className="w-5 h-5" />;
      default: return <Settings className="w-5 h-5" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-500 bg-red-500/20';
      case 'high': return 'text-orange-500 bg-orange-500/20';
      case 'medium': return 'text-yellow-500 bg-yellow-500/20';
      case 'low': return 'text-green-500 bg-green-500/20';
      default: return 'text-gray-500 bg-gray-500/20';
    }
  };

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.type.toLowerCase().includes(searchTerm.toLowerCase());
    
    let matchesStatus = statusFilter === 'all';
    if (!matchesStatus) {
      switch (statusFilter) {
        case 'busy':
          matchesStatus = agent.status === AgentStatus.BUSY;
          break;
        case 'ready':
          matchesStatus = agent.status === AgentStatus.READY;
          break;
        case 'offline':
          matchesStatus = agent.status === AgentStatus.OFFLINE;
          break;
        case 'error':
          matchesStatus = agent.status === AgentStatus.ERROR;
          break;
        default:
          matchesStatus = false;
      }
    }
    
    return matchesSearch && matchesStatus;
  });

  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || workflow.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Agent Control Panel</h2>
          <p className="text-gray-400 mt-1">Manage MCP agents and automation workflows</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Create New</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-slate-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('agents')}
          className={`flex-1 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'agents'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          Agents ({agents.length})
        </button>
        <button
          onClick={() => setActiveTab('workflows')}
          className={`flex-1 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'workflows'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          Workflows ({workflows.length})
        </button>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder={`Search ${activeTab}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
          />
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
          >
            <option value="all">All Status</option>
            <option value="busy">Busy</option>
            <option value="active">Active</option>
            <option value="ready">Ready</option>
            <option value="paused">Paused</option>
            <option value="offline">Offline</option>
            <option value="error">Error</option>
          </select>
        </div>
      </div>

      {/* Content */}
      {activeTab === 'agents' ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredAgents.map((agent) => (
            <div key={agent.id} className="bg-slate-800 rounded-lg border border-slate-700 p-6">
              {/* Agent Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-blue-500">
                    {getAgentTypeIcon(agent.type)}
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">{agent.name}</h3>
                    <span className="text-sm text-gray-400 capitalize">{agent.type}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`flex items-center space-x-1 ${getStatusColor(agent.status)}`}>
                    {getStatusIcon(agent.status)}
                    <span className="text-sm capitalize">{agent.status}</span>
                  </div>
                </div>
              </div>

              {/* Agent Info */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Priority:</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(agent.priority)}`}>
                    {agent.priority}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Auto Restart:</span>
                  <span className={agent.autoRestart ? 'text-green-400' : 'text-gray-400'}>
                    {agent.autoRestart ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Timeout:</span>
                  <span className="text-white">{agent.timeout / 1000}s</span>
                </div>
              </div>

              {/* Resource Usage */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-xs text-gray-400">
                  <span>CPU Limit: {agent.resources.maxCpu}%</span>
                  <span>Memory: {agent.resources.maxMemory}MB</span>
                </div>
                <div className="flex space-x-2">
                  <div className="flex-1 bg-slate-700 rounded-full h-1">
                    <div
                      className="bg-blue-500 h-1 rounded-full"
                      style={{ width: `${(agent.resources.maxCpu / 100) * 100}%` }}
                    />
                  </div>
                  <div className="flex-1 bg-slate-700 rounded-full h-1">
                    <div
                      className="bg-green-500 h-1 rounded-full"
                      style={{ width: `${(agent.resources.maxMemory / 1024) * 100}%` }}
                    />
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2">
                {agent.status === AgentStatus.BUSY ? (
                  <button
                    onClick={() => handleAgentAction(agent.id, 'stop')}
                    className="flex items-center space-x-1 px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm transition-colors"
                  >
                    <Square className="w-3 h-3" />
                    <span>Stop</span>
                  </button>
                ) : (
                  <button
                    onClick={() => handleAgentAction(agent.id, 'start')}
                    className="flex items-center space-x-1 px-3 py-1 bg-green-600 hover:bg-green-700 rounded text-sm transition-colors"
                  >
                    <Play className="w-3 h-3" />
                    <span>Start</span>
                  </button>
                )}
                <button
                  onClick={() => handleAgentAction(agent.id, 'restart')}
                  className="flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm transition-colors"
                >
                  <RefreshCw className="w-3 h-3" />
                  <span>Restart</span>
                </button>
                <button
                  onClick={() => handleAgentAction(agent.id, 'configure')}
                  className="p-1 text-gray-400 hover:text-white hover:bg-slate-700 rounded transition-colors"
                >
                  <Settings className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredWorkflows.map((workflow) => (
            <div key={workflow.id} className="bg-slate-800 rounded-lg border border-slate-700 p-6">
              {/* Workflow Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">{workflow.name}</h3>
                    <div className={`flex items-center space-x-1 ${getStatusColor(workflow.status)}`}>
                      {getStatusIcon(workflow.status)}
                      <span className="text-sm capitalize">{workflow.status}</span>
                    </div>
                  </div>
                  <p className="text-gray-400 text-sm">{workflow.description}</p>
                </div>
                <div className="flex items-center space-x-2">
                  {workflow.status === 'active' ? (
                    <button
                      onClick={() => handleWorkflowAction(workflow.id, 'pause')}
                      className="p-2 text-yellow-500 hover:bg-slate-700 rounded transition-colors"
                    >
                      <Pause className="w-4 h-4" />
                    </button>
                  ) : (
                    <button
                      onClick={() => handleWorkflowAction(workflow.id, 'start')}
                      className="p-2 text-green-500 hover:bg-slate-700 rounded transition-colors"
                    >
                      <Play className="w-4 h-4" />
                    </button>
                  )}
                  <button
                    onClick={() => handleWorkflowAction(workflow.id, 'stop')}
                    className="p-2 text-red-500 hover:bg-slate-700 rounded transition-colors"
                  >
                    <Square className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleWorkflowAction(workflow.id, 'configure')}
                    className="p-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded transition-colors"
                  >
                    <Settings className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Workflow Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{workflow.runCount}</div>
                  <div className="text-xs text-gray-400">Total Runs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{workflow.successRate}%</div>
                  <div className="text-xs text-gray-400">Success Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{workflow.steps.length}</div>
                  <div className="text-xs text-gray-400">Steps</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{workflow.agents.length}</div>
                  <div className="text-xs text-gray-400">Agents</div>
                </div>
              </div>

              {/* Workflow Timeline */}
              <div className="flex items-center space-x-4 text-sm text-gray-400">
                <span>Trigger: {workflow.trigger}</span>
                {workflow.lastRun && (
                  <span>Last run: {workflow.lastRun.toLocaleString()}</span>
                )}
                {workflow.nextRun && (
                  <span>Next run: {workflow.nextRun.toLocaleString()}</span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AgentControlPanel;