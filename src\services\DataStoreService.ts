import { PDFProcessingResult } from './PDFProcessingService';
import { SemanticAnalysisResult } from './SemanticAnalysisService';

export interface ProcessedDocument {
  id: string;
  filename: string;
  uploadedAt: Date;
  pdfResult: PDFProcessingResult;
  semanticResult: SemanticAnalysisResult;
  fileSize: number;
  processingDuration: number;
}

export interface DocumentMetrics {
  totalDocuments: number;
  totalPages: number;
  totalWords: number;
  averageComplexity: number;
  languageDistribution: Record<string, number>;
  topicDistribution: Record<string, number>;
  sentimentDistribution: Record<string, number>;
}

export interface TimeRangeData {
  startDate: Date;
  endDate: Date;
  documents: ProcessedDocument[];
  metrics: DocumentMetrics;
}

class DataStoreService {
  private documents: ProcessedDocument[] = [];
  private listeners: ((documents: ProcessedDocument[]) => void)[] = [];

  // Add a new processed document
  addDocument(document: ProcessedDocument): void {
    this.documents.push(document);
    this.notifyListeners();
  }

  // Get all documents
  getAllDocuments(): ProcessedDocument[] {
    return [...this.documents];
  }

  // Get documents by time range
  getDocumentsByTimeRange(startDate: Date, endDate: Date): TimeRangeData {
    const filteredDocs = this.documents.filter(doc => 
      doc.uploadedAt >= startDate && doc.uploadedAt <= endDate
    );

    return {
      startDate,
      endDate,
      documents: filteredDocs,
      metrics: this.calculateMetrics(filteredDocs)
    };
  }

  // Get the latest document
  getLatestDocument(): ProcessedDocument | null {
    if (this.documents.length === 0) return null;
    return this.documents[this.documents.length - 1];
  }

  // Get document by ID
  getDocumentById(id: string): ProcessedDocument | null {
    return this.documents.find(doc => doc.id === id) || null;
  }

  // Calculate metrics for a set of documents
  private calculateMetrics(documents: ProcessedDocument[]): DocumentMetrics {
    if (documents.length === 0) {
      return {
        totalDocuments: 0,
        totalPages: 0,
        totalWords: 0,
        averageComplexity: 0,
        languageDistribution: {},
        topicDistribution: {},
        sentimentDistribution: {}
      };
    }

    const totalPages = documents.reduce((sum, doc) => sum + doc.pdfResult.metadata.pageCount, 0);
    const totalWords = documents.reduce((sum, doc) => sum + doc.pdfResult.wordCount, 0);
    const averageComplexity = documents.reduce((sum, doc) => sum + doc.semanticResult.complexity.readability, 0) / documents.length;

    // Language distribution
    const languageDistribution: Record<string, number> = {};
    documents.forEach(doc => {
      const lang = doc.semanticResult.language;
      languageDistribution[lang] = (languageDistribution[lang] || 0) + 1;
    });

    // Topic distribution
    const topicDistribution: Record<string, number> = {};
    documents.forEach(doc => {
      doc.semanticResult.topics.forEach(topic => {
        topicDistribution[topic.name] = (topicDistribution[topic.name] || 0) + topic.relevance;
      });
    });

    // Sentiment distribution
    const sentimentDistribution: Record<string, number> = {};
    documents.forEach(doc => {
      const sentiment = doc.semanticResult.sentiment.overall;
      sentimentDistribution[sentiment] = (sentimentDistribution[sentiment] || 0) + 1;
    });

    return {
      totalDocuments: documents.length,
      totalPages,
      totalWords,
      averageComplexity,
      languageDistribution,
      topicDistribution,
      sentimentDistribution
    };
  }

  // Get overall metrics
  getOverallMetrics(): DocumentMetrics {
    return this.calculateMetrics(this.documents);
  }

  // Check if we have sufficient data for a time range
  hasSufficientData(startDate: Date, endDate: Date): boolean {
    const docs = this.getDocumentsByTimeRange(startDate, endDate);
    return docs.documents.length > 0;
  }

  // Subscribe to document changes
  subscribe(listener: (documents: ProcessedDocument[]) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Notify all listeners
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener([...this.documents]));
  }

  // Clear all data
  clear(): void {
    this.documents = [];
    this.notifyListeners();
  }

  // Get documents for knowledge graph
  getKnowledgeGraphData(): {
    entities: Array<{ name: string; type: string; frequency: number }>;
    relationships: Array<{ source: string; target: string; strength: number }>;
  } {
    const entityMap = new Map<string, { type: string; frequency: number }>();
    const relationships: Array<{ source: string; target: string; strength: number }> = [];

    this.documents.forEach(doc => {
      // Collect entities
      doc.semanticResult.entities.forEach(entity => {
        const key = entity.text.toLowerCase();
        if (entityMap.has(key)) {
          entityMap.get(key)!.frequency += entity.confidence;
        } else {
          entityMap.set(key, { type: entity.type, frequency: entity.confidence });
        }
      });

      // Create relationships between entities in the same document
      const entities = doc.semanticResult.entities;
      for (let i = 0; i < entities.length; i++) {
        for (let j = i + 1; j < entities.length; j++) {
          relationships.push({
            source: entities[i].text.toLowerCase(),
            target: entities[j].text.toLowerCase(),
            strength: (entities[i].confidence + entities[j].confidence) / 2
          });
        }
      }
    });

    return {
      entities: Array.from(entityMap.entries()).map(([name, data]) => ({
        name,
        type: data.type,
        frequency: data.frequency
      })),
      relationships
    };
  }

  // Get behavioral patterns data
  getBehavioralPatternsData(): {
    readingPatterns: Array<{ pattern: string; frequency: number }>;
    contentPreferences: Array<{ category: string; score: number }>;
    complexityTrends: Array<{ date: string; complexity: number }>;
  } {
    const readingPatterns = [
      { pattern: 'Sequential Reading', frequency: this.documents.length * 0.7 },
      { pattern: 'Selective Reading', frequency: this.documents.length * 0.3 },
      { pattern: 'Deep Analysis', frequency: this.documents.filter(d => d.semanticResult.complexity.readability > 0.7).length }
    ];

    const contentPreferences = this.documents.reduce((acc, doc) => {
      doc.semanticResult.topics.forEach(topic => {
        const existing = acc.find(p => p.category === topic.name);
        if (existing) {
          existing.score += topic.relevance;
        } else {
          acc.push({ category: topic.name, score: topic.relevance });
        }
      });
      return acc;
    }, [] as Array<{ category: string; score: number }>);

    const complexityTrends = this.documents.map(doc => ({
      date: doc.uploadedAt.toISOString().split('T')[0],
      complexity: doc.semanticResult.complexity.readability
    }));

    return {
      readingPatterns,
      contentPreferences: contentPreferences.slice(0, 10), // Top 10
      complexityTrends
    };
  }
}

// Create a singleton instance
export const dataStore = new DataStoreService();
export default DataStoreService;