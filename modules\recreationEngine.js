/**
 * Recreation Engine Module
 * Handles the recreation of images based on analyzed elements
 */

/**
 * Recreation Engine - Converts analyzed image data into recreatable code
 */
class RecreationEngine {
    constructor() {
        this.blueprint = null;
        this.codeTemplates = {
            html: '',
            css: '',
            javascript: ''
        };
    }

    async recreateFromAnalysis(analysisData, options = {}) {
        // Extract options with defaults
        const {
            mode = 'standard',
            accuracy = 'medium',
            interactive = false
        } = options;

        // Filter and process elements based on accuracy level
        const processedElements = this.processElementsByAccuracy(analysisData.elements, accuracy);
        
        this.blueprint = {
            metadata: {
                originalSize: analysisData.imageSize,
                processingTime: analysisData.processingTime,
                timestamp: new Date().toISOString(),
                recreationMode: mode,
                accuracyLevel: accuracy,
                interactive: interactive
            },
            elements: processedElements,
            colors: analysisData.colors,
            code: this.generateCode({ ...analysisData, elements: processedElements }, options)
        };
        
        return this.blueprint;
    }

    processElementsByAccuracy(elements, accuracy) {
        if (!elements || !Array.isArray(elements)) return [];
        
        switch(accuracy) {
            case 'low':
                // Keep only major elements (larger than average)
                const avgSize = elements.reduce((sum, el) => {
                    const size = (el.width || el.radius * 2 || 50) * (el.height || el.radius * 2 || 50);
                    return sum + size;
                }, 0) / elements.length;
                return elements.filter(el => {
                    const size = (el.width || el.radius * 2 || 50) * (el.height || el.radius * 2 || 50);
                    return size >= avgSize;
                });
            case 'high':
                // Keep all elements with enhanced precision
                return elements.map(el => ({
                    ...el,
                    x: Math.round(el.x * 10) / 10,
                    y: Math.round(el.y * 10) / 10,
                    width: el.width ? Math.round(el.width * 10) / 10 : el.width,
                    height: el.height ? Math.round(el.height * 10) / 10 : el.height,
                    radius: el.radius ? Math.round(el.radius * 10) / 10 : el.radius
                }));
            case 'medium':
            default:
                // Keep all elements with standard precision
                return elements;
        }
    }

    generateCode(data, options = {}) {
        const html = this.generateHTML(data.elements, options);
        const css = this.generateCSS(data.elements, data.colors, options);
        const js = this.generateJavaScript(data.elements, options);
        
        return { html, css, js };
    }

    generateHTML(elements, options = {}) {
        const { interactive = false, mode = 'standard' } = options;
        const containerClass = mode === 'responsive' ? 'recreated-image responsive' : 'recreated-image';
        
        let html = `<div class="${containerClass}">\n`;
        
        elements.forEach((element, index) => {
            const interactiveAttrs = interactive ? ` tabindex="0" role="button" aria-label="Element ${index + 1}"` : '';
            
            switch(element.type) {
                case 'circle':
                    html += `  <div class="circle circle-${index}" data-element="${index}"${interactiveAttrs}></div>\n`;
                    break;
                case 'rectangle':
                    html += `  <div class="rectangle rect-${index}" data-element="${index}"${interactiveAttrs}></div>\n`;
                    break;
                case 'text':
                    html += `  <div class="text text-${index}" data-element="${index}"${interactiveAttrs}>${element.text || 'Text'}</div>\n`;
                    break;
                case 'line':
                    html += `  <div class="line line-${index}" data-element="${index}"${interactiveAttrs}></div>\n`;
                    break;
            }
        });
        
        html += '</div>';
        return html;
    }

    generateCSS(elements, colors, options = {}) {
        const { interactive = false, mode = 'standard' } = options;
        
        let css = `.recreated-image {
  position: relative;
  width: 100%;
  height: 500px;
  border: 1px solid #ccc;
}\n\n`;

        // Add responsive styles if mode is responsive
        if (mode === 'responsive') {
            css += `.recreated-image.responsive {
  max-width: 100%;
  height: auto;
  min-height: 300px;
}\n\n`;
        }

        // Add interactive styles if interactive mode is enabled
        if (interactive) {
            css += `[data-element] {
  cursor: pointer;
  transition: all 0.2s ease;
}\n\n[data-element]:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
  z-index: 10;
}\n\n[data-element]:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}\n\n`;
        }

        elements.forEach((element, index) => {
            switch(element.type) {
                case 'circle':
                    css += `.circle-${index} {
  position: absolute;
  left: ${element.x - element.radius}px;
  top: ${element.y - element.radius}px;
  width: ${element.radius * 2}px;
  height: ${element.radius * 2}px;
  border-radius: 50%;
  background-color: ${element.color.hex};
}\n\n`;
                    break;
                case 'rectangle':
                    css += `.rect-${index} {
  position: absolute;
  left: ${element.x}px;
  top: ${element.y}px;
  width: ${element.width}px;
  height: ${element.height}px;
  background-color: ${element.color.hex};
}\n\n`;
                    break;
                case 'text':
                    css += `.text-${index} {
  position: absolute;
  left: ${element.x}px;
  top: ${element.y}px;
  font-size: ${element.fontSize || 16}px;
  color: #333;
}\n\n`;
                    break;
                case 'line':
                    const length = Math.sqrt((element.x2 - element.x1) ** 2 + (element.y2 - element.y1) ** 2);
                    const angle = Math.atan2(element.y2 - element.y1, element.x2 - element.x1) * 180 / Math.PI;
                    css += `.line-${index} {
  position: absolute;
  left: ${element.x1}px;
  top: ${element.y1}px;
  width: ${length}px;
  height: 2px;
  background-color: #333;
  transform: rotate(${angle}deg);
  transform-origin: 0 50%;
}\n\n`;
                    break;
            }
        });

        return css;
    }

    generateJavaScript(elements, options = {}) {
        const { interactive = false, mode = 'standard' } = options;
        
        let js = `// Recreation Engine Generated Code
const recreatedElements = ${JSON.stringify(elements, null, 2)};
const recreationOptions = ${JSON.stringify(options, null, 2)};

function initializeRecreation() {
    console.log('Recreation initialized with', recreatedElements.length, 'elements');
    console.log('Recreation mode:', recreationOptions.mode || 'standard');
    console.log('Interactive mode:', recreationOptions.interactive || false);
    `;

        if (interactive) {
            js += `
    // Enhanced interactivity
    document.querySelectorAll('[data-element]').forEach(el => {
        // Click handler
        el.addEventListener('click', (e) => {
            const elementIndex = e.target.dataset.element;
            const element = recreatedElements[elementIndex];
            console.log('Clicked element:', element);
            
            // Highlight selected element
            document.querySelectorAll('[data-element]').forEach(elem => {
                elem.classList.remove('selected');
            });
            e.target.classList.add('selected');
            
            // Dispatch custom event
            window.dispatchEvent(new CustomEvent('elementSelected', {
                detail: { element, index: elementIndex }
            }));
        });
        
        // Keyboard navigation
        el.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                e.target.click();
            }
        });
        
        // Hover effects
        el.addEventListener('mouseenter', (e) => {
            const elementIndex = e.target.dataset.element;
            const element = recreatedElements[elementIndex];
            e.target.title = \`\${element.type} - Position: (\${Math.round(element.x)}, \${Math.round(element.y)})\`;
        });
    });`;
        } else {
            js += `
    // Basic click logging
    document.querySelectorAll('[data-element]').forEach(el => {
        el.addEventListener('click', (e) => {
            const elementIndex = e.target.dataset.element;
            console.log('Clicked element:', recreatedElements[elementIndex]);
        });
    });`;
        }

        js += `
}

// CSS for selected state
if (recreationOptions.interactive) {
    const style = document.createElement('style');
    style.textContent = \`
        [data-element].selected {
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5) !important;
            z-index: 20 !important;
        }
    \`;
    document.head.appendChild(style);
}

document.addEventListener('DOMContentLoaded', initializeRecreation);`;

        return js;
    }

    exportBlueprint() {
        if (!this.blueprint) return null;
        
        return {
            ...this.blueprint,
            exportedAt: new Date().toISOString(),
            version: '1.0.0'
        };
    }
}

export default RecreationEngine;