/**
 * Core types and interfaces for the MCP Vision Mode Agent System
 * Defines the foundational contracts for multi-agent collaboration
 */

export type AgentId = string;
export type TaskId = string;
export type MessageId = string;

/**
 * Agent capability levels for different types of operations
 */
export enum AgentCapability {
  SEMANTIC_ANALYSIS = 'semantic_analysis',
  WORKFLOW_ORCHESTRATION = 'workflow_orchestration',
  VISUALIZATION = 'visualization',
  DATA_EXTRACTION = 'data_extraction',
  CODE_GENERATION = 'code_generation',
  PATTERN_RECOGNITION = 'pattern_recognition',
  AUTOMATION = 'automation',
  EXPORT = 'export'
}

/**
 * Agent status states throughout its lifecycle
 */
export enum AgentStatus {
  INITIALIZING = 'initializing',
  READY = 'ready',
  BUSY = 'busy',
  WAITING = 'waiting',
  ERROR = 'error',
  OFFLINE = 'offline'
}

/**
 * Message types for inter-agent communication
 */
export enum MessageType {
  TASK_REQUEST = 'task_request',
  TASK_RESPONSE = 'task_response',
  STATUS_UPDATE = 'status_update',
  ERROR_REPORT = 'error_report',
  CAPABILITY_QUERY = 'capability_query',
  HEARTBEAT = 'heartbeat',
  SHUTDOWN = 'shutdown'
}

/**
 * Priority levels for task execution
 */
export enum TaskPriority {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4
}

/**
 * Task execution states
 */
export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

/**
 * Agent metadata for registration and discovery
 */
export interface AgentMetadata {
  id: AgentId;
  name: string;
  version: string;
  description: string;
  capabilities: AgentCapability[];
  status: AgentStatus;
  lastHeartbeat: Date;
  maxConcurrentTasks: number;
  currentTaskCount: number;
  tags: string[];
}

/**
 * Task definition for agent execution
 */
export interface AgentTask {
  id: TaskId;
  type: string;
  priority: TaskPriority;
  status: TaskStatus;
  requesterId: AgentId;
  assignedAgentId?: AgentId;
  payload: Record<string, any>;
  dependencies: TaskId[];
  timeout: number;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  result?: any;
  error?: string;
}

/**
 * Inter-agent message structure
 */
export interface AgentMessage {
  id: MessageId;
  type: MessageType;
  senderId: AgentId;
  receiverId: AgentId | 'broadcast';
  payload: Record<string, any>;
  timestamp: Date;
  correlationId?: string;
  replyTo?: MessageId;
}

/**
 * Agent configuration options
 */
export interface AgentConfig {
  id: AgentId;
  name: string;
  capabilities: AgentCapability[];
  maxConcurrentTasks?: number;
  heartbeatInterval?: number;
  taskTimeout?: number;
  retryPolicy?: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
  customSettings?: Record<string, any>;
}

/**
 * Agent execution context
 */
export interface AgentContext {
  taskId: TaskId;
  requesterId: AgentId;
  startTime: Date;
  timeout: number;
  correlationId: string;
  metadata: Record<string, any>;
}

/**
 * Agent performance metrics
 */
export interface AgentMetrics {
  tasksCompleted: number;
  tasksFailedCount: number;
  averageExecutionTime: number;
  lastExecutionTime: number;
  uptime: number;
  memoryUsage: number;
  cpuUsage: number;
}

/**
 * Event types for agent lifecycle and operations
 */
export enum AgentEventType {
  AGENT_REGISTERED = 'agent_registered',
  AGENT_UNREGISTERED = 'agent_unregistered',
  AGENT_STATUS_CHANGED = 'agent_status_changed',
  TASK_ASSIGNED = 'task_assigned',
  TASK_STARTED = 'task_started',
  TASK_COMPLETED = 'task_completed',
  TASK_FAILED = 'task_failed',
  MESSAGE_SENT = 'message_sent',
  MESSAGE_RECEIVED = 'message_received',
  ERROR_OCCURRED = 'error_occurred'
}

/**
 * Agent event structure
 */
export interface AgentEvent {
  type: AgentEventType;
  agentId: AgentId;
  timestamp: Date;
  data: Record<string, any>;
  correlationId?: string;
}

/**
 * Agent discovery and health check interface
 */
export interface AgentHealthCheck {
  isHealthy: boolean;
  lastCheck: Date;
  responseTime: number;
  errorCount: number;
  details: Record<string, any>;
}