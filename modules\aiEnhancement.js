/**
 * AI-Powered Enhancement Module
 * Integrates TensorFlow.js for advanced pattern recognition and intelligent features
 */

export class AIEnhancement {
    constructor() {
        this.tf = null;
        this.models = {
            shapeClassifier: null,
            layoutAnalyzer: null,
            colorPredictor: null,
            textDetector: null,
            patternMatcher: null
        };
        this.isInitialized = false;
        this.cache = new Map();
        this.config = {
            modelUrls: {
                shapeClassifier: 'https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@2.1.0',
                layoutAnalyzer: null, // Custom model
                colorPredictor: null, // Custom model
                textDetector: 'https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2.2.2',
                patternMatcher: null // Custom model
            },
            confidence: {
                minimum: 0.6,
                high: 0.8,
                excellent: 0.9
            },
            features: {
                enableShapeClassification: true,
                enableLayoutAnalysis: true,
                enableColorSuggestions: true,
                enableSmartGrouping: true,
                enablePredictiveRecreation: true
            }
        };
        this.analytics = {
            predictions: 0,
            accuracy: 0,
            processingTime: [],
            cacheHits: 0
        };
    }

    async initialize() {
        try {
            console.log('🤖 Initializing AI Enhancement module...');
            
            // Load TensorFlow.js
            if (typeof tf === 'undefined') {
                console.log('📦 Loading TensorFlow.js...');
                await this.loadTensorFlow();
            } else {
                this.tf = tf;
            }

            // Initialize models
            await this.initializeModels();
            
            // Setup feature extractors
            this.setupFeatureExtractors();
            
            this.isInitialized = true;
            console.log('✅ AI Enhancement module initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize AI Enhancement:', error);
            return false;
        }
    }

    async loadTensorFlow() {
        return new Promise((resolve, reject) => {
            if (window.tf) {
                this.tf = window.tf;
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.15.0/dist/tf.min.js';
            script.onload = () => {
                this.tf = window.tf;
                console.log('✅ TensorFlow.js loaded successfully');
                resolve();
            };
            script.onerror = () => {
                reject(new Error('Failed to load TensorFlow.js'));
            };
            document.head.appendChild(script);
        });
    }

    async initializeModels() {
        console.log('🧠 Loading AI models...');
        
        try {
            // Load pre-trained models
            await this.loadPreTrainedModels();
            
            // Create custom neural networks
            await this.createCustomModels();
            
            console.log('✅ All AI models loaded successfully');
        } catch (error) {
            console.warn('⚠️ Some AI models failed to load:', error);
            // Continue with available models
        }
    }

    async loadPreTrainedModels() {
        // Load shape classifier (using MobileNet as base)
        if (this.config.features.enableShapeClassification) {
            try {
                // For now, we'll create a custom shape classifier
                this.models.shapeClassifier = await this.createShapeClassifier();
                console.log('✅ Shape classifier loaded');
            } catch (error) {
                console.warn('⚠️ Shape classifier failed to load:', error);
            }
        }

        // Load text detector (simplified version)
        if (this.config.features.enableSmartGrouping) {
            try {
                this.models.textDetector = await this.createTextDetector();
                console.log('✅ Text detector loaded');
            } catch (error) {
                console.warn('⚠️ Text detector failed to load:', error);
            }
        }
    }

    async createCustomModels() {
        // Create layout analyzer
        if (this.config.features.enableLayoutAnalysis) {
            this.models.layoutAnalyzer = await this.createLayoutAnalyzer();
            console.log('✅ Layout analyzer created');
        }

        // Create color predictor
        if (this.config.features.enableColorSuggestions) {
            this.models.colorPredictor = await this.createColorPredictor();
            console.log('✅ Color predictor created');
        }

        // Create pattern matcher
        if (this.config.features.enablePredictiveRecreation) {
            this.models.patternMatcher = await this.createPatternMatcher();
            console.log('✅ Pattern matcher created');
        }
    }

    async createShapeClassifier() {
        // Create a simple neural network for shape classification
        const model = this.tf.sequential({
            layers: [
                this.tf.layers.dense({
                    inputShape: [64], // Feature vector size
                    units: 128,
                    activation: 'relu'
                }),
                this.tf.layers.dropout({ rate: 0.2 }),
                this.tf.layers.dense({
                    units: 64,
                    activation: 'relu'
                }),
                this.tf.layers.dropout({ rate: 0.2 }),
                this.tf.layers.dense({
                    units: 10, // Number of shape classes
                    activation: 'softmax'
                })
            ]
        });

        model.compile({
            optimizer: 'adam',
            loss: 'categoricalCrossentropy',
            metrics: ['accuracy']
        });

        return model;
    }

    async createLayoutAnalyzer() {
        // Create neural network for layout understanding
        const model = this.tf.sequential({
            layers: [
                this.tf.layers.dense({
                    inputShape: [100], // Layout feature vector
                    units: 256,
                    activation: 'relu'
                }),
                this.tf.layers.dropout({ rate: 0.3 }),
                this.tf.layers.dense({
                    units: 128,
                    activation: 'relu'
                }),
                this.tf.layers.dense({
                    units: 64,
                    activation: 'relu'
                }),
                this.tf.layers.dense({
                    units: 20, // Layout pattern classes
                    activation: 'softmax'
                })
            ]
        });

        model.compile({
            optimizer: 'adam',
            loss: 'categoricalCrossentropy',
            metrics: ['accuracy']
        });

        return model;
    }

    async createColorPredictor() {
        // Create neural network for color palette prediction
        const model = this.tf.sequential({
            layers: [
                this.tf.layers.dense({
                    inputShape: [15], // RGB values of 5 dominant colors
                    units: 64,
                    activation: 'relu'
                }),
                this.tf.layers.dense({
                    units: 32,
                    activation: 'relu'
                }),
                this.tf.layers.dense({
                    units: 15, // Predicted RGB values
                    activation: 'sigmoid'
                })
            ]
        });

        model.compile({
            optimizer: 'adam',
            loss: 'meanSquaredError',
            metrics: ['mae']
        });

        return model;
    }

    async createTextDetector() {
        // Simplified text detection model
        const model = this.tf.sequential({
            layers: [
                this.tf.layers.dense({
                    inputShape: [50], // Text feature vector
                    units: 100,
                    activation: 'relu'
                }),
                this.tf.layers.dense({
                    units: 50,
                    activation: 'relu'
                }),
                this.tf.layers.dense({
                    units: 1, // Text probability
                    activation: 'sigmoid'
                })
            ]
        });

        model.compile({
            optimizer: 'adam',
            loss: 'binaryCrossentropy',
            metrics: ['accuracy']
        });

        return model;
    }

    async createPatternMatcher() {
        // Create neural network for pattern matching
        const model = this.tf.sequential({
            layers: [
                this.tf.layers.dense({
                    inputShape: [200], // Pattern feature vector
                    units: 512,
                    activation: 'relu'
                }),
                this.tf.layers.dropout({ rate: 0.4 }),
                this.tf.layers.dense({
                    units: 256,
                    activation: 'relu'
                }),
                this.tf.layers.dropout({ rate: 0.3 }),
                this.tf.layers.dense({
                    units: 128,
                    activation: 'relu'
                }),
                this.tf.layers.dense({
                    units: 50, // Pattern similarity classes
                    activation: 'softmax'
                })
            ]
        });

        model.compile({
            optimizer: 'adam',
            loss: 'categoricalCrossentropy',
            metrics: ['accuracy']
        });

        return model;
    }

    setupFeatureExtractors() {
        // Setup feature extraction utilities
        this.featureExtractors = {
            shape: this.extractShapeFeatures.bind(this),
            layout: this.extractLayoutFeatures.bind(this),
            color: this.extractColorFeatures.bind(this),
            text: this.extractTextFeatures.bind(this),
            pattern: this.extractPatternFeatures.bind(this)
        };
    }

    // AI-Powered Features

    async classifyShape(element) {
        if (!this.models.shapeClassifier || !this.config.features.enableShapeClassification) {
            return null;
        }

        try {
            const startTime = performance.now();
            
            // Extract features from element
            const features = this.extractShapeFeatures(element);
            const cacheKey = `shape_${this.hashFeatures(features)}`;
            
            // Check cache
            if (this.cache.has(cacheKey)) {
                this.analytics.cacheHits++;
                return this.cache.get(cacheKey);
            }

            // Predict using neural network
            const prediction = await this.predictWithModel(
                this.models.shapeClassifier,
                features
            );

            const result = {
                type: this.getShapeType(prediction),
                confidence: Math.max(...prediction),
                probabilities: prediction,
                processingTime: performance.now() - startTime
            };

            // Cache result
            this.cache.set(cacheKey, result);
            this.updateAnalytics(result.processingTime);

            return result;
        } catch (error) {
            console.error('Shape classification error:', error);
            return null;
        }
    }

    async analyzeLayout(elements) {
        if (!this.models.layoutAnalyzer || !this.config.features.enableLayoutAnalysis) {
            return null;
        }

        try {
            const startTime = performance.now();
            
            // Extract layout features
            const features = this.extractLayoutFeatures(elements);
            const cacheKey = `layout_${this.hashFeatures(features)}`;
            
            if (this.cache.has(cacheKey)) {
                this.analytics.cacheHits++;
                return this.cache.get(cacheKey);
            }

            // Predict layout pattern
            const prediction = await this.predictWithModel(
                this.models.layoutAnalyzer,
                features
            );

            const result = {
                pattern: this.getLayoutPattern(prediction),
                confidence: Math.max(...prediction),
                suggestions: this.generateLayoutSuggestions(prediction),
                processingTime: performance.now() - startTime
            };

            this.cache.set(cacheKey, result);
            this.updateAnalytics(result.processingTime);

            return result;
        } catch (error) {
            console.error('Layout analysis error:', error);
            return null;
        }
    }

    async suggestColors(currentColors) {
        if (!this.models.colorPredictor || !this.config.features.enableColorSuggestions) {
            return null;
        }

        try {
            const startTime = performance.now();
            
            // Extract color features
            const features = this.extractColorFeatures(currentColors);
            const cacheKey = `colors_${this.hashFeatures(features)}`;
            
            if (this.cache.has(cacheKey)) {
                this.analytics.cacheHits++;
                return this.cache.get(cacheKey);
            }

            // Predict complementary colors
            const prediction = await this.predictWithModel(
                this.models.colorPredictor,
                features
            );

            const result = {
                suggestedColors: this.convertToColors(prediction),
                harmony: this.analyzeColorHarmony(currentColors, prediction),
                confidence: this.calculateColorConfidence(prediction),
                processingTime: performance.now() - startTime
            };

            this.cache.set(cacheKey, result);
            this.updateAnalytics(result.processingTime);

            return result;
        } catch (error) {
            console.error('Color suggestion error:', error);
            return null;
        }
    }

    async groupElements(elements) {
        if (!this.config.features.enableSmartGrouping) {
            return null;
        }

        try {
            const startTime = performance.now();
            
            // Analyze element relationships
            const groups = await this.analyzeElementRelationships(elements);
            
            const result = {
                groups: groups,
                confidence: this.calculateGroupingConfidence(groups),
                suggestions: this.generateGroupingSuggestions(groups),
                processingTime: performance.now() - startTime
            };

            this.updateAnalytics(result.processingTime);
            return result;
        } catch (error) {
            console.error('Element grouping error:', error);
            return null;
        }
    }

    async predictElements(partialRecreation) {
        if (!this.models.patternMatcher || !this.config.features.enablePredictiveRecreation) {
            return null;
        }

        try {
            const startTime = performance.now();
            
            // Extract pattern features
            const features = this.extractPatternFeatures(partialRecreation);
            
            // Predict missing elements
            const prediction = await this.predictWithModel(
                this.models.patternMatcher,
                features
            );

            const result = {
                predictedElements: this.generatePredictedElements(prediction),
                confidence: Math.max(...prediction),
                suggestions: this.generateCompletionSuggestions(prediction),
                processingTime: performance.now() - startTime
            };

            this.updateAnalytics(result.processingTime);
            return result;
        } catch (error) {
            console.error('Element prediction error:', error);
            return null;
        }
    }

    // Feature Extraction Methods

    extractShapeFeatures(element) {
        // Extract geometric and visual features from element
        const features = new Array(64).fill(0);
        
        if (element.type === 'rect') {
            features[0] = 1; // Rectangle indicator
            features[1] = element.width / element.height; // Aspect ratio
            features[2] = element.width; // Normalized width
            features[3] = element.height; // Normalized height
        } else if (element.type === 'circle') {
            features[4] = 1; // Circle indicator
            features[5] = element.radius; // Normalized radius
        } else if (element.type === 'path') {
            features[6] = 1; // Path indicator
            features[7] = element.pathLength || 0; // Path complexity
        }

        // Add color features
        if (element.fill) {
            const rgb = this.hexToRgb(element.fill);
            features[8] = rgb.r / 255;
            features[9] = rgb.g / 255;
            features[10] = rgb.b / 255;
        }

        // Add position features
        features[11] = element.x || 0;
        features[12] = element.y || 0;

        return features;
    }

    extractLayoutFeatures(elements) {
        const features = new Array(100).fill(0);
        
        if (!elements || elements.length === 0) return features;

        // Basic statistics
        features[0] = elements.length; // Element count
        
        // Position distribution
        const positions = elements.map(el => ({ x: el.x || 0, y: el.y || 0 }));
        const avgX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
        const avgY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
        
        features[1] = avgX;
        features[2] = avgY;
        
        // Spacing analysis
        const spacings = this.calculateSpacings(positions);
        features[3] = spacings.horizontal;
        features[4] = spacings.vertical;
        
        // Alignment analysis
        const alignments = this.analyzeAlignments(positions);
        features[5] = alignments.horizontal;
        features[6] = alignments.vertical;
        
        // Symmetry analysis
        const symmetry = this.analyzeSymmetry(positions);
        features[7] = symmetry.horizontal;
        features[8] = symmetry.vertical;

        return features;
    }

    extractColorFeatures(colors) {
        const features = new Array(15).fill(0);
        
        if (!colors || colors.length === 0) return features;

        // Convert up to 5 colors to RGB values
        for (let i = 0; i < Math.min(5, colors.length); i++) {
            const rgb = this.hexToRgb(colors[i]);
            features[i * 3] = rgb.r / 255;
            features[i * 3 + 1] = rgb.g / 255;
            features[i * 3 + 2] = rgb.b / 255;
        }

        return features;
    }

    extractTextFeatures(element) {
        const features = new Array(50).fill(0);
        
        if (element.type === 'text') {
            features[0] = 1; // Text indicator
            features[1] = element.text ? element.text.length : 0; // Text length
            features[2] = element.fontSize || 0; // Font size
            features[3] = element.fontWeight === 'bold' ? 1 : 0; // Bold
            features[4] = element.fontStyle === 'italic' ? 1 : 0; // Italic
        }

        return features;
    }

    extractPatternFeatures(recreation) {
        const features = new Array(200).fill(0);
        
        if (!recreation || !recreation.elements) return features;

        const elements = recreation.elements;
        
        // Element type distribution
        const typeCount = {};
        elements.forEach(el => {
            typeCount[el.type] = (typeCount[el.type] || 0) + 1;
        });
        
        features[0] = typeCount.rect || 0;
        features[1] = typeCount.circle || 0;
        features[2] = typeCount.path || 0;
        features[3] = typeCount.text || 0;
        
        // Complexity metrics
        features[4] = elements.length; // Total elements
        features[5] = this.calculateComplexity(elements); // Visual complexity
        
        return features;
    }

    // Utility Methods

    async predictWithModel(model, features) {
        const tensor = this.tf.tensor2d([features]);
        const prediction = await model.predict(tensor).data();
        tensor.dispose();
        return Array.from(prediction);
    }

    getShapeType(prediction) {
        const shapeTypes = [
            'rectangle', 'circle', 'triangle', 'polygon', 
            'line', 'curve', 'star', 'arrow', 'diamond', 'other'
        ];
        const maxIndex = prediction.indexOf(Math.max(...prediction));
        return shapeTypes[maxIndex] || 'unknown';
    }

    getLayoutPattern(prediction) {
        const patterns = [
            'grid', 'linear', 'circular', 'scattered', 'hierarchical',
            'symmetric', 'asymmetric', 'centered', 'aligned', 'random',
            'clustered', 'spaced', 'overlapping', 'nested', 'flowing',
            'structured', 'organic', 'geometric', 'artistic', 'functional'
        ];
        const maxIndex = prediction.indexOf(Math.max(...prediction));
        return patterns[maxIndex] || 'unknown';
    }

    generateLayoutSuggestions(prediction) {
        const suggestions = [];
        const patterns = this.getLayoutPattern(prediction);
        
        switch (patterns) {
            case 'grid':
                suggestions.push('Consider adding more structure with consistent spacing');
                suggestions.push('Align elements to a grid for better organization');
                break;
            case 'linear':
                suggestions.push('Add visual hierarchy with size variations');
                suggestions.push('Consider grouping related elements');
                break;
            case 'scattered':
                suggestions.push('Group related elements together');
                suggestions.push('Create visual paths between important elements');
                break;
            default:
                suggestions.push('Maintain consistent spacing between elements');
                suggestions.push('Consider the visual flow and hierarchy');
        }
        
        return suggestions;
    }

    convertToColors(prediction) {
        const colors = [];
        for (let i = 0; i < prediction.length; i += 3) {
            if (i + 2 < prediction.length) {
                const r = Math.round(prediction[i] * 255);
                const g = Math.round(prediction[i + 1] * 255);
                const b = Math.round(prediction[i + 2] * 255);
                colors.push(`rgb(${r}, ${g}, ${b})`);
            }
        }
        return colors;
    }

    analyzeColorHarmony(currentColors, predictedColors) {
        // Simplified color harmony analysis
        return {
            type: 'complementary',
            score: 0.8,
            description: 'Colors work well together'
        };
    }

    calculateColorConfidence(prediction) {
        // Calculate confidence based on prediction certainty
        const variance = this.calculateVariance(prediction);
        return Math.max(0, 1 - variance);
    }

    async analyzeElementRelationships(elements) {
        const groups = [];
        const processed = new Set();
        
        for (let i = 0; i < elements.length; i++) {
            if (processed.has(i)) continue;
            
            const group = [elements[i]];
            processed.add(i);
            
            // Find related elements
            for (let j = i + 1; j < elements.length; j++) {
                if (processed.has(j)) continue;
                
                if (this.areElementsRelated(elements[i], elements[j])) {
                    group.push(elements[j]);
                    processed.add(j);
                }
            }
            
            if (group.length > 1) {
                groups.push({
                    elements: group,
                    type: this.determineGroupType(group),
                    confidence: this.calculateGroupConfidence(group)
                });
            }
        }
        
        return groups;
    }

    areElementsRelated(element1, element2) {
        // Check spatial proximity
        const distance = Math.sqrt(
            Math.pow((element1.x || 0) - (element2.x || 0), 2) +
            Math.pow((element1.y || 0) - (element2.y || 0), 2)
        );
        
        // Check visual similarity
        const colorSimilar = element1.fill === element2.fill;
        const typeSimilar = element1.type === element2.type;
        
        return distance < 100 || colorSimilar || typeSimilar;
    }

    determineGroupType(group) {
        const types = group.map(el => el.type);
        const uniqueTypes = [...new Set(types)];
        
        if (uniqueTypes.length === 1) {
            return `${uniqueTypes[0]}_group`;
        } else {
            return 'mixed_group';
        }
    }

    calculateGroupConfidence(group) {
        // Simple confidence calculation based on group coherence
        return Math.min(1, group.length / 5);
    }

    calculateGroupingConfidence(groups) {
        if (groups.length === 0) return 0;
        const avgConfidence = groups.reduce((sum, group) => sum + group.confidence, 0) / groups.length;
        return avgConfidence;
    }

    generateGroupingSuggestions(groups) {
        const suggestions = [];
        
        if (groups.length === 0) {
            suggestions.push('No clear grouping patterns detected');
            suggestions.push('Consider organizing elements by function or visual similarity');
        } else {
            suggestions.push(`Found ${groups.length} potential groups`);
            suggestions.push('Consider applying consistent styling within groups');
            suggestions.push('Use spacing to separate different groups');
        }
        
        return suggestions;
    }

    generatePredictedElements(prediction) {
        // Generate predicted elements based on pattern analysis
        const elements = [];
        
        // This is a simplified implementation
        // In a real scenario, this would be much more sophisticated
        if (prediction[0] > 0.7) { // High confidence for rectangle
            elements.push({
                type: 'rect',
                x: 100,
                y: 100,
                width: 50,
                height: 30,
                fill: '#cccccc',
                confidence: prediction[0]
            });
        }
        
        return elements;
    }

    generateCompletionSuggestions(prediction) {
        const suggestions = [];
        const maxConfidence = Math.max(...prediction);
        
        if (maxConfidence > this.config.confidence.high) {
            suggestions.push('High confidence prediction available');
            suggestions.push('Consider adding the suggested elements');
        } else if (maxConfidence > this.config.confidence.minimum) {
            suggestions.push('Moderate confidence prediction');
            suggestions.push('Review suggested elements before adding');
        } else {
            suggestions.push('Low confidence prediction');
            suggestions.push('Manual completion recommended');
        }
        
        return suggestions;
    }

    // Helper Methods

    hashFeatures(features) {
        return features.reduce((hash, val) => {
            return ((hash << 5) - hash) + val;
        }, 0).toString();
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : { r: 0, g: 0, b: 0 };
    }

    calculateSpacings(positions) {
        // Calculate average horizontal and vertical spacings
        let horizontalSpacing = 0;
        let verticalSpacing = 0;
        let count = 0;
        
        for (let i = 0; i < positions.length - 1; i++) {
            for (let j = i + 1; j < positions.length; j++) {
                horizontalSpacing += Math.abs(positions[i].x - positions[j].x);
                verticalSpacing += Math.abs(positions[i].y - positions[j].y);
                count++;
            }
        }
        
        return {
            horizontal: count > 0 ? horizontalSpacing / count : 0,
            vertical: count > 0 ? verticalSpacing / count : 0
        };
    }

    analyzeAlignments(positions) {
        // Analyze horizontal and vertical alignments
        const xValues = positions.map(pos => pos.x);
        const yValues = positions.map(pos => pos.y);
        
        const xAlignment = this.calculateAlignment(xValues);
        const yAlignment = this.calculateAlignment(yValues);
        
        return {
            horizontal: xAlignment,
            vertical: yAlignment
        };
    }

    calculateAlignment(values) {
        // Calculate how well values are aligned (0 = scattered, 1 = perfectly aligned)
        if (values.length < 2) return 1;
        
        const sorted = [...values].sort((a, b) => a - b);
        const range = sorted[sorted.length - 1] - sorted[0];
        const avgSpacing = range / (values.length - 1);
        
        let alignmentScore = 0;
        for (let i = 1; i < sorted.length; i++) {
            const spacing = sorted[i] - sorted[i - 1];
            const deviation = Math.abs(spacing - avgSpacing);
            alignmentScore += Math.max(0, 1 - deviation / avgSpacing);
        }
        
        return alignmentScore / (values.length - 1);
    }

    analyzeSymmetry(positions) {
        // Analyze horizontal and vertical symmetry
        if (positions.length < 2) return { horizontal: 1, vertical: 1 };
        
        const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
        const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
        
        let horizontalSymmetry = 0;
        let verticalSymmetry = 0;
        
        positions.forEach(pos => {
            const mirrorX = 2 * centerX - pos.x;
            const mirrorY = 2 * centerY - pos.y;
            
            // Find closest position to mirror point
            const closestToMirrorX = positions.reduce((closest, p) => 
                Math.abs(p.x - mirrorX) < Math.abs(closest.x - mirrorX) ? p : closest
            );
            const closestToMirrorY = positions.reduce((closest, p) => 
                Math.abs(p.y - mirrorY) < Math.abs(closest.y - mirrorY) ? p : closest
            );
            
            horizontalSymmetry += 1 - Math.abs(closestToMirrorX.x - mirrorX) / 100;
            verticalSymmetry += 1 - Math.abs(closestToMirrorY.y - mirrorY) / 100;
        });
        
        return {
            horizontal: Math.max(0, horizontalSymmetry / positions.length),
            vertical: Math.max(0, verticalSymmetry / positions.length)
        };
    }

    calculateComplexity(elements) {
        // Calculate visual complexity score
        let complexity = 0;
        
        complexity += elements.length * 0.1; // Base complexity from element count
        
        elements.forEach(element => {
            switch (element.type) {
                case 'rect':
                case 'circle':
                    complexity += 1;
                    break;
                case 'path':
                    complexity += 2;
                    break;
                case 'text':
                    complexity += 1.5;
                    break;
                default:
                    complexity += 1;
            }
        });
        
        return complexity;
    }

    calculateVariance(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
        return variance;
    }

    updateAnalytics(processingTime) {
        this.analytics.predictions++;
        this.analytics.processingTime.push(processingTime);
        
        // Keep only last 100 processing times
        if (this.analytics.processingTime.length > 100) {
            this.analytics.processingTime = this.analytics.processingTime.slice(-100);
        }
    }

    // Public API Methods

    getAnalytics() {
        const avgProcessingTime = this.analytics.processingTime.length > 0 ?
            this.analytics.processingTime.reduce((sum, time) => sum + time, 0) / this.analytics.processingTime.length :
            0;

        return {
            ...this.analytics,
            averageProcessingTime: avgProcessingTime,
            cacheHitRate: this.analytics.predictions > 0 ? 
                this.analytics.cacheHits / this.analytics.predictions : 0
        };
    }

    clearCache() {
        this.cache.clear();
        console.log('🧹 AI cache cleared');
    }

    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ AI configuration updated');
    }

    isFeatureEnabled(feature) {
        return this.config.features[feature] || false;
    }

    getModelStatus() {
        const status = {};
        Object.keys(this.models).forEach(key => {
            status[key] = this.models[key] ? 'loaded' : 'not_loaded';
        });
        return status;
    }
}

// Export singleton instance
export const aiEnhancement = new AIEnhancement();

// Make it globally available
if (typeof window !== 'undefined') {
    window.AIEnhancement = AIEnhancement;
    window.aiEnhancement = aiEnhancement;
}