<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syntax Error Debug</title>
</head>
<body>
    <h1>Syntax Error Debugging</h1>
    <div id="results"></div>
    
    <script type="module">
        // Simple test to isolate the syntax error
        try {
            console.log('Testing basic module loading...');
            
            // Test the main app import
            import('./recreationApp.js')
                .then(() => {
                    console.log('✅ Main app loaded successfully');
                    document.getElementById('results').innerHTML = '<p style="color: green;">✅ No syntax errors found!</p>';
                })
                .catch(error => {
                    console.error('❌ Syntax error found:', error);
                    document.getElementById('results').innerHTML = `
                        <p style="color: red;">❌ Syntax Error Found:</p>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">${error.message}</pre>
                        <p><strong>Stack:</strong></p>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">${error.stack}</pre>
                    `;
                });
        } catch (error) {
            console.error('❌ Critical error:', error);
            document.getElementById('results').innerHTML = `<p style="color: red;">❌ Critical Error: ${error.message}</p>`;
        }
    </script>
</body>
</html>