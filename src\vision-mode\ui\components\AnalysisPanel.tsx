/**
 * Analysis Panel Component
 * Interface for semantic analysis and knowledge extraction
 */

import React, { useState } from 'react';
import {
  Brain,
  FileText,
  Search,
  BarChart3,
  Network,
  Eye,
  Layers,
  Target
} from 'lucide-react';

export interface AnalysisPanelProps {
  visionAgent?: any;
  systemStatus?: any;
  onStatusUpdate?: (status: any) => void;
}

export const AnalysisPanel: React.FC<AnalysisPanelProps> = ({
  visionAgent,
  systemStatus,
  onStatusUpdate
}) => {
  const [activeAnalysis, setActiveAnalysis] = useState('semantic');
  const [analysisResults] = useState({
    semantic: {
      entities: 24,
      relationships: 156,
      concepts: 89,
      confidence: 0.87
    },
    sentiment: {
      positive: 45,
      neutral: 35,
      negative: 20,
      overall: 'positive'
    },
    topics: [
      { name: 'Technology', weight: 0.8, documents: 12 },
      { name: 'Business', weight: 0.6, documents: 8 },
      { name: 'Innovation', weight: 0.4, documents: 5 }
    ]
  });

  const analysisTypes = [
    { id: 'semantic', label: 'Semantic Analysis', icon: Brain },
    { id: 'sentiment', label: 'Sentiment Analysis', icon: Target },
    { id: 'topics', label: 'Topic Modeling', icon: Layers },
    { id: 'entities', label: 'Entity Recognition', icon: Search }
  ];

  return (
    <div className="p-6 h-full overflow-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white flex items-center space-x-2">
          <Brain className="w-6 h-6 text-purple-500" />
          <span>Semantic Analysis</span>
        </h2>
        <p className="text-gray-400 mt-1">Document analysis and knowledge extraction</p>
      </div>

      {/* Analysis Type Selector */}
      <div className="flex space-x-1 bg-slate-800 p-1 rounded-lg mb-6">
        {analysisTypes.map((type) => {
          const Icon = type.icon;
          return (
            <button
              key={type.id}
              onClick={() => setActiveAnalysis(type.id)}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeAnalysis === type.id
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-slate-700'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="hidden sm:inline">{type.label}</span>
            </button>
          );
        })}
      </div>

      {/* Analysis Results */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Semantic Analysis */}
        {activeAnalysis === 'semantic' && (
          <>
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Network className="w-5 h-5 text-blue-500" />
                <h3 className="text-lg font-semibold text-white">Entities</h3>
              </div>
              <div className="text-3xl font-bold text-blue-500 mb-2">
                {analysisResults.semantic.entities}
              </div>
              <p className="text-gray-400 text-sm">Named entities identified</p>
            </div>

            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <BarChart3 className="w-5 h-5 text-green-500" />
                <h3 className="text-lg font-semibold text-white">Relationships</h3>
              </div>
              <div className="text-3xl font-bold text-green-500 mb-2">
                {analysisResults.semantic.relationships}
              </div>
              <p className="text-gray-400 text-sm">Semantic relationships found</p>
            </div>

            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Eye className="w-5 h-5 text-purple-500" />
                <h3 className="text-lg font-semibold text-white">Concepts</h3>
              </div>
              <div className="text-3xl font-bold text-purple-500 mb-2">
                {analysisResults.semantic.concepts}
              </div>
              <p className="text-gray-400 text-sm">Key concepts extracted</p>
            </div>
          </>
        )}

        {/* Topic Analysis */}
        {activeAnalysis === 'topics' && (
          <div className="col-span-full">
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Topic Distribution</h3>
              <div className="space-y-4">
                {analysisResults.topics.map((topic, index) => (
                  <div key={index} className="">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-white font-medium">{topic.name}</span>
                      <span className="text-gray-400 text-sm">{topic.documents} docs</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${topic.weight * 100}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Sentiment Analysis */}
        {activeAnalysis === 'sentiment' && (
          <div className="col-span-full">
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Sentiment Distribution</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-500 mb-1">
                    {analysisResults.sentiment.positive}%
                  </div>
                  <p className="text-gray-400">Positive</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-400 mb-1">
                    {analysisResults.sentiment.neutral}%
                  </div>
                  <p className="text-gray-400">Neutral</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-500 mb-1">
                    {analysisResults.sentiment.negative}%
                  </div>
                  <p className="text-gray-400">Negative</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Entity Recognition */}
        {activeAnalysis === 'entities' && (
          <div className="col-span-full">
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Entity Recognition</h3>
              <p className="text-gray-400">Entity recognition analysis will be displayed here.</p>
            </div>
          </div>
        )}
      </div>

      {/* Confidence Score */}
      <div className="mt-6">
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
          <div className="flex items-center justify-between">
            <span className="text-gray-400">Analysis Confidence</span>
            <span className="text-white font-semibold">
              {Math.round(analysisResults.semantic.confidence * 100)}%
            </span>
          </div>
          <div className="w-full bg-slate-700 rounded-full h-2 mt-2">
            <div 
              className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${analysisResults.semantic.confidence * 100}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalysisPanel;