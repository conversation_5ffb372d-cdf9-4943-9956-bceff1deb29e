/**
 * Interactive Editor Module
 * Provides real-time editing capabilities for recreated elements
 */

class InteractiveEditor {
    constructor() {
        this.selectedElements = new Set();
        this.clipboard = [];
        this.history = [];
        this.historyIndex = -1;
        this.maxHistorySize = 50;
        this.isDragging = false;
        this.dragStartPos = { x: 0, y: 0 };
        this.elements = [];
        this.layers = new Map();
        this.currentLayer = 'shapes';
        this.snapToGrid = true;
        this.gridSize = 10;
        this.zoom = 1;
        this.pan = { x: 0, y: 0 };
        
        this.initializeEditor();
        this.setupEventListeners();
    }

    initializeEditor() {
        // Initialize layers
        this.layers.set('background', { visible: true, locked: false, elements: [] });
        this.layers.set('shapes', { visible: true, locked: false, elements: [] });
        this.layers.set('text', { visible: true, locked: false, elements: [] });
        this.layers.set('connections', { visible: true, locked: false, elements: [] });
        this.layers.set('annotations', { visible: true, locked: false, elements: [] });

        // Initialize property panels
        this.initializePropertyPanels();
        
        // Initialize toolbar
        this.initializeToolbar();
        
        // Save initial state
        this.saveState('Initial state');
    }

    setupEventListeners() {
        const svg = document.getElementById('recreationSVG');
        if (!svg) return;

        // Mouse events for selection and dragging
        svg.addEventListener('mousedown', this.handleMouseDown.bind(this));
        svg.addEventListener('mousemove', this.handleMouseMove.bind(this));
        svg.addEventListener('mouseup', this.handleMouseUp.bind(this));
        svg.addEventListener('click', this.handleClick.bind(this));

        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        document.addEventListener('keyup', this.handleKeyUp.bind(this));

        // Context menu
        svg.addEventListener('contextmenu', this.handleContextMenu.bind(this));

        // Zoom and pan
        svg.addEventListener('wheel', this.handleWheel.bind(this));

        // Layer controls
        this.setupLayerControls();
        
        // Property panel updates
        this.setupPropertyControls();
    }

    // Element Selection and Manipulation
    selectElement(elementId, addToSelection = false) {
        const element = this.getElementById(elementId);
        if (!element) return;

        if (!addToSelection) {
            this.clearSelection();
        }

        this.selectedElements.add(elementId);
        this.highlightElement(element);
        this.updatePropertyPanel();
        this.updateSelectionInfo();
    }

    clearSelection() {
        this.selectedElements.forEach(id => {
            const element = this.getElementById(id);
            if (element) {
                this.unhighlightElement(element);
            }
        });
        this.selectedElements.clear();
        this.updatePropertyPanel();
    }

    deleteSelectedElements() {
        if (this.selectedElements.size === 0) return;

        const elementsToDelete = Array.from(this.selectedElements);
        this.saveState('Delete elements');

        elementsToDelete.forEach(id => {
            this.deleteElement(id);
        });

        this.clearSelection();
        this.updateLayerCounts();
    }

    duplicateSelectedElements() {
        if (this.selectedElements.size === 0) return;

        this.saveState('Duplicate elements');
        const newSelection = new Set();

        this.selectedElements.forEach(id => {
            const element = this.getElementById(id);
            if (element) {
                const newElement = this.duplicateElement(element);
                newSelection.add(newElement.id);
            }
        });

        this.clearSelection();
        newSelection.forEach(id => this.selectElement(id, true));
    }

    // Drag and Drop Implementation
    handleMouseDown(event) {
        const target = event.target.closest('[data-element-id]');
        if (!target) return;

        const elementId = target.dataset.elementId;
        const isSelected = this.selectedElements.has(elementId);

        if (!isSelected) {
            this.selectElement(elementId, event.ctrlKey || event.metaKey);
        }

        if (this.selectedElements.size > 0) {
            this.isDragging = true;
            this.dragStartPos = { x: event.clientX, y: event.clientY };
            
            // Store initial positions for all selected elements
            this.dragInitialPositions = new Map();
            this.selectedElements.forEach(id => {
                const element = this.getElementById(id);
                if (element) {
                    const rect = element.getBoundingClientRect();
                    this.dragInitialPositions.set(id, {
                        x: parseFloat(element.getAttribute('x') || element.getAttribute('cx') || 0),
                        y: parseFloat(element.getAttribute('y') || element.getAttribute('cy') || 0)
                    });
                }
            });

            event.preventDefault();
        }
    }

    handleMouseMove(event) {
        if (!this.isDragging || this.selectedElements.size === 0) return;

        const deltaX = (event.clientX - this.dragStartPos.x) / this.zoom;
        const deltaY = (event.clientY - this.dragStartPos.y) / this.zoom;

        this.selectedElements.forEach(id => {
            const element = this.getElementById(id);
            const initialPos = this.dragInitialPositions.get(id);
            
            if (element && initialPos) {
                let newX = initialPos.x + deltaX;
                let newY = initialPos.y + deltaY;

                // Snap to grid if enabled
                if (this.snapToGrid) {
                    newX = Math.round(newX / this.gridSize) * this.gridSize;
                    newY = Math.round(newY / this.gridSize) * this.gridSize;
                }

                this.moveElement(element, newX, newY);
            }
        });

        this.updatePropertyPanel();
    }

    handleMouseUp(event) {
        if (this.isDragging) {
            this.isDragging = false;
            this.saveState('Move elements');
        }
    }

    // Element Manipulation Methods
    moveElement(element, x, y) {
        const elementData = this.getElementData(element);
        
        switch (elementData.type) {
            case 'circle':
                element.setAttribute('cx', x);
                element.setAttribute('cy', y);
                break;
            case 'rect':
                element.setAttribute('x', x);
                element.setAttribute('y', y);
                break;
            case 'line':
                const dx = x - parseFloat(element.getAttribute('x1'));
                const dy = y - parseFloat(element.getAttribute('y1'));
                element.setAttribute('x1', x);
                element.setAttribute('y1', y);
                element.setAttribute('x2', parseFloat(element.getAttribute('x2')) + dx);
                element.setAttribute('y2', parseFloat(element.getAttribute('y2')) + dy);
                break;
            case 'text':
                element.setAttribute('x', x);
                element.setAttribute('y', y);
                break;
            default:
                if (element.tagName === 'g') {
                    element.setAttribute('transform', `translate(${x}, ${y})`);
                }
        }

        // Update element data
        this.updateElementData(element.dataset.elementId, { x, y });
    }

    resizeElement(element, width, height) {
        const elementData = this.getElementData(element);
        
        switch (elementData.type) {
            case 'circle':
                element.setAttribute('r', Math.min(width, height) / 2);
                break;
            case 'rect':
                element.setAttribute('width', width);
                element.setAttribute('height', height);
                break;
            case 'line':
                // For lines, adjust the end point
                const x1 = parseFloat(element.getAttribute('x1'));
                const y1 = parseFloat(element.getAttribute('y1'));
                element.setAttribute('x2', x1 + width);
                element.setAttribute('y2', y1 + height);
                break;
        }

        this.updateElementData(element.dataset.elementId, { width, height });
        this.saveState('Resize element');
    }

    // Property Panel Implementation
    initializePropertyPanels() {
        const propertiesContent = document.getElementById('propertiesContent');
        if (!propertiesContent) return;

        propertiesContent.innerHTML = `
            <div class="property-group" id="positionGroup" style="display: none;">
                <h4>Position</h4>
                <div class="property-row">
                    <label>X:</label>
                    <input type="number" id="propX" step="1">
                </div>
                <div class="property-row">
                    <label>Y:</label>
                    <input type="number" id="propY" step="1">
                </div>
            </div>

            <div class="property-group" id="sizeGroup" style="display: none;">
                <h4>Size</h4>
                <div class="property-row">
                    <label>Width:</label>
                    <input type="number" id="propWidth" step="1" min="1">
                </div>
                <div class="property-row">
                    <label>Height:</label>
                    <input type="number" id="propHeight" step="1" min="1">
                </div>
            </div>

            <div class="property-group" id="styleGroup" style="display: none;">
                <h4>Style</h4>
                <div class="property-row">
                    <label>Fill:</label>
                    <input type="color" id="propFill">
                </div>
                <div class="property-row">
                    <label>Stroke:</label>
                    <input type="color" id="propStroke">
                </div>
                <div class="property-row">
                    <label>Stroke Width:</label>
                    <input type="number" id="propStrokeWidth" step="0.5" min="0">
                </div>
                <div class="property-row">
                    <label>Opacity:</label>
                    <input type="range" id="propOpacity" min="0" max="1" step="0.1">
                    <span id="opacityValue">1.0</span>
                </div>
            </div>

            <div class="property-group" id="textGroup" style="display: none;">
                <h4>Text</h4>
                <div class="property-row">
                    <label>Content:</label>
                    <input type="text" id="propTextContent">
                </div>
                <div class="property-row">
                    <label>Font Size:</label>
                    <input type="number" id="propFontSize" step="1" min="8">
                </div>
                <div class="property-row">
                    <label>Font Family:</label>
                    <select id="propFontFamily">
                        <option value="Arial">Arial</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Courier New">Courier New</option>
                    </select>
                </div>
            </div>

            <div class="property-group" id="layerGroup" style="display: none;">
                <h4>Layer</h4>
                <div class="property-row">
                    <label>Layer:</label>
                    <select id="propLayer">
                        <option value="background">Background</option>
                        <option value="shapes">Shapes</option>
                        <option value="text">Text</option>
                        <option value="connections">Connections</option>
                        <option value="annotations">Annotations</option>
                    </select>
                </div>
                <div class="property-row">
                    <label>Z-Index:</label>
                    <input type="number" id="propZIndex" step="1">
                </div>
            </div>

            <div class="property-actions">
                <button id="duplicateBtn" class="action-btn">Duplicate</button>
                <button id="deleteBtn" class="action-btn danger">Delete</button>
                <button id="groupBtn" class="action-btn">Group</button>
                <button id="ungroupBtn" class="action-btn">Ungroup</button>
            </div>
        `;
    }

    updatePropertyPanel() {
        if (this.selectedElements.size === 0) {
            this.hideAllPropertyGroups();
            return;
        }

        if (this.selectedElements.size === 1) {
            this.showSingleElementProperties();
        } else {
            this.showMultiElementProperties();
        }
    }

    showSingleElementProperties() {
        const elementId = Array.from(this.selectedElements)[0];
        const element = this.getElementById(elementId);
        const elementData = this.getElementData(element);

        // Show relevant property groups
        document.getElementById('positionGroup').style.display = 'block';
        document.getElementById('sizeGroup').style.display = 'block';
        document.getElementById('styleGroup').style.display = 'block';
        document.getElementById('layerGroup').style.display = 'block';

        // Populate position values
        document.getElementById('propX').value = elementData.x || 0;
        document.getElementById('propY').value = elementData.y || 0;

        // Populate size values
        document.getElementById('propWidth').value = elementData.width || 0;
        document.getElementById('propHeight').value = elementData.height || 0;

        // Populate style values
        document.getElementById('propFill').value = elementData.fill || '#000000';
        document.getElementById('propStroke').value = elementData.stroke || '#000000';
        document.getElementById('propStrokeWidth').value = elementData.strokeWidth || 1;
        document.getElementById('propOpacity').value = elementData.opacity || 1;
        document.getElementById('opacityValue').textContent = elementData.opacity || 1;

        // Show text properties if it's a text element
        if (elementData.type === 'text') {
            document.getElementById('textGroup').style.display = 'block';
            document.getElementById('propTextContent').value = elementData.text || '';
            document.getElementById('propFontSize').value = elementData.fontSize || 16;
            document.getElementById('propFontFamily').value = elementData.fontFamily || 'Arial';
        }

        // Populate layer values
        document.getElementById('propLayer').value = elementData.layer || 'shapes';
        document.getElementById('propZIndex').value = elementData.zIndex || 0;
    }

    // Undo/Redo System
    saveState(description) {
        // Remove any states after current index (when user made changes after undo)
        this.history = this.history.slice(0, this.historyIndex + 1);
        
        // Add new state
        const state = {
            description,
            timestamp: Date.now(),
            elements: this.serializeElements(),
            selectedElements: Array.from(this.selectedElements)
        };
        
        this.history.push(state);
        this.historyIndex++;
        
        // Limit history size
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
            this.historyIndex--;
        }
        
        this.updateUndoRedoButtons();
    }

    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.restoreState(this.history[this.historyIndex]);
            this.updateUndoRedoButtons();
        }
    }

    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.restoreState(this.history[this.historyIndex]);
            this.updateUndoRedoButtons();
        }
    }

    restoreState(state) {
        this.clearSelection();
        this.deserializeElements(state.elements);
        
        // Restore selection
        state.selectedElements.forEach(id => {
            this.selectElement(id, true);
        });
        
        this.updateLayerCounts();
        this.updatePropertyPanel();
    }

    // Element Grouping and Layering
    groupSelectedElements() {
        if (this.selectedElements.size < 2) return;

        this.saveState('Group elements');
        
        const groupId = 'group_' + Date.now();
        const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        group.setAttribute('data-element-id', groupId);
        group.setAttribute('data-element-type', 'group');
        
        const svg = document.getElementById('recreationSVG');
        const shapesLayer = svg.querySelector('.shapes-layer');
        
        // Move selected elements into the group
        this.selectedElements.forEach(id => {
            const element = this.getElementById(id);
            if (element) {
                group.appendChild(element);
            }
        });
        
        shapesLayer.appendChild(group);
        
        // Update selection to the group
        this.clearSelection();
        this.selectElement(groupId);
        
        this.updateLayerCounts();
    }

    ungroupSelectedElements() {
        if (this.selectedElements.size !== 1) return;
        
        const groupId = Array.from(this.selectedElements)[0];
        const group = this.getElementById(groupId);
        
        if (!group || group.tagName !== 'g') return;
        
        this.saveState('Ungroup elements');
        
        const svg = document.getElementById('recreationSVG');
        const shapesLayer = svg.querySelector('.shapes-layer');
        const newSelection = new Set();
        
        // Move children out of group
        while (group.firstChild) {
            const child = group.firstChild;
            shapesLayer.appendChild(child);
            if (child.dataset && child.dataset.elementId) {
                newSelection.add(child.dataset.elementId);
            }
        }
        
        // Remove the group
        group.remove();
        
        // Update selection
        this.clearSelection();
        newSelection.forEach(id => this.selectElement(id, true));
        
        this.updateLayerCounts();
    }

    // Layer Management
    setupLayerControls() {
        const layersList = document.getElementById('layersList');
        if (!layersList) return;

        layersList.addEventListener('click', (event) => {
            const layerItem = event.target.closest('.layer-item');
            if (!layerItem) return;

            const layerName = layerItem.dataset.layer;
            
            if (event.target.classList.contains('layer-visibility')) {
                this.toggleLayerVisibility(layerName);
            } else {
                this.setCurrentLayer(layerName);
            }
        });
    }

    toggleLayerVisibility(layerName) {
        const layer = this.layers.get(layerName);
        if (!layer) return;

        layer.visible = !layer.visible;
        
        const svg = document.getElementById('recreationSVG');
        const layerElement = svg.querySelector(`.${layerName}-layer`);
        
        if (layerElement) {
            layerElement.style.display = layer.visible ? 'block' : 'none';
        }
        
        // Update UI
        const layerItem = document.querySelector(`[data-layer="${layerName}"]`);
        const visibilityIcon = layerItem.querySelector('.layer-visibility');
        visibilityIcon.textContent = layer.visible ? '👁️' : '🙈';
    }

    setCurrentLayer(layerName) {
        this.currentLayer = layerName;
        
        // Update UI
        document.querySelectorAll('.layer-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-layer="${layerName}"]`).classList.add('active');
    }

    // Keyboard Shortcuts
    handleKeyDown(event) {
        // Prevent shortcuts when typing in inputs
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return;
        }

        const ctrl = event.ctrlKey || event.metaKey;
        
        switch (event.key) {
            case 'Delete':
            case 'Backspace':
                this.deleteSelectedElements();
                event.preventDefault();
                break;
                
            case 'z':
                if (ctrl && event.shiftKey) {
                    this.redo();
                } else if (ctrl) {
                    this.undo();
                }
                event.preventDefault();
                break;
                
            case 'y':
                if (ctrl) {
                    this.redo();
                    event.preventDefault();
                }
                break;
                
            case 'c':
                if (ctrl) {
                    this.copySelectedElements();
                    event.preventDefault();
                }
                break;
                
            case 'v':
                if (ctrl) {
                    this.pasteElements();
                    event.preventDefault();
                }
                break;
                
            case 'd':
                if (ctrl) {
                    this.duplicateSelectedElements();
                    event.preventDefault();
                }
                break;
                
            case 'a':
                if (ctrl) {
                    this.selectAllElements();
                    event.preventDefault();
                }
                break;
                
            case 'g':
                if (ctrl) {
                    this.groupSelectedElements();
                    event.preventDefault();
                }
                break;
                
            case 'Escape':
                this.clearSelection();
                break;
        }
    }

    // Utility Methods
    getElementById(id) {
        return document.querySelector(`[data-element-id="${id}"]`);
    }

    getElementData(element) {
        if (!element) return {};
        
        const type = element.dataset.elementType || element.tagName.toLowerCase();
        const data = { type };
        
        // Extract common properties
        switch (type) {
            case 'circle':
                data.x = parseFloat(element.getAttribute('cx')) || 0;
                data.y = parseFloat(element.getAttribute('cy')) || 0;
                data.radius = parseFloat(element.getAttribute('r')) || 0;
                data.width = data.radius * 2;
                data.height = data.radius * 2;
                break;
                
            case 'rect':
                data.x = parseFloat(element.getAttribute('x')) || 0;
                data.y = parseFloat(element.getAttribute('y')) || 0;
                data.width = parseFloat(element.getAttribute('width')) || 0;
                data.height = parseFloat(element.getAttribute('height')) || 0;
                break;
                
            case 'line':
                data.x = parseFloat(element.getAttribute('x1')) || 0;
                data.y = parseFloat(element.getAttribute('y1')) || 0;
                data.x2 = parseFloat(element.getAttribute('x2')) || 0;
                data.y2 = parseFloat(element.getAttribute('y2')) || 0;
                data.width = Math.abs(data.x2 - data.x);
                data.height = Math.abs(data.y2 - data.y);
                break;
                
            case 'text':
                data.x = parseFloat(element.getAttribute('x')) || 0;
                data.y = parseFloat(element.getAttribute('y')) || 0;
                data.text = element.textContent || '';
                data.fontSize = parseFloat(element.getAttribute('font-size')) || 16;
                data.fontFamily = element.getAttribute('font-family') || 'Arial';
                break;
        }
        
        // Extract style properties
        data.fill = element.getAttribute('fill') || '#000000';
        data.stroke = element.getAttribute('stroke') || 'none';
        data.strokeWidth = parseFloat(element.getAttribute('stroke-width')) || 0;
        data.opacity = parseFloat(element.getAttribute('opacity')) || 1;
        
        return data;
    }

    updateElementData(elementId, newData) {
        // Update internal element data structure
        const elementIndex = this.elements.findIndex(el => el.id === elementId);
        if (elementIndex !== -1) {
            this.elements[elementIndex] = { ...this.elements[elementIndex], ...newData };
        }
    }

    highlightElement(element) {
        element.classList.add('selected');
        element.style.filter = 'drop-shadow(0 0 3px #007bff)';
    }

    unhighlightElement(element) {
        element.classList.remove('selected');
        element.style.filter = '';
    }

    updateLayerCounts() {
        // Update layer counts in the UI
        this.layers.forEach((layer, name) => {
            const svg = document.getElementById('recreationSVG');
            const layerElement = svg.querySelector(`.${name}-layer`);
            const count = layerElement ? layerElement.children.length : 0;
            
            const layerItem = document.querySelector(`[data-layer="${name}"]`);
            if (layerItem) {
                const countElement = layerItem.querySelector('.layer-count');
                if (countElement) {
                    countElement.textContent = count;
                }
            }
        });
    }

    // Initialize toolbar with interactive tools
    initializeToolbar() {
        const canvasControls = document.querySelector('.canvas-controls');
        if (!canvasControls) return;

        const interactiveControls = document.createElement('div');
        interactiveControls.className = 'interactive-controls';
        interactiveControls.innerHTML = `
            <div class="tool-group">
                <button id="selectTool" class="tool-btn active" title="Select Tool (V)">
                    <span>🔍</span>
                </button>
                <button id="moveTool" class="tool-btn" title="Move Tool (M)">
                    <span>✋</span>
                </button>
                <button id="undoBtn" class="tool-btn" title="Undo (Ctrl+Z)">
                    <span>↶</span>
                </button>
                <button id="redoBtn" class="tool-btn" title="Redo (Ctrl+Y)">
                    <span>↷</span>
                </button>
            </div>
            <div class="snap-controls">
                <label class="snap-option">
                    <input type="checkbox" id="snapToGrid" checked>
                    <span>Snap to Grid</span>
                </label>
                <input type="number" id="gridSize" value="10" min="5" max="50" step="5">
            </div>
        `;

        canvasControls.appendChild(interactiveControls);

        // Setup toolbar event listeners
        document.getElementById('undoBtn')?.addEventListener('click', () => this.undo());
        document.getElementById('redoBtn')?.addEventListener('click', () => this.redo());
        document.getElementById('snapToGrid')?.addEventListener('change', (e) => {
            this.snapToGrid = e.target.checked;
        });
        document.getElementById('gridSize')?.addEventListener('change', (e) => {
            this.gridSize = parseInt(e.target.value);
        });
    }

    setupPropertyControls() {
        // Setup property panel event listeners
        document.getElementById('duplicateBtn')?.addEventListener('click', () => {
            this.duplicateSelectedElements();
        });
        
        document.getElementById('deleteBtn')?.addEventListener('click', () => {
            this.deleteSelectedElements();
        });
        
        document.getElementById('groupBtn')?.addEventListener('click', () => {
            this.groupSelectedElements();
        });
        
        document.getElementById('ungroupBtn')?.addEventListener('click', () => {
            this.ungroupSelectedElements();
        });

        // Property value change listeners
        ['propX', 'propY', 'propWidth', 'propHeight', 'propFill', 'propStroke', 
         'propStrokeWidth', 'propOpacity', 'propTextContent', 'propFontSize', 
         'propFontFamily', 'propLayer', 'propZIndex'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyPropertyChanges());
                element.addEventListener('input', () => this.applyPropertyChanges());
            }
        });
    }

    applyPropertyChanges() {
        if (this.selectedElements.size === 0) return;

        this.selectedElements.forEach(elementId => {
            const element = this.getElementById(elementId);
            if (!element) return;

            // Apply position changes
            const x = parseFloat(document.getElementById('propX').value);
            const y = parseFloat(document.getElementById('propY').value);
            if (!isNaN(x) && !isNaN(y)) {
                this.moveElement(element, x, y);
            }

            // Apply size changes
            const width = parseFloat(document.getElementById('propWidth').value);
            const height = parseFloat(document.getElementById('propHeight').value);
            if (!isNaN(width) && !isNaN(height)) {
                this.resizeElement(element, width, height);
            }

            // Apply style changes
            const fill = document.getElementById('propFill').value;
            const stroke = document.getElementById('propStroke').value;
            const strokeWidth = document.getElementById('propStrokeWidth').value;
            const opacity = document.getElementById('propOpacity').value;

            if (fill) element.setAttribute('fill', fill);
            if (stroke) element.setAttribute('stroke', stroke);
            if (strokeWidth) element.setAttribute('stroke-width', strokeWidth);
            if (opacity) element.setAttribute('opacity', opacity);

            // Update opacity display
            document.getElementById('opacityValue').textContent = opacity;

            // Apply text changes
            if (element.tagName === 'text') {
                const textContent = document.getElementById('propTextContent').value;
                const fontSize = document.getElementById('propFontSize').value;
                const fontFamily = document.getElementById('propFontFamily').value;

                if (textContent !== undefined) element.textContent = textContent;
                if (fontSize) element.setAttribute('font-size', fontSize);
                if (fontFamily) element.setAttribute('font-family', fontFamily);
            }
        });
    }

    hideAllPropertyGroups() {
        ['positionGroup', 'sizeGroup', 'styleGroup', 'textGroup', 'layerGroup'].forEach(id => {
            const group = document.getElementById(id);
            if (group) group.style.display = 'none';
        });
    }

    updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undoBtn');
        const redoBtn = document.getElementById('redoBtn');
        
        if (undoBtn) undoBtn.disabled = this.historyIndex <= 0;
        if (redoBtn) redoBtn.disabled = this.historyIndex >= this.history.length - 1;
    }

    updateSelectionInfo() {
        const statusMessage = document.getElementById('statusMessage');
        if (statusMessage) {
            if (this.selectedElements.size === 0) {
                statusMessage.textContent = 'No elements selected';
            } else if (this.selectedElements.size === 1) {
                statusMessage.textContent = '1 element selected';
            } else {
                statusMessage.textContent = `${this.selectedElements.size} elements selected`;
            }
        }
    }

    // Additional utility methods for copy/paste, serialization, etc.
    copySelectedElements() {
        this.clipboard = [];
        this.selectedElements.forEach(id => {
            const element = this.getElementById(id);
            if (element) {
                this.clipboard.push(this.getElementData(element));
            }
        });
    }

    pasteElements() {
        if (this.clipboard.length === 0) return;

        this.saveState('Paste elements');
        this.clearSelection();

        this.clipboard.forEach(elementData => {
            const newElement = this.createElement(elementData);
            // Offset pasted elements
            newElement.x = (elementData.x || 0) + 20;
            newElement.y = (elementData.y || 0) + 20;
            this.addElementToCanvas(newElement);
            this.selectElement(newElement.id, true);
        });
    }

    selectAllElements() {
        this.clearSelection();
        const svg = document.getElementById('recreationSVG');
        const elements = svg.querySelectorAll('[data-element-id]');
        
        elements.forEach(element => {
            this.selectElement(element.dataset.elementId, true);
        });
    }

    serializeElements() {
        // Serialize current state for undo/redo
        const svg = document.getElementById('recreationSVG');
        return svg.innerHTML;
    }

    deserializeElements(serializedData) {
        // Restore state from serialized data
        const svg = document.getElementById('recreationSVG');
        svg.innerHTML = serializedData;
    }

    // Context menu implementation
    handleContextMenu(event) {
        event.preventDefault();
        
        const contextMenu = this.createContextMenu();
        contextMenu.style.left = event.pageX + 'px';
        contextMenu.style.top = event.pageY + 'px';
        
        document.body.appendChild(contextMenu);
        
        // Remove context menu when clicking elsewhere
        const removeMenu = () => {
            contextMenu.remove();
            document.removeEventListener('click', removeMenu);
        };
        
        setTimeout(() => {
            document.addEventListener('click', removeMenu);
        }, 0);
    }

    createContextMenu() {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.innerHTML = `
            <div class="context-item" data-action="copy">Copy</div>
            <div class="context-item" data-action="paste">Paste</div>
            <div class="context-item" data-action="duplicate">Duplicate</div>
            <div class="context-separator"></div>
            <div class="context-item" data-action="group">Group</div>
            <div class="context-item" data-action="ungroup">Ungroup</div>
            <div class="context-separator"></div>
            <div class="context-item" data-action="delete">Delete</div>
        `;

        menu.addEventListener('click', (event) => {
            const action = event.target.dataset.action;
            switch (action) {
                case 'copy': this.copySelectedElements(); break;
                case 'paste': this.pasteElements(); break;
                case 'duplicate': this.duplicateSelectedElements(); break;
                case 'group': this.groupSelectedElements(); break;
                case 'ungroup': this.ungroupSelectedElements(); break;
                case 'delete': this.deleteSelectedElements(); break;
            }
            menu.remove();
        });

        return menu;
    }

    // Handle click events for element selection
    handleClick(event) {
        const target = event.target.closest('[data-element-id]');
        
        if (target) {
            const elementId = target.dataset.elementId;
            this.selectElement(elementId, event.ctrlKey || event.metaKey);
        } else if (!event.ctrlKey && !event.metaKey) {
            this.clearSelection();
        }
    }

    // Handle wheel events for zooming
    handleWheel(event) {
        if (event.ctrlKey) {
            event.preventDefault();
            
            const delta = event.deltaY > 0 ? 0.9 : 1.1;
            this.zoom = Math.max(0.1, Math.min(5, this.zoom * delta));
            
            const svg = document.getElementById('recreationSVG');
            svg.style.transform = `scale(${this.zoom})`;
            
            // Update zoom display
            const zoomLevel = document.getElementById('zoomLevel');
            if (zoomLevel) {
                zoomLevel.textContent = Math.round(this.zoom * 100) + '%';
            }
        }
    }

    // Load elements from recreation data
    loadElements(elements) {
        this.elements = elements || [];
        this.clearSelection();
        this.updateLayerCounts();
        this.saveState('Load elements');
    }

    // Get current editor state
    getState() {
        return {
            elements: this.elements,
            selectedElements: Array.from(this.selectedElements),
            zoom: this.zoom,
            pan: this.pan,
            currentLayer: this.currentLayer,
            snapToGrid: this.snapToGrid,
            gridSize: this.gridSize
        };
    }
}

export default InteractiveEditor;