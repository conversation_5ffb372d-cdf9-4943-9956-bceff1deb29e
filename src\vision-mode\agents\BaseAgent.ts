/**
 * Base Agent Interface and Abstract Implementation
 * Provides the foundational structure for all MCP Vision Mode agents
 */

import { <PERSON>rowserEventEmitter } from '../utils/BrowserEventEmitter';
import {
  AgentId,
  TaskId,
  MessageId,
  AgentMetadata,
  AgentTask,
  AgentMessage,
  AgentConfig,
  AgentContext,
  AgentMetrics,
  AgentStatus,
  AgentCapability,
  TaskStatus,
  MessageType,
  AgentEvent,
  AgentEventType,
  AgentHealthCheck
} from './types.js';

/**
 * Core interface that all agents must implement
 */
export interface IAgent {
  readonly id: AgentId;
  readonly metadata: AgentMetadata;
  readonly metrics: AgentMetrics;

  // Lifecycle methods
  initialize(): Promise<void>;
  start(): Promise<void>;
  stop(): Promise<void>;
  shutdown(): Promise<void>;

  // Task execution
  executeTask(task: AgentTask, context: AgentContext): Promise<any>;
  canExecuteTask(task: AgentTask): boolean;
  cancelTask(taskId: TaskId): Promise<boolean>;

  // Communication
  sendMessage(message: AgentMessage): Promise<void>;
  receiveMessage(message: AgentMessage): Promise<void>;
  broadcast(payload: Record<string, any>, type: MessageType): Promise<void>;

  // Health and monitoring
  getHealthStatus(): Promise<AgentHealthCheck>;
  updateStatus(status: AgentStatus): void;
  getMetrics(): AgentMetrics;

  // Event handling
  on(event: string, listener: (...args: any[]) => void): this;
  emit(event: string, ...args: any[]): boolean;
}

/**
 * Abstract base class providing common agent functionality
 */
export abstract class BaseAgent extends BrowserEventEmitter implements IAgent {
  protected _metadata: AgentMetadata;
  protected _metrics: AgentMetrics;
  protected _config: AgentConfig;
  protected _activeTasks: Map<TaskId, AgentTask> = new Map();
  protected _messageHandlers: Map<MessageType, (message: AgentMessage) => Promise<void>> = new Map();
  protected _heartbeatInterval?: NodeJS.Timeout;
  protected _isInitialized = false;
  protected _isRunning = false;

  constructor(config: AgentConfig) {
    super();
    this._config = config;
    this._metadata = {
      id: config.id,
      name: config.name,
      version: '1.0.0',
      description: '',
      capabilities: config.capabilities,
      status: AgentStatus.INITIALIZING,
      lastHeartbeat: new Date(),
      maxConcurrentTasks: config.maxConcurrentTasks || 5,
      currentTaskCount: 0,
      tags: []
    };
    this._metrics = {
      tasksCompleted: 0,
      tasksFailedCount: 0,
      averageExecutionTime: 0,
      lastExecutionTime: 0,
      uptime: 0,
      memoryUsage: 0,
      cpuUsage: 0
    };

    this.setupMessageHandlers();
  }

  get id(): AgentId {
    return this._metadata.id;
  }

  get metadata(): AgentMetadata {
    return { ...this._metadata };
  }

  get metrics(): AgentMetrics {
    return { ...this._metrics };
  }

  /**
   * Initialize the agent - must be called before start()
   */
  async initialize(): Promise<void> {
    if (this._isInitialized) {
      throw new Error(`Agent ${this.id} is already initialized`);
    }

    try {
      await this.onInitialize();
      this._isInitialized = true;
      this.updateStatus(AgentStatus.READY);
      this.emitEvent(AgentEventType.AGENT_REGISTERED, { agentId: this.id });
    } catch (error) {
      this.updateStatus(AgentStatus.ERROR);
      throw new Error(`Failed to initialize agent ${this.id}: ${error}`);
    }
  }

  /**
   * Start the agent and begin processing
   */
  async start(): Promise<void> {
    if (!this._isInitialized) {
      throw new Error(`Agent ${this.id} must be initialized before starting`);
    }

    if (this._isRunning) {
      return;
    }

    try {
      await this.onStart();
      this._isRunning = true;
      this.startHeartbeat();
      this.updateStatus(AgentStatus.READY);
    } catch (error) {
      this.updateStatus(AgentStatus.ERROR);
      throw new Error(`Failed to start agent ${this.id}: ${error}`);
    }
  }

  /**
   * Stop the agent gracefully
   */
  async stop(): Promise<void> {
    if (!this._isRunning) {
      return;
    }

    try {
      // Cancel all active tasks
      const cancelPromises = Array.from(this._activeTasks.keys()).map(taskId => 
        this.cancelTask(taskId)
      );
      await Promise.all(cancelPromises);

      await this.onStop();
      this.stopHeartbeat();
      this._isRunning = false;
      this.updateStatus(AgentStatus.OFFLINE);
    } catch (error) {
      this.updateStatus(AgentStatus.ERROR);
      throw new Error(`Failed to stop agent ${this.id}: ${error}`);
    }
  }

  /**
   * Shutdown the agent completely
   */
  async shutdown(): Promise<void> {
    await this.stop();
    await this.onShutdown();
    this.emitEvent(AgentEventType.AGENT_UNREGISTERED, { agentId: this.id });
    this.removeAllListeners();
  }

  /**
   * Execute a task with proper error handling and metrics
   */
  async executeTask(task: AgentTask, context: AgentContext): Promise<any> {
    if (!this.canExecuteTask(task)) {
      throw new Error(`Agent ${this.id} cannot execute task ${task.id}`);
    }

    if (this._activeTasks.size >= this._metadata.maxConcurrentTasks) {
      throw new Error(`Agent ${this.id} has reached maximum concurrent task limit`);
    }

    const startTime = Date.now();
    this._activeTasks.set(task.id, task);
    this._metadata.currentTaskCount++;
    this.updateStatus(AgentStatus.BUSY);

    this.emitEvent(AgentEventType.TASK_STARTED, {
      agentId: this.id,
      taskId: task.id,
      taskType: task.type
    });

    try {
      const result = await this.onExecuteTask(task, context);
      const executionTime = Date.now() - startTime;
      
      this.updateMetrics(executionTime, true);
      this._activeTasks.delete(task.id);
      this._metadata.currentTaskCount--;
      
      if (this._activeTasks.size === 0) {
        this.updateStatus(AgentStatus.READY);
      }

      this.emitEvent(AgentEventType.TASK_COMPLETED, {
        agentId: this.id,
        taskId: task.id,
        executionTime,
        result
      });

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateMetrics(executionTime, false);
      this._activeTasks.delete(task.id);
      this._metadata.currentTaskCount--;
      
      if (this._activeTasks.size === 0) {
        this.updateStatus(AgentStatus.READY);
      }

      this.emitEvent(AgentEventType.TASK_FAILED, {
        agentId: this.id,
        taskId: task.id,
        error: error instanceof Error ? error.message : String(error)
      });

      throw error;
    }
  }

  /**
   * Check if agent can execute a specific task
   */
  canExecuteTask(task: AgentTask): boolean {
    return this._metadata.status === AgentStatus.READY || 
           this._metadata.status === AgentStatus.BUSY;
  }

  /**
   * Cancel a running task
   */
  async cancelTask(taskId: TaskId): Promise<boolean> {
    const task = this._activeTasks.get(taskId);
    if (!task) {
      return false;
    }

    try {
      await this.onCancelTask(taskId);
      this._activeTasks.delete(taskId);
      this._metadata.currentTaskCount--;
      return true;
    } catch (error) {
      console.error(`Failed to cancel task ${taskId}:`, error);
      return false;
    }
  }

  /**
   * Send a message to another agent
   */
  async sendMessage(message: AgentMessage): Promise<void> {
    this.emitEvent(AgentEventType.MESSAGE_SENT, {
      agentId: this.id,
      messageId: message.id,
      receiverId: message.receiverId
    });
    await this.onSendMessage(message);
  }

  /**
   * Receive and process a message
   */
  async receiveMessage(message: AgentMessage): Promise<void> {
    this.emitEvent(AgentEventType.MESSAGE_RECEIVED, {
      agentId: this.id,
      messageId: message.id,
      senderId: message.senderId
    });

    const handler = this._messageHandlers.get(message.type);
    if (handler) {
      try {
        await handler(message);
      } catch (error) {
        console.error(`Error handling message ${message.id}:`, error);
        this.emitEvent(AgentEventType.ERROR_OCCURRED, {
          agentId: this.id,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  /**
   * Broadcast a message to all agents
   */
  async broadcast(payload: Record<string, any>, type: MessageType): Promise<void> {
    const message: AgentMessage = {
      id: this.generateMessageId(),
      type,
      senderId: this.id,
      receiverId: 'broadcast',
      payload,
      timestamp: new Date()
    };
    await this.sendMessage(message);
  }

  /**
   * Get current health status
   */
  async getHealthStatus(): Promise<AgentHealthCheck> {
    const now = new Date();
    return {
      isHealthy: this._metadata.status !== AgentStatus.ERROR,
      lastCheck: now,
      responseTime: 0, // Will be measured by caller
      errorCount: this._metrics.tasksFailedCount,
      details: {
        status: this._metadata.status,
        activeTasks: this._activeTasks.size,
        uptime: this._metrics.uptime
      }
    };
  }

  /**
   * Update agent status
   */
  updateStatus(status: AgentStatus): void {
    const oldStatus = this._metadata.status;
    this._metadata.status = status;
    this._metadata.lastHeartbeat = new Date();
    
    if (oldStatus !== status) {
      this.emitEvent(AgentEventType.AGENT_STATUS_CHANGED, {
        agentId: this.id,
        oldStatus,
        newStatus: status
      });
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): AgentMetrics {
    return { ...this._metrics };
  }

  // Protected methods for subclasses to override
  protected abstract onInitialize(): Promise<void>;
  protected abstract onStart(): Promise<void>;
  protected abstract onStop(): Promise<void>;
  protected abstract onShutdown(): Promise<void>;
  protected abstract onExecuteTask(task: AgentTask, context: AgentContext): Promise<any>;
  protected abstract onCancelTask(taskId: TaskId): Promise<void>;
  protected abstract onSendMessage(message: AgentMessage): Promise<void>;

  // Protected utility methods
  protected setupMessageHandlers(): void {
    // Default message handlers - can be overridden
    this._messageHandlers.set(MessageType.HEARTBEAT, this.handleHeartbeat.bind(this));
    this._messageHandlers.set(MessageType.SHUTDOWN, this.handleShutdown.bind(this));
  }

  protected async handleHeartbeat(message: AgentMessage): Promise<void> {
    // Respond to heartbeat
    const response: AgentMessage = {
      id: this.generateMessageId(),
      type: MessageType.STATUS_UPDATE,
      senderId: this.id,
      receiverId: message.senderId,
      payload: { status: this._metadata.status, metrics: this._metrics },
      timestamp: new Date(),
      replyTo: message.id
    };
    await this.sendMessage(response);
  }

  protected async handleShutdown(message: AgentMessage): Promise<void> {
    await this.shutdown();
  }

  protected startHeartbeat(): void {
    const interval = this._config.heartbeatInterval || 30000; // 30 seconds default
    this._heartbeatInterval = setInterval(() => {
      this._metadata.lastHeartbeat = new Date();
      this._metrics.uptime += interval;
    }, interval);
  }

  protected stopHeartbeat(): void {
    if (this._heartbeatInterval) {
      clearInterval(this._heartbeatInterval);
      this._heartbeatInterval = undefined;
    }
  }

  protected updateMetrics(executionTime: number, success: boolean): void {
    if (success) {
      this._metrics.tasksCompleted++;
    } else {
      this._metrics.tasksFailedCount++;
    }
    
    this._metrics.lastExecutionTime = executionTime;
    this._metrics.averageExecutionTime = 
      (this._metrics.averageExecutionTime * (this._metrics.tasksCompleted + this._metrics.tasksFailedCount - 1) + executionTime) /
      (this._metrics.tasksCompleted + this._metrics.tasksFailedCount);
  }

  protected emitEvent(type: AgentEventType, data: Record<string, any>): void {
    const event: AgentEvent = {
      type,
      agentId: this.id,
      timestamp: new Date(),
      data
    };
    this.emit('agent-event', event);
  }

  protected generateMessageId(): MessageId {
    return `${this.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}