import { Link } from 'react-router-dom';
import { Eye, FileText, Zap, BarChart3, Settings, Download, Upload, CheckCircle, AlertCircle } from 'lucide-react';
import { useRef, useState } from 'react';
import { pdfProcessingService, PDFProcessingResult } from '../services/PDFProcessingService';
import { semanticAnalysisService, SemanticAnalysisResult } from '../services/SemanticAnalysisService';
import { dataStore, ProcessedDocument } from '../services/DataStoreService';

interface ProcessingResult {
  pdfResult: PDFProcessingResult;
  semanticResult: SemanticAnalysisResult;
}

export default function Home() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingResult, setProcessingResult] = useState<ProcessingResult | null>(null);
  const [processingStep, setProcessingStep] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const handleStartPDFProcessing = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && isValidPDFFile(file)) {
      setSelectedFile(file);
      setError(null);
      processPDF(file);
    } else {
      setError('Please select a valid PDF file. Only .pdf files are supported.');
    }
  };

  const isValidPDFFile = (file: File): boolean => {
    // Check MIME type first
    if (file.type === 'application/pdf') {
      return true;
    }
    
    // Fallback to file extension check
    const fileName = file.name.toLowerCase();
    if (fileName.endsWith('.pdf')) {
      return true;
    }
    
    // Check for common PDF MIME type variations
    const validMimeTypes = [
      'application/pdf',
      'application/x-pdf',
      'application/acrobat',
      'applications/vnd.pdf',
      'text/pdf',
      'text/x-pdf'
    ];
    
    return validMimeTypes.includes(file.type);
  };

  const processPDF = async (file: File) => {
    setIsProcessing(true);
    setProcessingStep('Validating PDF...');
    setError(null);
    const startTime = Date.now();
    
    try {
      // Validate PDF
      const validationResult = await pdfProcessingService.validatePDF(file);
      if (!validationResult.isValid) {
        throw new Error(validationResult.error || 'Invalid PDF file format');
      }

      // Process PDF
      setProcessingStep('Extracting text and metadata...');
      const pdfResult = await pdfProcessingService.processPDF(file);
      
      // Perform semantic analysis
      setProcessingStep('Analyzing content...');
      const semanticResult = await semanticAnalysisService.analyzeContent(pdfResult);
      
      // Create processed document and store it
      const processedDocument: ProcessedDocument = {
        id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        filename: file.name,
        uploadedAt: new Date(),
        pdfResult,
        semanticResult,
        fileSize: file.size,
        processingDuration: Date.now() - startTime
      };

      // Store in data store for other components to access
      dataStore.addDocument(processedDocument);
      
      // Store results
      const result: ProcessingResult = { pdfResult, semanticResult };
      setProcessingResult(result);
      
      // Store in localStorage for Vision Mode access
      localStorage.setItem('lastProcessedPDF', JSON.stringify({
        fileName: file.name,
        timestamp: new Date().toISOString(),
        ...result
      }));
      
      setProcessingStep('Processing complete!');
    } catch (error) {
      console.error('Error processing PDF:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
    }
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <div className="container mx-auto px-6 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-white mb-4">
            PDF Recreation & Vision Mode
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Advanced document processing with intelligent multi-agent analysis and automation
          </p>
        </div>

        {/* Main Navigation Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {/* PDF Recreation */}
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-8 hover:border-blue-500/50 transition-all duration-300">
            <div className="flex items-center space-x-4 mb-6">
              <div className="p-3 bg-blue-600 rounded-lg">
                <FileText className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">PDF Recreation</h2>
                <p className="text-gray-400">Traditional document processing</p>
              </div>
            </div>
            <p className="text-gray-300 mb-6">
              Upload, process, and recreate PDF documents with standard tools and workflows.
            </p>
            <input
              ref={fileInputRef}
              type="file"
              accept=".pdf"
              onChange={handleFileSelect}
              className="hidden"
            />
            <button 
              onClick={handleStartPDFProcessing}
              disabled={isProcessing}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <Upload className="w-5 h-5" />
                  <span>Start PDF Processing</span>
                </>
              )}
            </button>
            
            {/* Processing Status */}
            {processingStep && (
              <div className="mt-3 p-3 bg-blue-900/30 border border-blue-500/30 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
                  <span className="text-blue-300 text-sm">{processingStep}</span>
                </div>
              </div>
            )}
            
            {/* Error Display */}
            {error && (
              <div className="mt-3 p-3 bg-red-900/30 border border-red-500/30 rounded-lg">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="w-4 h-4 text-red-400" />
                  <span className="text-red-300 text-sm">{error}</span>
                </div>
              </div>
            )}
            
            {/* Success Display */}
            {processingResult && (
              <div className="mt-3 p-4 bg-green-900/30 border border-green-500/30 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-green-300 text-sm font-medium">Processing Complete!</span>
                </div>
                <div className="text-xs text-gray-400 space-y-1">
                  <div>Pages: {processingResult.pdfResult.metadata.pageCount}</div>
                  <div>Words: {processingResult.pdfResult.wordCount.toLocaleString()}</div>
                  <div>Topics: {processingResult.semanticResult.topics.length}</div>
                  <div>Entities: {processingResult.semanticResult.entities.length}</div>
                  <div>Language: {processingResult.semanticResult.language}</div>
                  <div>Sentiment: {processingResult.semanticResult.sentiment.overall}</div>
                </div>
                <Link 
                  to="/vision-mode" 
                  className="inline-block mt-3 text-blue-400 hover:text-blue-300 text-sm underline"
                >
                  View in Vision Mode →
                </Link>
              </div>
            )}
            
            {selectedFile && !processingResult && !error && (
              <p className="text-sm text-gray-400 mt-2">
                Selected: {selectedFile.name}
              </p>
            )}
          </div>

          {/* Vision Mode */}
          <Link to="/vision-mode" className="block">
            <div className="bg-gradient-to-br from-purple-900/50 to-blue-900/50 rounded-xl border border-purple-500/30 p-8 hover:border-purple-400 transition-all duration-300 h-full">
              <div className="flex items-center space-x-4 mb-6">
                <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
                  <Eye className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">Vision Mode</h2>
                  <p className="text-purple-300">Intelligent multi-agent system</p>
                </div>
              </div>
              <p className="text-gray-300 mb-6">
                Advanced document analysis with AI agents, semantic understanding, and automated workflows.
              </p>
              <div className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-all text-center">
                Enter Vision Mode
              </div>
            </div>
          </Link>
        </div>

        {/* Vision Mode Features */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-white text-center mb-12">
            Vision Mode Capabilities
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6 text-center">
              <div className="p-3 bg-green-600 rounded-lg w-fit mx-auto mb-4">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">Real-time Dashboard</h4>
              <p className="text-gray-400 text-sm">
                Monitor system performance and agent activities in real-time
              </p>
            </div>
            
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6 text-center">
              <div className="p-3 bg-blue-600 rounded-lg w-fit mx-auto mb-4">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">Agent Control</h4>
              <p className="text-gray-400 text-sm">
                Manage MCP agents and configure automated workflows
              </p>
            </div>
            
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6 text-center">
              <div className="p-3 bg-purple-600 rounded-lg w-fit mx-auto mb-4">
                <Eye className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">Visualization</h4>
              <p className="text-gray-400 text-sm">
                Interactive knowledge graphs and semantic analysis
              </p>
            </div>
            
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-6 text-center">
              <div className="p-3 bg-orange-600 rounded-lg w-fit mx-auto mb-4">
                <Download className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">Export Hub</h4>
              <p className="text-gray-400 text-sm">
                Download reports and analysis in multiple formats
              </p>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-8">Quick Actions</h3>
          <div className="flex flex-wrap justify-center gap-4">
            <Link 
              to="/vision-mode" 
              className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              <Eye className="w-5 h-5" />
              <span>Launch Vision Mode</span>
            </Link>
            <button 
              onClick={handleStartPDFProcessing}
              disabled={isProcessing}
              className="flex items-center space-x-2 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-500 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              <FileText className="w-5 h-5" />
              <span>Upload Document</span>
            </button>
            <button className="flex items-center space-x-2 bg-slate-700 hover:bg-slate-600 text-white font-medium py-3 px-6 rounded-lg transition-colors">
              <Settings className="w-5 h-5" />
              <span>Settings</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}