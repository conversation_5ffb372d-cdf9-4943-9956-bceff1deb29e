/**
 * Test Setup Configuration for Vision Mode Integration Tests
 */

import { beforeAll, afterAll, vi, expect } from 'vitest';
import '@testing-library/jest-dom';

// Global test setup
beforeAll(() => {
  // Mock browser APIs
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock WebSocket for real-time features
  const MockWebSocket = vi.fn().mockImplementation(() => ({
    send: vi.fn(),
    close: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    readyState: 1, // OPEN
  }));
  Object.assign(MockWebSocket, {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
  });
  global.WebSocket = MockWebSocket as any;

  // Mock File API
  global.File = class MockFile {
    name: string;
    size: number;
    type: string;
    lastModified: number;
    
    constructor(bits: any[], filename: string, options: any = {}) {
      this.name = filename;
      this.size = bits.reduce((acc, bit) => acc + (bit.length || 0), 0);
      this.type = options.type || '';
      this.lastModified = options.lastModified || Date.now();
    }
  } as any;

  // Mock FileReader
  const MockFileReader = vi.fn().mockImplementation(() => ({
    readAsText: vi.fn(),
    readAsDataURL: vi.fn(),
    readAsArrayBuffer: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    result: null,
    error: null,
    readyState: 0,
  }));
  Object.assign(MockFileReader, {
    EMPTY: 0,
    LOADING: 1,
    DONE: 2
  });
  global.FileReader = MockFileReader as any;

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });

  // Mock sessionStorage
  Object.defineProperty(window, 'sessionStorage', {
    value: localStorageMock,
  });

  // Mock URL.createObjectURL
  global.URL.createObjectURL = vi.fn(() => 'mock-object-url');
  global.URL.revokeObjectURL = vi.fn();

  // Mock fetch for API calls
  global.fetch = vi.fn();

  // Mock console methods to reduce noise in tests
  vi.spyOn(console, 'log').mockImplementation(() => {});
  vi.spyOn(console, 'warn').mockImplementation(() => {});
  vi.spyOn(console, 'error').mockImplementation(() => {});

  // Set up test environment variables
  process.env.NODE_ENV = 'test';
  process.env.VITE_API_URL = 'http://localhost:3000';
  process.env.VITE_WS_URL = 'ws://localhost:3000';
});

afterAll(() => {
  // Cleanup
  vi.restoreAllMocks();
});

// Custom matchers for Vision Mode specific assertions
expect.extend({
  toBeValidVisionModeAgent(received) {
    const pass = received && 
                 typeof received.id === 'string' &&
                 typeof received.name === 'string' &&
                 Array.isArray(received.capabilities) &&
                 typeof received.initialize === 'function' &&
                 typeof received.processRequest === 'function' &&
                 typeof received.getStatus === 'function';

    return {
      message: () => 
        pass 
          ? `Expected ${received} not to be a valid Vision Mode agent`
          : `Expected ${received} to be a valid Vision Mode agent with required properties`,
      pass,
    };
  },

  toHaveValidAnalysisResult(received) {
    const pass = received &&
                 Array.isArray(received.entities) &&
                 Array.isArray(received.relationships) &&
                 Array.isArray(received.concepts) &&
                 typeof received.summary === 'string' &&
                 typeof received.confidence === 'number' &&
                 received.confidence >= 0 &&
                 received.confidence <= 1;

    return {
      message: () =>
        pass
          ? `Expected ${received} not to be a valid analysis result`
          : `Expected ${received} to be a valid analysis result with entities, relationships, concepts, summary, and confidence`,
      pass,
    };
  },

  toHaveValidBehavioralData(received) {
    const pass = received &&
                 Array.isArray(received.userSessions) &&
                 Array.isArray(received.interactionPatterns) &&
                 Array.isArray(received.performanceMetrics) &&
                 received.aggregatedResults &&
                 typeof received.aggregatedResults.totalSessions === 'number';

    return {
      message: () =>
        pass
          ? `Expected ${received} not to be valid behavioral data`
          : `Expected ${received} to be valid behavioral data with required arrays and aggregated results`,
      pass,
    };
  },

  toBeWithinPerformanceThreshold(received, threshold) {
    const pass = typeof received === 'number' && received <= threshold;

    return {
      message: () =>
        pass
          ? `Expected ${received} to exceed performance threshold of ${threshold}ms`
          : `Expected ${received} to be within performance threshold of ${threshold}ms`,
      pass,
    };
  }
});

// Type declarations for custom matchers
declare global {
  namespace Vi {
    interface AsymmetricMatchersContaining {
      toBeValidVisionModeAgent(): any;
      toHaveValidAnalysisResult(): any;
      toHaveValidBehavioralData(): any;
      toBeWithinPerformanceThreshold(threshold: number): any;
    }
  }
}

// Export test utilities
export const createMockVisionAgent = (overrides = {}) => ({
  id: 'mock-agent',
  name: 'Mock Vision Agent',
  capabilities: ['semantic-analysis', 'behavioral-analysis'],
  initialize: vi.fn().mockResolvedValue(undefined),
  processRequest: vi.fn().mockResolvedValue({ success: true }),
  getStatus: vi.fn().mockReturnValue({
    status: 'active',
    health: 'healthy',
    lastActivity: new Date(),
    metrics: {
      requestsProcessed: 100,
      averageResponseTime: 250,
      errorRate: 0.02
    }
  }),
  ...overrides
});

export const createMockAnalysisResult = (overrides = {}) => ({
  entities: [{ text: 'test entity', type: 'PERSON', confidence: 0.95 }],
  relationships: [{ source: 'entity1', target: 'entity2', type: 'RELATED_TO' }],
  concepts: [{ name: 'test concept', confidence: 0.9, category: 'GENERAL' }],
  summary: 'Test document analysis',
  confidence: 0.92,
  ...overrides
});

export const createMockBehavioralData = (overrides = {}) => ({
  userSessions: [{ id: '1', duration: 300, interactions: 10 }],
  interactionPatterns: [{ type: 'click', frequency: 50 }],
  performanceMetrics: [{ metric: 'response_time', value: 200 }],
  userJourneys: [],
  goals: [],
  conversionData: [],
  engagementMetrics: [],
  satisfactionScores: [],
  aggregatedResults: {
    totalSessions: 1,
    averageSessionDuration: 300,
    bounceRate: 0.1,
    conversionRate: 0.05,
    userSatisfaction: 0.8,
    performanceScore: 0.9
  },
  ...overrides
});

export const waitForAsyncOperations = () => new Promise(resolve => setTimeout(resolve, 0));

export const simulateFileUpload = (filename = 'test.pdf', content = 'test content', type = 'application/pdf') => {
  return new File([content], filename, { type });
};

export const mockWebSocketConnection = () => {
  const mockWs = {
    send: vi.fn(),
    close: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    readyState: 1,
    onopen: null,
    onclose: null,
    onmessage: null,
    onerror: null
  };

  return mockWs;
};