/**
 * Export Panel Component
 * Interface for downloading reports, documentation, and analysis results
 */

import React, { useState, useEffect } from 'react';
import {
  Download,
  FileText,
  Image,
  Database,
  Archive,
  Calendar,
  Filter,
  Search,
  CheckCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  Eye,
  Trash2,
  Share2,
  Settings,
  BarChart3,
  Brain,
  Users,
  Network,
  FileJson,
  FileSpreadsheet,
  FileImage,
  Square
} from 'lucide-react';
import { exportService, ExportRequest } from '../../../services/ExportService';
import { dataStore } from '../../../services/DataStoreService';
// Temporarily commented out to fix compilation issues
// import { VisionModeAgent } from '../../VisionModeAgent';

// Mock type definition
interface VisionModeAgent {
  initialize(): Promise<void>;
  getStatus(): any;
}

/**
 * Export panel props
 */
export interface ExportPanelProps {
  visionAgent: VisionModeAgent | null;
  systemStatus: any;
  onStatusUpdate?: (status: any) => void;
  className?: string;
}

/**
 * Export format
 */
export type ExportFormat = 'pdf' | 'json' | 'csv' | 'xlsx' | 'png' | 'svg' | 'html' | 'md';

/**
 * Export type
 */
export type ExportType = 
  | 'semantic-analysis'
  | 'knowledge-graph'
  | 'behavioral-patterns'
  | 'agent-reports'
  | 'system-metrics'
  | 'workflow-logs'
  | 'full-report'
  | 'custom';

/**
 * Export job
 */
export interface ExportJob {
  id: string;
  name: string;
  type: ExportType;
  format: ExportFormat;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  size?: number;
  downloadUrl?: string;
  createdAt: Date;
  completedAt?: Date;
  error?: string;
  metadata: {
    dateRange?: { start: Date; end: Date };
    filters?: Record<string, any>;
    includeCharts?: boolean;
    includeRawData?: boolean;
    compression?: boolean;
  };
}

/**
 * Export template
 */
export interface ExportTemplate {
  id: string;
  name: string;
  description: string;
  type: ExportType;
  format: ExportFormat;
  defaultSettings: {
    includeCharts: boolean;
    includeRawData: boolean;
    dateRange: 'last7days' | 'last30days' | 'last90days' | 'custom';
    filters: Record<string, any>;
  };
  isCustom: boolean;
  createdAt: Date;
}

/**
 * Export Panel Component
 */
export const ExportPanel: React.FC<ExportPanelProps> = ({
  visionAgent,
  systemStatus,
  onStatusUpdate,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'create' | 'jobs' | 'templates'>('create');
  const [exportJobs, setExportJobs] = useState<ExportJob[]>([]);
  const [templates, setTemplates] = useState<ExportTemplate[]>([]);
  const [hasData, setHasData] = useState(false);
  const [selectedType, setSelectedType] = useState<ExportType>('semantic-analysis');
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('pdf');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [exportSettings, setExportSettings] = useState({
    includeCharts: true,
    includeRawData: false,
    compression: false,
    dateRange: 'last30days' as 'last7days' | 'last30days' | 'last90days' | 'custom',
    customDateStart: '',
    customDateEnd: '',
    filters: {} as Record<string, any>
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Check if we have processed documents
  useEffect(() => {
    const checkData = () => {
      const documents = dataStore.getAllDocuments();
      setHasData(documents.length > 0);
    };
    
    checkData();
    const unsubscribe = dataStore.subscribe(checkData);
    return unsubscribe;
  }, []);

  // Subscribe to export job updates
  useEffect(() => {
    const unsubscribe = exportService.subscribe((jobs) => {
      setExportJobs(jobs);
    });
    
    // Load initial jobs
    setExportJobs(exportService.getAllJobs());
    
    return unsubscribe;
  }, []);



  // Initialize mock templates
  useEffect(() => {
    const mockTemplates: ExportTemplate[] = [
      {
        id: 'template-1',
        name: 'Monthly Semantic Report',
        description: 'Comprehensive semantic analysis report with charts and insights',
        type: 'semantic-analysis',
        format: 'pdf',
        defaultSettings: {
          includeCharts: true,
          includeRawData: false,
          dateRange: 'last30days',
          filters: { minConfidence: 0.8 }
        },
        isCustom: false,
        createdAt: new Date(Date.now() - 86400000 * 7)
      },
      {
        id: 'template-2',
        name: 'Knowledge Graph Backup',
        description: 'Complete knowledge graph data export for backup purposes',
        type: 'knowledge-graph',
        format: 'json',
        defaultSettings: {
          includeCharts: false,
          includeRawData: true,
          dateRange: 'last90days',
          filters: {}
        },
        isCustom: false,
        createdAt: new Date(Date.now() - 86400000 * 14)
      },
      {
        id: 'template-3',
        name: 'User Behavior Dashboard',
        description: 'Interactive HTML dashboard showing user behavior patterns',
        type: 'behavioral-patterns',
        format: 'html',
        defaultSettings: {
          includeCharts: true,
          includeRawData: true,
          dateRange: 'last7days',
          filters: { minSessionDuration: 30 }
        },
        isCustom: true,
        createdAt: new Date(Date.now() - 86400000 * 3)
      }
    ];

    setTemplates(mockTemplates);
  }, []);

  // Simulate job progress updates
  useEffect(() => {
    const interval = setInterval(() => {
      setExportJobs(prev => prev.map(job => {
        if (job.status === 'processing' && job.progress < 100) {
          const newProgress = Math.min(100, job.progress + Math.random() * 10);
          const newStatus = newProgress >= 100 ? 'completed' : 'processing';
          return {
            ...job,
            progress: newProgress,
            status: newStatus as any,
            completedAt: newStatus === 'completed' ? new Date() : undefined,
            size: newStatus === 'completed' ? Math.random() * 5 * 1024 * 1024 : undefined,
            downloadUrl: newStatus === 'completed' ? `/exports/${job.name.toLowerCase().replace(/\s+/g, '-')}.${job.format}` : undefined
          };
        }
        return job;
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const handleCreateExport = async () => {
    if (!hasData) {
      alert('No documents available for export. Please upload and process a PDF first.');
      return;
    }

    try {
      const request: ExportRequest = {
        id: Date.now().toString(),
        type: selectedType,
        format: selectedFormat,
        settings: {
          includeCharts: exportSettings.includeCharts,
          includeRawData: exportSettings.includeRawData,
          compression: exportSettings.compression,
          dateRange: exportSettings.dateRange === 'custom' ? {
            start: new Date(exportSettings.customDateStart),
            end: new Date(exportSettings.customDateEnd)
          } : undefined
        }
      };

      await exportService.createExport(request);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to create export');
    }
  };

  const handleUseTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedType(template.type);
      setSelectedFormat(template.format);
      setExportSettings({
        ...exportSettings,
        ...template.defaultSettings,
        customDateStart: '',
        customDateEnd: ''
      });
      setSelectedTemplate(templateId);
      setActiveTab('create');
    }
  };

  const handleDownload = (job: ExportJob) => {
    if (job.status === 'completed') {
      exportService.downloadJob(job);
    }
  };

  const handleCancelJob = (jobId: string) => {
    exportService.cancelJob(jobId);
  };

  const handleDeleteJob = (jobId: string) => {
    exportService.deleteJob(jobId);
  };

  const getTypeLabel = (type: ExportType): string => {
    const labels: Record<ExportType, string> = {
      'semantic-analysis': 'Semantic Analysis',
      'knowledge-graph': 'Knowledge Graph',
      'behavioral-patterns': 'Behavioral Patterns',
      'agent-reports': 'Agent Reports',
      'system-metrics': 'System Metrics',
      'workflow-logs': 'Workflow Logs',
      'full-report': 'Full Report',
      'custom': 'Custom Export'
    };
    return labels[type];
  };

  const getTypeIcon = (type: ExportType) => {
    const icons: Record<ExportType, React.ComponentType<any>> = {
      'semantic-analysis': Brain,
      'knowledge-graph': Network,
      'behavioral-patterns': Users,
      'agent-reports': FileText,
      'system-metrics': BarChart3,
      'workflow-logs': Archive,
      'full-report': Database,
      'custom': Settings
    };
    const Icon = icons[type];
    return <Icon className="w-4 h-4" />;
  };

  const getFormatIcon = (format: ExportFormat) => {
    const icons: Record<ExportFormat, React.ComponentType<any>> = {
      pdf: FileText,
      json: FileJson,
      csv: FileSpreadsheet,
      xlsx: FileSpreadsheet,
      png: FileImage,
      svg: FileImage,
      html: FileText,
      md: FileText
    };
    const Icon = icons[format];
    return <Icon className="w-4 h-4" />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-500';
      case 'processing': return 'text-blue-500';
      case 'failed': return 'text-red-500';
      case 'cancelled': return 'text-gray-500';
      default: return 'text-yellow-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'processing': return <RefreshCw className="w-4 h-4 animate-spin" />;
      case 'failed': return <AlertCircle className="w-4 h-4" />;
      case 'cancelled': return <Clock className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const filteredJobs = exportJobs.filter(job => {
    const matchesSearch = job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || job.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Export Center</h2>
          <p className="text-gray-400 mt-1">Generate and download reports, data exports, and documentation</p>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-400">
            {exportJobs.filter(j => j.status === 'processing').length} active jobs
          </span>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-slate-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('create')}
          className={`flex-1 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'create'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          Create Export
        </button>
        <button
          onClick={() => setActiveTab('jobs')}
          className={`flex-1 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'jobs'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          Export Jobs ({exportJobs.length})
        </button>
        <button
          onClick={() => setActiveTab('templates')}
          className={`flex-1 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'templates'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          Templates ({templates.length})
        </button>
      </div>

      {/* Create Export Tab */}
      {activeTab === 'create' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Export Configuration */}
          <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
            <h3 className="text-lg font-semibold mb-4">Export Configuration</h3>
            
            {/* Export Type */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">Export Type</label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value as ExportType)}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              >
                <option value="semantic-analysis">Semantic Analysis</option>
                <option value="knowledge-graph">Knowledge Graph</option>
                <option value="behavioral-patterns">Behavioral Patterns</option>
                <option value="agent-reports">Agent Reports</option>
                <option value="system-metrics">System Metrics</option>
                <option value="workflow-logs">Workflow Logs</option>
                <option value="full-report">Full Report</option>
                <option value="custom">Custom Export</option>
              </select>
            </div>

            {/* Export Format */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">Format</label>
              <div className="grid grid-cols-4 gap-2">
                {(['pdf', 'json', 'csv', 'xlsx', 'png', 'svg', 'html', 'md'] as ExportFormat[]).map((format) => (
                  <button
                    key={format}
                    onClick={() => setSelectedFormat(format)}
                    className={`flex items-center justify-center space-x-1 px-3 py-2 rounded border transition-colors ${
                      selectedFormat === format
                        ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                        : 'border-slate-600 bg-slate-700 text-gray-300 hover:border-slate-500'
                    }`}
                  >
                    {getFormatIcon(format)}
                    <span className="text-xs uppercase">{format}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Date Range */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
              <select
                value={exportSettings.dateRange}
                onChange={(e) => setExportSettings(prev => ({ ...prev, dateRange: e.target.value as any }))}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              >
                <option value="last7days">Last 7 days</option>
                <option value="last30days">Last 30 days</option>
                <option value="last90days">Last 90 days</option>
                <option value="custom">Custom range</option>
              </select>
              
              {exportSettings.dateRange === 'custom' && (
                <div className="grid grid-cols-2 gap-2 mt-2">
                  <input
                    type="date"
                    value={exportSettings.customDateStart}
                    onChange={(e) => setExportSettings(prev => ({ ...prev, customDateStart: e.target.value }))}
                    className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                  />
                  <input
                    type="date"
                    value={exportSettings.customDateEnd}
                    onChange={(e) => setExportSettings(prev => ({ ...prev, customDateEnd: e.target.value }))}
                    className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                  />
                </div>
              )}
            </div>

            {/* Options */}
            <div className="space-y-3">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exportSettings.includeCharts}
                  onChange={(e) => setExportSettings(prev => ({ ...prev, includeCharts: e.target.checked }))}
                  className="rounded border-slate-600 bg-slate-700 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-300">Include charts and visualizations</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exportSettings.includeRawData}
                  onChange={(e) => setExportSettings(prev => ({ ...prev, includeRawData: e.target.checked }))}
                  className="rounded border-slate-600 bg-slate-700 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-300">Include raw data</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exportSettings.compression}
                  onChange={(e) => setExportSettings(prev => ({ ...prev, compression: e.target.checked }))}
                  className="rounded border-slate-600 bg-slate-700 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-300">Compress output</span>
              </label>
            </div>
          </div>

          {/* Preview & Actions */}
          <div className="bg-slate-800 rounded-lg border border-slate-700 p-6">
            <h3 className="text-lg font-semibold mb-4">Export Preview</h3>
            
            <div className="space-y-4 mb-6">
              <div className="flex items-center space-x-3">
                <div className="text-blue-500">
                  {getTypeIcon(selectedType)}
                </div>
                <div>
                  <div className="font-medium">{getTypeLabel(selectedType)}</div>
                  <div className="text-sm text-gray-400">Export type</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="text-green-500">
                  {getFormatIcon(selectedFormat)}
                </div>
                <div>
                  <div className="font-medium uppercase">{selectedFormat}</div>
                  <div className="text-sm text-gray-400">Output format</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Calendar className="w-4 h-4 text-purple-500" />
                <div>
                  <div className="font-medium">
                    {exportSettings.dateRange === 'custom' 
                      ? `${exportSettings.customDateStart} to ${exportSettings.customDateEnd}`
                      : exportSettings.dateRange.replace(/([a-z])([0-9])/g, '$1 $2')
                    }
                  </div>
                  <div className="text-sm text-gray-400">Date range</div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={handleCreateExport}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Create Export</span>
              </button>
              
              {selectedTemplate && (
                <button
                  onClick={() => setSelectedTemplate(null)}
                  className="w-full px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg transition-colors text-sm"
                >
                  Clear Template
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Export Jobs Tab */}
      {activeTab === 'jobs' && (
        <div className="space-y-4">
          {/* Filters */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search export jobs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>

          {/* Jobs List */}
          <div className="space-y-3">
            {filteredJobs.map((job) => (
              <div key={job.id} className="bg-slate-800 rounded-lg border border-slate-700 p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="text-blue-500">
                      {getTypeIcon(job.type)}
                    </div>
                    <div>
                      <h4 className="font-medium text-white">{job.name}</h4>
                      <div className="flex items-center space-x-2 text-sm text-gray-400">
                        <span className="capitalize">{job.type.replace('-', ' ')}</span>
                        <span>•</span>
                        <span className="uppercase">{job.format}</span>
                        <span>•</span>
                        <span>{job.createdAt.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`flex items-center space-x-1 ${getStatusColor(job.status)}`}>
                      {getStatusIcon(job.status)}
                      <span className="text-sm capitalize">{job.status}</span>
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                {job.status === 'processing' && (
                  <div className="mb-3">
                    <div className="flex items-center justify-between text-sm text-gray-400 mb-1">
                      <span>Progress</span>
                      <span>{job.progress.toFixed(0)}%</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${job.progress}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Error Message */}
                {job.status === 'failed' && job.error && (
                  <div className="mb-3 p-2 bg-red-500/20 border border-red-500/50 rounded text-red-400 text-sm">
                    {job.error}
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-400">
                    {job.size && (
                      <span>Size: {formatFileSize(job.size)}</span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {job.status === 'completed' && job.downloadUrl && (
                      <button
                        onClick={() => handleDownload(job)}
                        className="flex items-center space-x-1 px-3 py-1 bg-green-600 hover:bg-green-700 rounded text-sm transition-colors"
                      >
                        <Download className="w-3 h-3" />
                        <span>Download</span>
                      </button>
                    )}
                    {job.status === 'processing' && (
                      <button
                        onClick={() => handleCancelJob(job.id)}
                        className="flex items-center space-x-1 px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm transition-colors"
                      >
                        <Square className="w-3 h-3" />
                        <span>Cancel</span>
                      </button>
                    )}
                    <button
                      onClick={() => handleDeleteJob(job.id)}
                      className="p-1 text-gray-400 hover:text-red-400 hover:bg-slate-700 rounded transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Templates Tab */}
      {activeTab === 'templates' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {templates.map((template) => (
            <div key={template.id} className="bg-slate-800 rounded-lg border border-slate-700 p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className="text-blue-500">
                    {getTypeIcon(template.type)}
                  </div>
                  <h4 className="font-medium text-white">{template.name}</h4>
                </div>
                {template.isCustom && (
                  <span className="px-2 py-1 bg-purple-500/20 text-purple-400 text-xs rounded">
                    Custom
                  </span>
                )}
              </div>
              
              <p className="text-sm text-gray-400 mb-4">{template.description}</p>
              
              <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                <span className="capitalize">{template.type.replace('-', ' ')}</span>
                <span className="uppercase">{template.format}</span>
              </div>
              
              <button
                onClick={() => handleUseTemplate(template.id)}
                className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
              >
                <Eye className="w-4 h-4" />
                <span>Use Template</span>
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ExportPanel;