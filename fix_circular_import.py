# Fix circular import in documentationSuite.js
import re

# Read the file
with open('modules/documentationSuite.js', 'r', encoding='utf-8') as f:
    content = f.read()

# Replace the problematic import line in the code example
content = re.sub(
    r"import ImageRecreationApp from '\./recreationApp\.js';\n\n",
    "// Initialize the application (ImageRecreationApp should be imported at module level)\n",
    content
)

# Write the fixed content back
with open('modules/documentationSuite.js', 'w', encoding='utf-8') as f:
    f.write(content)

print("Fixed circular import in documentationSuite.js")