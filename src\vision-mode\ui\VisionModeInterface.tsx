/**
 * Vision Mode Interface
 * Main interface component that integrates with the existing PDF recreation app
 */

import React, { useState, useEffect } from 'react';
import { 
  Activity, 
  Brain, 
  Settings, 
  FileText, 
  BarChart3, 
  Workflow, 
  Download,
  Play,
  Pause,
  Square,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Users
} from 'lucide-react';
// Temporarily simplified imports to fix compilation issues
// import { VisionModeAgent, createDefaultVisionModeConfig } from '../VisionModeAgent';
// import { AgentStatus } from '../agents/types';
import { Dashboard as DashboardPanel } from './components/Dashboard';
import { AgentControlPanel } from './components/AgentControlPanel';
import { VisualizationPanel } from './components/VisualizationPanel';
import { ExportPanel } from './components/ExportPanel';
import { SettingsPanel } from './components/SettingsPanel';
import { WorkflowPanel } from './components/WorkflowPanel';
import { AnalysisPanel } from './components/AnalysisPanel';

// Temporary type definitions
type AgentStatus = 'initializing' | 'ready' | 'busy' | 'waiting' | 'error' | 'offline';
interface VisionModeAgent {
  initialize(): Promise<void>;
  getStatus(): any;
}

// Mock VisionModeAgent for now
const createDefaultVisionModeConfig = () => ({});
class MockVisionModeAgent implements VisionModeAgent {
  async initialize() {
    console.log('Mock VisionModeAgent initialized');
  }
  getStatus() {
    return { initialized: true, health: 'good' };
  }
}

/**
 * Vision Mode interface props
 */
export interface VisionModeInterfaceProps {
  onDocumentUpload?: (file: File) => void;
  onAnalysisComplete?: (results: any) => void;
  className?: string;
}

/**
 * System status
 */
export interface SystemStatus {
  overall: 'idle' | 'running' | 'error' | 'complete';
  agents: Record<string, AgentStatus>;
  activeWorkflows: number;
  completedTasks: number;
  totalTasks: number;
  uptime: number;
  lastUpdate: Date;
}

/**
 * Navigation tab
 */
export interface NavigationTab {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  component: React.ComponentType<any>;
  badge?: number;
}

/**
 * Vision Mode Interface Component
 */
export const VisionModeInterface: React.FC<VisionModeInterfaceProps> = ({
  onDocumentUpload,
  onAnalysisComplete,
  className = ''
}) => {
  const [visionAgent, setVisionAgent] = useState<VisionModeAgent | null>(null);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    overall: 'idle',
    agents: {},
    activeWorkflows: 0,
    completedTasks: 0,
    totalTasks: 0,
    uptime: 0,
    lastUpdate: new Date()
  });
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize Vision Mode system
  useEffect(() => {
    const initializeVisionMode = async () => {
      try {
        setIsInitializing(true);
        const config = createDefaultVisionModeConfig();
        const agent = new MockVisionModeAgent();
        await agent.initialize();
        setVisionAgent(agent);
        setError(null);
      } catch (err) {
        console.error('Failed to initialize Vision Mode:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsInitializing(false);
      }
    };

    initializeVisionMode();
  }, []);

  // Update system status
  useEffect(() => {
    if (!visionAgent) return;

    const updateStatus = () => {
      // This would be connected to real agent status in production
      setSystemStatus(prev => ({
        ...prev,
        lastUpdate: new Date(),
        uptime: prev.uptime + 1
      }));
    };

    const interval = setInterval(updateStatus, 1000);
    return () => clearInterval(interval);
  }, [visionAgent]);

  // Navigation tabs configuration
  const navigationTabs: NavigationTab[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Activity,
      component: DashboardPanel,
      badge: systemStatus.activeWorkflows
    },
    {
      id: 'agents',
      label: 'Agents',
      icon: Users,
      component: AgentControlPanel
    },
    {
      id: 'workflows',
      label: 'Workflows',
      icon: Workflow,
      component: WorkflowPanel
    },
    {
      id: 'analysis',
      label: 'Analysis',
      icon: Brain,
      component: AnalysisPanel
    },
    {
      id: 'visualization',
      label: 'Visualization',
      icon: BarChart3,
      component: VisualizationPanel
    },
    {
      id: 'export',
      label: 'Export',
      icon: Download,
      component: ExportPanel
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      component: SettingsPanel
    }
  ];

  const handleDocumentUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onDocumentUpload) {
      onDocumentUpload(file);
    }
  };

  const handleSystemControl = (action: 'start' | 'pause' | 'stop' | 'restart') => {
    console.log(`System ${action} requested`);
    // Implement system control logic
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-500';
      case 'error': return 'text-red-500';
      case 'complete': return 'text-blue-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <RefreshCw className="w-4 h-4 animate-spin" />;
      case 'error': return <AlertCircle className="w-4 h-4" />;
      case 'complete': return <CheckCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  if (isInitializing) {
    return (
      <div className={`flex items-center justify-center min-h-screen bg-slate-900 ${className}`}>
        <div className="text-center">
          <RefreshCw className="w-12 h-12 text-blue-500 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-white mb-2">Initializing Vision Mode</h2>
          <p className="text-gray-400">Setting up MCP agents and semantic analysis...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center min-h-screen bg-slate-900 ${className}`}>
        <div className="text-center max-w-md">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-white mb-2">Initialization Failed</h2>
          <p className="text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const ActiveComponent = navigationTabs.find(tab => tab.id === activeTab)?.component || DashboardPanel;

  return (
    <div className={`min-h-screen bg-slate-900 text-white ${className}`}>
      {/* Header */}
      <header className="bg-slate-800 border-b border-slate-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Brain className="w-8 h-8 text-blue-500" />
                <h1 className="text-2xl font-bold">Vision Mode</h1>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <div className={`flex items-center space-x-1 ${getStatusColor(systemStatus.overall)}`}>
                  {getStatusIcon(systemStatus.overall)}
                  <span className="capitalize">{systemStatus.overall}</span>
                </div>
                <span className="text-gray-400">•</span>
                <span className="text-gray-400">
                  {systemStatus.completedTasks}/{systemStatus.totalTasks} tasks
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* System Controls */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleSystemControl('start')}
                  className="p-2 text-green-500 hover:bg-slate-700 rounded-lg transition-colors"
                  title="Start System"
                >
                  <Play className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleSystemControl('pause')}
                  className="p-2 text-yellow-500 hover:bg-slate-700 rounded-lg transition-colors"
                  title="Pause System"
                >
                  <Pause className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleSystemControl('stop')}
                  className="p-2 text-red-500 hover:bg-slate-700 rounded-lg transition-colors"
                  title="Stop System"
                >
                  <Square className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleSystemControl('restart')}
                  className="p-2 text-blue-500 hover:bg-slate-700 rounded-lg transition-colors"
                  title="Restart System"
                >
                  <RefreshCw className="w-4 h-4" />
                </button>
              </div>

              {/* Document Upload */}
              <div className="relative">
                <input
                  type="file"
                  id="document-upload"
                  onChange={handleDocumentUpload}
                  accept=".pdf,.png,.jpg,.jpeg,.txt,.md"
                  className="hidden"
                />
                <label
                  htmlFor="document-upload"
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg cursor-pointer transition-colors"
                >
                  <FileText className="w-4 h-4" />
                  <span>Upload Document</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar Navigation */}
        <nav className="w-64 bg-slate-800 border-r border-slate-700">
          <div className="p-4">
            <div className="space-y-2">
              {navigationTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-300 hover:bg-slate-700 hover:text-white'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="w-5 h-5" />
                      <span>{tab.label}</span>
                    </div>
                    {tab.badge !== undefined && tab.badge > 0 && (
                      <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                        {tab.badge}
                      </span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* System Status Summary */}
          <div className="p-4 border-t border-slate-700">
            <h3 className="text-sm font-semibold text-gray-400 mb-3">System Status</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Uptime</span>
                <span className="text-white">{Math.floor(systemStatus.uptime / 60)}m</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Active Agents</span>
                <span className="text-white">{Object.keys(systemStatus.agents).length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Workflows</span>
                <span className="text-white">{systemStatus.activeWorkflows}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 overflow-hidden">
          <ActiveComponent
            visionAgent={visionAgent}
            systemStatus={systemStatus}
            onStatusUpdate={setSystemStatus}
          />
        </main>
      </div>
    </div>
  );
};

// Placeholder components removed - using real imported components

export default VisionModeInterface;