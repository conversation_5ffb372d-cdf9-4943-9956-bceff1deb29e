/**
 * AI Service Error Handler
 * Provides comprehensive error handling and fallback mechanisms for AI services
 */

import { getEnvironmentConfig, maskSensitiveData } from '../../config/environment.js';

/**
 * AI Error Types
 */
export enum AIErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * AI Error Details
 */
export interface AIError {
  type: AIErrorType;
  message: string;
  code?: string | number;
  statusCode?: number;
  retryable: boolean;
  retryAfter?: number; // seconds
  details?: any;
  timestamp: Date;
  service: string;
}

/**
 * Retry Configuration
 */
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
  retryableErrors: AIErrorType[];
}

/**
 * Fallback Strategy
 */
export enum FallbackStrategy {
  LOCAL_AI = 'LOCAL_AI',
  CACHED_RESPONSE = 'CACHED_RESPONSE',
  SIMPLIFIED_ANALYSIS = 'SIMPLIFIED_ANALYSIS',
  GRACEFUL_DEGRADATION = 'GRACEFUL_DEGRADATION',
  FAIL_FAST = 'FAIL_FAST'
}

/**
 * Error Handler Configuration
 */
export interface ErrorHandlerConfig {
  enableLogging: boolean;
  enableMetrics: boolean;
  enableFallback: boolean;
  defaultFallbackStrategy: FallbackStrategy;
  retryConfig: RetryConfig;
  circuitBreakerThreshold: number;
  circuitBreakerTimeout: number; // milliseconds
}

/**
 * Circuit Breaker State
 */
enum CircuitBreakerState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN'
}

/**
 * AI Service Error Handler
 */
export class AIErrorHandler {
  private config: ErrorHandlerConfig;
  private errorMetrics: Map<string, number> = new Map();
  private circuitBreakerState: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private circuitBreakerFailureCount: number = 0;
  private circuitBreakerLastFailure: Date | null = null;
  private retryDelays: Map<string, number> = new Map();

  constructor(config?: Partial<ErrorHandlerConfig>) {
    const envConfig = getEnvironmentConfig();
    
    this.config = {
      enableLogging: envConfig.ai.enableLogging,
      enableMetrics: true,
      enableFallback: envConfig.ai.fallbackToLocal,
      defaultFallbackStrategy: FallbackStrategy.LOCAL_AI,
      retryConfig: {
        maxRetries: envConfig.ai.maxRetries,
        baseDelay: 1000,
        maxDelay: 30000,
        backoffMultiplier: 2,
        retryableErrors: [
          AIErrorType.NETWORK_ERROR,
          AIErrorType.TIMEOUT_ERROR,
          AIErrorType.RATE_LIMIT_ERROR,
          AIErrorType.SERVICE_UNAVAILABLE
        ]
      },
      circuitBreakerThreshold: 5,
      circuitBreakerTimeout: 60000,
      ...config
    };
  }

  /**
   * Handle and classify errors
   */
  handleError(error: any, service: string, operation: string): AIError {
    const aiError = this.classifyError(error, service);
    
    // Log error
    if (this.config.enableLogging) {
      this.logError(aiError, operation);
    }
    
    // Update metrics
    if (this.config.enableMetrics) {
      this.updateErrorMetrics(aiError);
    }
    
    // Update circuit breaker
    this.updateCircuitBreaker(aiError);
    
    return aiError;
  }

  /**
   * Classify error type
   */
  private classifyError(error: any, service: string): AIError {
    const timestamp = new Date();
    
    // Network errors
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ECONNRESET') {
      return {
        type: AIErrorType.NETWORK_ERROR,
        message: `Network error: ${error.message}`,
        code: error.code,
        retryable: true,
        timestamp,
        service
      };
    }
    
    // HTTP status code errors
    if (error.response?.status) {
      const statusCode = error.response.status;
      
      if (statusCode === 401 || statusCode === 403) {
        return {
          type: AIErrorType.AUTHENTICATION_ERROR,
          message: 'Authentication failed. Please check your API key.',
          statusCode,
          retryable: false,
          timestamp,
          service
        };
      }
      
      if (statusCode === 429) {
        const retryAfter = this.parseRetryAfter(error.response.headers);
        return {
          type: AIErrorType.RATE_LIMIT_ERROR,
          message: 'Rate limit exceeded. Please try again later.',
          statusCode,
          retryable: true,
          retryAfter,
          timestamp,
          service
        };
      }
      
      if (statusCode === 402 || statusCode === 402) {
        return {
          type: AIErrorType.QUOTA_EXCEEDED,
          message: 'API quota exceeded. Please check your billing.',
          statusCode,
          retryable: false,
          timestamp,
          service
        };
      }
      
      if (statusCode >= 500) {
        return {
          type: AIErrorType.SERVICE_UNAVAILABLE,
          message: `Service temporarily unavailable (${statusCode})`,
          statusCode,
          retryable: true,
          timestamp,
          service
        };
      }
      
      if (statusCode >= 400) {
        return {
          type: AIErrorType.API_ERROR,
          message: `API error: ${error.response.data?.message || error.message}`,
          statusCode,
          retryable: false,
          timestamp,
          service,
          details: error.response.data
        };
      }
    }
    
    // Timeout errors
    if (error.code === 'ETIMEDOUT' || error.message?.includes('timeout')) {
      return {
        type: AIErrorType.TIMEOUT_ERROR,
        message: 'Request timeout. The service took too long to respond.',
        code: error.code,
        retryable: true,
        timestamp,
        service
      };
    }
    
    // Validation errors
    if (error.name === 'ValidationError' || error.message?.includes('validation')) {
      return {
        type: AIErrorType.VALIDATION_ERROR,
        message: `Validation error: ${error.message}`,
        retryable: false,
        timestamp,
        service,
        details: error.details
      };
    }
    
    // Unknown errors
    return {
      type: AIErrorType.UNKNOWN_ERROR,
      message: error.message || 'An unknown error occurred',
      retryable: false,
      timestamp,
      service,
      details: error
    };
  }

  /**
   * Parse retry-after header
   */
  private parseRetryAfter(headers: any): number | undefined {
    const retryAfter = headers?.['retry-after'] || headers?.['Retry-After'];
    if (retryAfter) {
      const seconds = parseInt(retryAfter, 10);
      return isNaN(seconds) ? undefined : seconds;
    }
    return undefined;
  }

  /**
   * Check if error is retryable
   */
  isRetryable(error: AIError): boolean {
    if (!error.retryable) return false;
    if (this.circuitBreakerState === CircuitBreakerState.OPEN) return false;
    return this.config.retryConfig.retryableErrors.includes(error.type);
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay(attempt: number, error: AIError): number {
    const { baseDelay, maxDelay, backoffMultiplier } = this.config.retryConfig;
    
    // Use retry-after header if available
    if (error.retryAfter) {
      return Math.min(error.retryAfter * 1000, maxDelay);
    }
    
    // Exponential backoff with jitter
    const delay = baseDelay * Math.pow(backoffMultiplier, attempt - 1);
    const jitter = Math.random() * 0.1 * delay; // 10% jitter
    
    return Math.min(delay + jitter, maxDelay);
  }

  /**
   * Execute operation with retry logic
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    service: string,
    operationName: string
  ): Promise<T> {
    const { maxRetries } = this.config.retryConfig;
    let lastError: AIError | null = null;
    
    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        // Check circuit breaker
        if (this.circuitBreakerState === CircuitBreakerState.OPEN) {
          if (this.shouldTryCircuitBreaker()) {
            this.circuitBreakerState = CircuitBreakerState.HALF_OPEN;
          } else {
            throw new Error('Circuit breaker is open. Service temporarily unavailable.');
          }
        }
        
        const result = await operation();
        
        // Reset circuit breaker on success
        if (this.circuitBreakerState === CircuitBreakerState.HALF_OPEN) {
          this.circuitBreakerState = CircuitBreakerState.CLOSED;
          this.circuitBreakerFailureCount = 0;
        }
        
        return result;
        
      } catch (error) {
        lastError = this.handleError(error, service, operationName);
        
        // Don't retry if not retryable or max attempts reached
        if (!this.isRetryable(lastError) || attempt > maxRetries) {
          break;
        }
        
        // Calculate and wait for retry delay
        const delay = this.calculateRetryDelay(attempt, lastError);
        
        if (this.config.enableLogging) {
          console.warn(`Retrying ${operationName} in ${delay}ms (attempt ${attempt}/${maxRetries})`);
        }
        
        await this.sleep(delay);
      }
    }
    
    throw lastError || new Error('Operation failed after retries');
  }

  /**
   * Get fallback strategy for error
   */
  getFallbackStrategy(error: AIError): FallbackStrategy {
    switch (error.type) {
      case AIErrorType.AUTHENTICATION_ERROR:
      case AIErrorType.QUOTA_EXCEEDED:
        return FallbackStrategy.LOCAL_AI;
      
      case AIErrorType.RATE_LIMIT_ERROR:
        return FallbackStrategy.CACHED_RESPONSE;
      
      case AIErrorType.TIMEOUT_ERROR:
      case AIErrorType.SERVICE_UNAVAILABLE:
        return FallbackStrategy.SIMPLIFIED_ANALYSIS;
      
      case AIErrorType.VALIDATION_ERROR:
        return FallbackStrategy.GRACEFUL_DEGRADATION;
      
      default:
        return this.config.defaultFallbackStrategy;
    }
  }

  /**
   * Update circuit breaker state
   */
  private updateCircuitBreaker(error: AIError): void {
    if (error.retryable) {
      this.circuitBreakerFailureCount++;
      this.circuitBreakerLastFailure = new Date();
      
      if (this.circuitBreakerFailureCount >= this.config.circuitBreakerThreshold) {
        this.circuitBreakerState = CircuitBreakerState.OPEN;
        
        if (this.config.enableLogging) {
          console.warn(`Circuit breaker opened for ${error.service} after ${this.circuitBreakerFailureCount} failures`);
        }
      }
    }
  }

  /**
   * Check if circuit breaker should try again
   */
  private shouldTryCircuitBreaker(): boolean {
    if (!this.circuitBreakerLastFailure) return true;
    
    const timeSinceLastFailure = Date.now() - this.circuitBreakerLastFailure.getTime();
    return timeSinceLastFailure >= this.config.circuitBreakerTimeout;
  }

  /**
   * Update error metrics
   */
  private updateErrorMetrics(error: AIError): void {
    const key = `${error.service}:${error.type}`;
    const current = this.errorMetrics.get(key) || 0;
    this.errorMetrics.set(key, current + 1);
  }

  /**
   * Log error with appropriate level
   */
  private logError(error: AIError, operation: string): void {
    const logData = {
      operation,
      error: {
        type: error.type,
        message: error.message,
        service: error.service,
        retryable: error.retryable,
        timestamp: error.timestamp
      }
    };
    
    // Mask sensitive data
    const maskedData = maskSensitiveData(logData);
    
    if (error.retryable) {
      console.warn('AI Service Warning:', maskedData);
    } else {
      console.error('AI Service Error:', maskedData);
    }
  }

  /**
   * Get error metrics
   */
  getErrorMetrics(): Record<string, number> {
    return Object.fromEntries(this.errorMetrics);
  }

  /**
   * Get circuit breaker status
   */
  getCircuitBreakerStatus(): {
    state: CircuitBreakerState;
    failureCount: number;
    lastFailure: Date | null;
  } {
    return {
      state: this.circuitBreakerState,
      failureCount: this.circuitBreakerFailureCount,
      lastFailure: this.circuitBreakerLastFailure
    };
  }

  /**
   * Reset circuit breaker
   */
  resetCircuitBreaker(): void {
    this.circuitBreakerState = CircuitBreakerState.CLOSED;
    this.circuitBreakerFailureCount = 0;
    this.circuitBreakerLastFailure = null;
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Global error handler instance
 */
export const aiErrorHandler = new AIErrorHandler();