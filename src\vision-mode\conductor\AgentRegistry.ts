/**
 * Agent Registry and Discovery System
 * Manages agent registration, discovery, and lifecycle coordination
 */

import {
  AgentId,
  AgentCapability,
  AgentStatus,
  AgentMetadata,
  AgentConfig,
  AgentHealthCheck,
  AgentMetrics
} from '../agents/types.js';
import { EventEmitter } from 'events';

/**
 * Agent registration information
 */
export interface AgentRegistration {
  id: AgentId;
  config: AgentConfig;
  metadata: AgentMetadata;
  status: AgentStatus;
  capabilities: AgentCapability[];
  endpoint: AgentEndpoint;
  healthCheck: AgentHealthCheck;
  metrics: AgentMetrics;
  registeredAt: Date;
  lastSeen: Date;
  version: string;
  dependencies: AgentDependency[];
  tags: string[];
}

/**
 * Agent endpoint information
 */
export interface AgentEndpoint {
  type: 'local' | 'remote' | 'worker' | 'service';
  address: string;
  port?: number;
  protocol: 'http' | 'https' | 'ws' | 'wss' | 'ipc' | 'memory';
  authentication?: AuthenticationConfig;
  timeout: number;
  retries: number;
}

/**
 * Authentication configuration
 */
export interface AuthenticationConfig {
  type: 'none' | 'token' | 'oauth' | 'certificate' | 'custom';
  credentials?: Record<string, any>;
  refreshInterval?: number;
}

/**
 * Agent dependency
 */
export interface AgentDependency {
  agentId: AgentId;
  capability: AgentCapability;
  required: boolean;
  version?: string;
  fallback?: AgentId;
}

/**
 * Discovery query
 */
export interface DiscoveryQuery {
  capabilities?: AgentCapability[];
  tags?: string[];
  status?: AgentStatus[];
  version?: string;
  location?: LocationFilter;
  performance?: PerformanceFilter;
  availability?: AvailabilityFilter;
}

/**
 * Location filter
 */
export interface LocationFilter {
  type: 'local' | 'remote' | 'region' | 'zone';
  value?: string;
  proximity?: number;
}

/**
 * Performance filter
 */
export interface PerformanceFilter {
  minCpuAvailable?: number;
  minMemoryAvailable?: number;
  maxLatency?: number;
  minThroughput?: number;
}

/**
 * Availability filter
 */
export interface AvailabilityFilter {
  minUptime?: number;
  maxLoadFactor?: number;
  requiresHealthy?: boolean;
}

/**
 * Discovery result
 */
export interface DiscoveryResult {
  agents: AgentRegistration[];
  total: number;
  query: DiscoveryQuery;
  executionTime: number;
  cached: boolean;
}

/**
 * Load balancing strategy
 */
export enum LoadBalancingStrategy {
  ROUND_ROBIN = 'round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  LEAST_RESPONSE_TIME = 'least_response_time',
  WEIGHTED_ROUND_ROBIN = 'weighted_round_robin',
  RANDOM = 'random',
  CAPABILITY_BASED = 'capability_based',
  PERFORMANCE_BASED = 'performance_based'
}

/**
 * Agent selection criteria
 */
export interface AgentSelectionCriteria {
  capability: AgentCapability;
  strategy: LoadBalancingStrategy;
  filters?: DiscoveryQuery;
  weights?: Record<string, number>;
  excludeAgents?: AgentId[];
  preferredAgents?: AgentId[];
  maxCandidates?: number;
}

/**
 * Agent selection result
 */
export interface AgentSelectionResult {
  agent: AgentRegistration;
  score: number;
  reason: string;
  alternatives: AgentRegistration[];
}

/**
 * Registry configuration
 */
export interface RegistryConfig {
  maxAgents: number;
  healthCheckInterval: number;
  cleanupInterval: number;
  agentTimeout: number;
  cacheTimeout: number;
  enableMetrics: boolean;
  enableLoadBalancing: boolean;
  defaultStrategy: LoadBalancingStrategy;
  persistenceEnabled: boolean;
  persistencePath?: string;
}

/**
 * Registry statistics
 */
export interface RegistryStatistics {
  totalAgents: number;
  activeAgents: number;
  inactiveAgents: number;
  capabilityDistribution: Record<AgentCapability, number>;
  statusDistribution: Record<AgentStatus, number>;
  averageResponseTime: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  uptime: number;
}

/**
 * Registry event types
 */
export enum RegistryEventType {
  AGENT_REGISTERED = 'agent_registered',
  AGENT_UNREGISTERED = 'agent_unregistered',
  AGENT_STATUS_CHANGED = 'agent_status_changed',
  AGENT_HEALTH_CHANGED = 'agent_health_changed',
  DISCOVERY_QUERY = 'discovery_query',
  AGENT_SELECTED = 'agent_selected',
  REGISTRY_STARTED = 'registry_started',
  REGISTRY_STOPPED = 'registry_stopped'
}

/**
 * Registry event
 */
export interface RegistryEvent {
  type: RegistryEventType;
  agentId?: AgentId;
  data: Record<string, any>;
  timestamp: Date;
}

/**
 * Agent Registry Implementation
 */
export class AgentRegistry extends EventEmitter {
  private _agents: Map<AgentId, AgentRegistration> = new Map();
  private _capabilityIndex: Map<AgentCapability, Set<AgentId>> = new Map();
  private _tagIndex: Map<string, Set<AgentId>> = new Map();
  private _statusIndex: Map<AgentStatus, Set<AgentId>> = new Map();
  private _discoveryCache: Map<string, DiscoveryResult> = new Map();
  private _loadBalancers: Map<AgentCapability, LoadBalancer> = new Map();
  private _config: RegistryConfig;
  private _statistics: RegistryStatistics;
  private _healthCheckTimer?: NodeJS.Timeout;
  private _cleanupTimer?: NodeJS.Timeout;
  private _started: boolean = false;

  constructor(config: Partial<RegistryConfig> = {}) {
    super();
    
    this._config = {
      maxAgents: 100,
      healthCheckInterval: 30000, // 30 seconds
      cleanupInterval: 300000, // 5 minutes
      agentTimeout: 120000, // 2 minutes
      cacheTimeout: 60000, // 1 minute
      enableMetrics: true,
      enableLoadBalancing: true,
      defaultStrategy: LoadBalancingStrategy.LEAST_CONNECTIONS,
      persistenceEnabled: false,
      ...config
    };

    this._statistics = {
      totalAgents: 0,
      activeAgents: 0,
      inactiveAgents: 0,
      capabilityDistribution: {} as Record<AgentCapability, number>,
      statusDistribution: {} as Record<AgentStatus, number>,
      averageResponseTime: 0,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      uptime: 0
    };

    this.initializeIndexes();
  }

  /**
   * Start the registry
   */
  async start(): Promise<void> {
    if (this._started) {
      throw new Error('Registry is already started');
    }

    console.log('Starting Agent Registry...');
    
    // Load persisted agents if enabled
    if (this._config.persistenceEnabled) {
      await this.loadPersistedAgents();
    }

    // Start health check timer
    this._healthCheckTimer = setInterval(() => {
      this.performHealthChecks();
    }, this._config.healthCheckInterval);

    // Start cleanup timer
    this._cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this._config.cleanupInterval);

    this._started = true;
    this._statistics.uptime = Date.now();

    this.emit('registry_started', {
      type: RegistryEventType.REGISTRY_STARTED,
      data: { config: this._config },
      timestamp: new Date()
    });

    console.log('Agent Registry started successfully');
  }

  /**
   * Stop the registry
   */
  async stop(): Promise<void> {
    if (!this._started) {
      return;
    }

    console.log('Stopping Agent Registry...');

    // Clear timers
    if (this._healthCheckTimer) {
      clearInterval(this._healthCheckTimer);
      this._healthCheckTimer = undefined;
    }

    if (this._cleanupTimer) {
      clearInterval(this._cleanupTimer);
      this._cleanupTimer = undefined;
    }

    // Persist agents if enabled
    if (this._config.persistenceEnabled) {
      await this.persistAgents();
    }

    // Unregister all agents
    const agentIds = Array.from(this._agents.keys());
    for (const agentId of agentIds) {
      await this.unregisterAgent(agentId);
    }

    this._started = false;

    this.emit('registry_stopped', {
      type: RegistryEventType.REGISTRY_STOPPED,
      data: { statistics: this._statistics },
      timestamp: new Date()
    });

    console.log('Agent Registry stopped');
  }

  /**
   * Register an agent
   */
  async registerAgent(registration: Omit<AgentRegistration, 'registeredAt' | 'lastSeen'>): Promise<void> {
    if (this._agents.size >= this._config.maxAgents) {
      throw new Error(`Maximum number of agents (${this._config.maxAgents}) reached`);
    }

    if (this._agents.has(registration.id)) {
      throw new Error(`Agent ${registration.id} is already registered`);
    }

    const fullRegistration: AgentRegistration = {
      ...registration,
      registeredAt: new Date(),
      lastSeen: new Date()
    };

    // Store agent
    this._agents.set(registration.id, fullRegistration);

    // Update indexes
    this.updateIndexes(fullRegistration, 'add');

    // Initialize load balancer for new capabilities
    for (const capability of registration.capabilities) {
      if (!this._loadBalancers.has(capability)) {
        this._loadBalancers.set(capability, new LoadBalancer(this._config.defaultStrategy));
      }
      this._loadBalancers.get(capability)!.addAgent(fullRegistration);
    }

    // Update statistics
    this.updateStatistics();

    // Clear discovery cache
    this._discoveryCache.clear();

    console.log(`Agent ${registration.id} registered with capabilities: ${registration.capabilities.join(', ')}`);

    this.emit('agent_registered', {
      type: RegistryEventType.AGENT_REGISTERED,
      agentId: registration.id,
      data: { registration: fullRegistration },
      timestamp: new Date()
    });
  }

  /**
   * Unregister an agent
   */
  async unregisterAgent(agentId: AgentId): Promise<void> {
    const registration = this._agents.get(agentId);
    if (!registration) {
      throw new Error(`Agent ${agentId} is not registered`);
    }

    // Remove from indexes
    this.updateIndexes(registration, 'remove');

    // Remove from load balancers
    for (const capability of registration.capabilities) {
      const loadBalancer = this._loadBalancers.get(capability);
      if (loadBalancer) {
        loadBalancer.removeAgent(registration);
      }
    }

    // Remove agent
    this._agents.delete(agentId);

    // Update statistics
    this.updateStatistics();

    // Clear discovery cache
    this._discoveryCache.clear();

    console.log(`Agent ${agentId} unregistered`);

    this.emit('agent_unregistered', {
      type: RegistryEventType.AGENT_UNREGISTERED,
      agentId,
      data: { registration },
      timestamp: new Date()
    });
  }

  /**
   * Update agent status
   */
  async updateAgentStatus(agentId: AgentId, status: AgentStatus): Promise<void> {
    const registration = this._agents.get(agentId);
    if (!registration) {
      throw new Error(`Agent ${agentId} is not registered`);
    }

    const oldStatus = registration.status;
    registration.status = status;
    registration.lastSeen = new Date();

    // Update status index
    const oldStatusSet = this._statusIndex.get(oldStatus);
    if (oldStatusSet) {
      oldStatusSet.delete(agentId);
    }

    let newStatusSet = this._statusIndex.get(status);
    if (!newStatusSet) {
      newStatusSet = new Set();
      this._statusIndex.set(status, newStatusSet);
    }
    newStatusSet.add(agentId);

    // Update load balancers
    for (const capability of registration.capabilities) {
      const loadBalancer = this._loadBalancers.get(capability);
      if (loadBalancer) {
        loadBalancer.updateAgent(registration);
      }
    }

    // Update statistics
    this.updateStatistics();

    // Clear discovery cache
    this._discoveryCache.clear();

    console.log(`Agent ${agentId} status changed from ${oldStatus} to ${status}`);

    this.emit('agent_status_changed', {
      type: RegistryEventType.AGENT_STATUS_CHANGED,
      agentId,
      data: { oldStatus, newStatus: status },
      timestamp: new Date()
    });
  }

  /**
   * Update agent health
   */
  async updateAgentHealth(agentId: AgentId, healthCheck: AgentHealthCheck): Promise<void> {
    const registration = this._agents.get(agentId);
    if (!registration) {
      throw new Error(`Agent ${agentId} is not registered`);
    }

    const oldHealth = registration.healthCheck;
    registration.healthCheck = healthCheck;
    registration.lastSeen = new Date();

    // Update load balancers
    for (const capability of registration.capabilities) {
      const loadBalancer = this._loadBalancers.get(capability);
      if (loadBalancer) {
        loadBalancer.updateAgent(registration);
      }
    }

    console.log(`Agent ${agentId} health updated: ${healthCheck.status}`);

    this.emit('agent_health_changed', {
      type: RegistryEventType.AGENT_HEALTH_CHANGED,
      agentId,
      data: { oldHealth, newHealth: healthCheck },
      timestamp: new Date()
    });
  }

  /**
   * Update agent metrics
   */
  async updateAgentMetrics(agentId: AgentId, metrics: AgentMetrics): Promise<void> {
    const registration = this._agents.get(agentId);
    if (!registration) {
      throw new Error(`Agent ${agentId} is not registered`);
    }

    registration.metrics = metrics;
    registration.lastSeen = new Date();

    // Update load balancers
    for (const capability of registration.capabilities) {
      const loadBalancer = this._loadBalancers.get(capability);
      if (loadBalancer) {
        loadBalancer.updateAgent(registration);
      }
    }
  }

  /**
   * Discover agents based on query
   */
  async discoverAgents(query: DiscoveryQuery = {}): Promise<DiscoveryResult> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(query);
    
    // Check cache
    const cached = this._discoveryCache.get(cacheKey);
    if (cached && (Date.now() - cached.executionTime) < this._config.cacheTimeout) {
      this._statistics.totalRequests++;
      this._statistics.successfulRequests++;
      return { ...cached, cached: true };
    }

    let candidates = Array.from(this._agents.values());

    // Apply filters
    if (query.capabilities && query.capabilities.length > 0) {
      candidates = candidates.filter(agent => 
        query.capabilities!.some(cap => agent.capabilities.includes(cap))
      );
    }

    if (query.tags && query.tags.length > 0) {
      candidates = candidates.filter(agent => 
        query.tags!.some(tag => agent.tags.includes(tag))
      );
    }

    if (query.status && query.status.length > 0) {
      candidates = candidates.filter(agent => 
        query.status!.includes(agent.status)
      );
    }

    if (query.version) {
      candidates = candidates.filter(agent => 
        agent.version === query.version
      );
    }

    if (query.location) {
      candidates = this.applyLocationFilter(candidates, query.location);
    }

    if (query.performance) {
      candidates = this.applyPerformanceFilter(candidates, query.performance);
    }

    if (query.availability) {
      candidates = this.applyAvailabilityFilter(candidates, query.availability);
    }

    const executionTime = Date.now() - startTime;
    const result: DiscoveryResult = {
      agents: candidates,
      total: candidates.length,
      query,
      executionTime,
      cached: false
    };

    // Cache result
    this._discoveryCache.set(cacheKey, result);

    // Update statistics
    this._statistics.totalRequests++;
    this._statistics.successfulRequests++;
    this._statistics.averageResponseTime = 
      (this._statistics.averageResponseTime * (this._statistics.totalRequests - 1) + executionTime) / 
      this._statistics.totalRequests;

    this.emit('discovery_query', {
      type: RegistryEventType.DISCOVERY_QUERY,
      data: { query, result },
      timestamp: new Date()
    });

    return result;
  }

  /**
   * Select best agent for a capability
   */
  async selectAgent(criteria: AgentSelectionCriteria): Promise<AgentSelectionResult> {
    const loadBalancer = this._loadBalancers.get(criteria.capability);
    if (!loadBalancer) {
      throw new Error(`No agents available for capability: ${criteria.capability}`);
    }

    // Discover candidates
    const discovery = await this.discoverAgents({
      capabilities: [criteria.capability],
      ...criteria.filters
    });

    let candidates = discovery.agents;

    // Apply exclusions
    if (criteria.excludeAgents) {
      candidates = candidates.filter(agent => 
        !criteria.excludeAgents!.includes(agent.id)
      );
    }

    // Apply preferences
    if (criteria.preferredAgents) {
      const preferred = candidates.filter(agent => 
        criteria.preferredAgents!.includes(agent.id)
      );
      if (preferred.length > 0) {
        candidates = preferred;
      }
    }

    // Limit candidates
    if (criteria.maxCandidates && candidates.length > criteria.maxCandidates) {
      candidates = candidates.slice(0, criteria.maxCandidates);
    }

    if (candidates.length === 0) {
      throw new Error(`No suitable agents found for capability: ${criteria.capability}`);
    }

    // Select agent using load balancer
    const selected = loadBalancer.selectAgent(candidates, criteria);
    
    const result: AgentSelectionResult = {
      agent: selected,
      score: this.calculateAgentScore(selected, criteria),
      reason: `Selected using ${criteria.strategy} strategy`,
      alternatives: candidates.filter(agent => agent.id !== selected.id)
    };

    this.emit('agent_selected', {
      type: RegistryEventType.AGENT_SELECTED,
      agentId: selected.id,
      data: { criteria, result },
      timestamp: new Date()
    });

    return result;
  }

  /**
   * Get agent by ID
   */
  getAgent(agentId: AgentId): AgentRegistration | undefined {
    return this._agents.get(agentId);
  }

  /**
   * Get all agents
   */
  getAllAgents(): AgentRegistration[] {
    return Array.from(this._agents.values());
  }

  /**
   * Get agents by capability
   */
  getAgentsByCapability(capability: AgentCapability): AgentRegistration[] {
    const agentIds = this._capabilityIndex.get(capability) || new Set();
    return Array.from(agentIds).map(id => this._agents.get(id)!).filter(Boolean);
  }

  /**
   * Get agents by status
   */
  getAgentsByStatus(status: AgentStatus): AgentRegistration[] {
    const agentIds = this._statusIndex.get(status) || new Set();
    return Array.from(agentIds).map(id => this._agents.get(id)!).filter(Boolean);
  }

  /**
   * Get registry statistics
   */
  getStatistics(): RegistryStatistics {
    return { ...this._statistics };
  }

  /**
   * Get registry configuration
   */
  getConfig(): RegistryConfig {
    return { ...this._config };
  }

  // Private methods

  private initializeIndexes(): void {
    // Initialize capability index
    for (const capability of Object.values(AgentCapability)) {
      this._capabilityIndex.set(capability, new Set());
    }

    // Initialize status index
    for (const status of Object.values(AgentStatus)) {
      this._statusIndex.set(status, new Set());
    }
  }

  private updateIndexes(registration: AgentRegistration, operation: 'add' | 'remove'): void {
    // Update capability index
    for (const capability of registration.capabilities) {
      let capabilitySet = this._capabilityIndex.get(capability);
      if (!capabilitySet) {
        capabilitySet = new Set();
        this._capabilityIndex.set(capability, capabilitySet);
      }
      
      if (operation === 'add') {
        capabilitySet.add(registration.id);
      } else {
        capabilitySet.delete(registration.id);
      }
    }

    // Update tag index
    for (const tag of registration.tags) {
      let tagSet = this._tagIndex.get(tag);
      if (!tagSet) {
        tagSet = new Set();
        this._tagIndex.set(tag, tagSet);
      }
      
      if (operation === 'add') {
        tagSet.add(registration.id);
      } else {
        tagSet.delete(registration.id);
      }
    }

    // Update status index
    let statusSet = this._statusIndex.get(registration.status);
    if (!statusSet) {
      statusSet = new Set();
      this._statusIndex.set(registration.status, statusSet);
    }
    
    if (operation === 'add') {
      statusSet.add(registration.id);
    } else {
      statusSet.delete(registration.id);
    }
  }

  private updateStatistics(): void {
    this._statistics.totalAgents = this._agents.size;
    this._statistics.activeAgents = this.getAgentsByStatus(AgentStatus.RUNNING).length;
    this._statistics.inactiveAgents = this._statistics.totalAgents - this._statistics.activeAgents;

    // Update capability distribution
    this._statistics.capabilityDistribution = {} as Record<AgentCapability, number>;
    for (const [capability, agentIds] of this._capabilityIndex) {
      this._statistics.capabilityDistribution[capability] = agentIds.size;
    }

    // Update status distribution
    this._statistics.statusDistribution = {} as Record<AgentStatus, number>;
    for (const [status, agentIds] of this._statusIndex) {
      this._statistics.statusDistribution[status] = agentIds.size;
    }
  }

  private async performHealthChecks(): Promise<void> {
    const agents = Array.from(this._agents.values());
    const now = Date.now();

    for (const agent of agents) {
      const timeSinceLastSeen = now - agent.lastSeen.getTime();
      
      if (timeSinceLastSeen > this._config.agentTimeout) {
        console.warn(`Agent ${agent.id} has not been seen for ${timeSinceLastSeen}ms, marking as inactive`);
        await this.updateAgentStatus(agent.id, AgentStatus.STOPPED);
      }
    }
  }

  private async performCleanup(): Promise<void> {
    // Clean up discovery cache
    const now = Date.now();
    for (const [key, result] of this._discoveryCache) {
      if (now - result.executionTime > this._config.cacheTimeout) {
        this._discoveryCache.delete(key);
      }
    }

    // Clean up inactive agents
    const inactiveAgents = this.getAgentsByStatus(AgentStatus.STOPPED);
    const cutoffTime = now - (this._config.agentTimeout * 2);
    
    for (const agent of inactiveAgents) {
      if (agent.lastSeen.getTime() < cutoffTime) {
        console.log(`Removing inactive agent ${agent.id}`);
        await this.unregisterAgent(agent.id);
      }
    }
  }

  private applyLocationFilter(agents: AgentRegistration[], filter: LocationFilter): AgentRegistration[] {
    // Implement location-based filtering
    return agents.filter(agent => {
      switch (filter.type) {
        case 'local':
          return agent.endpoint.type === 'local';
        case 'remote':
          return agent.endpoint.type === 'remote';
        default:
          return true;
      }
    });
  }

  private applyPerformanceFilter(agents: AgentRegistration[], filter: PerformanceFilter): AgentRegistration[] {
    return agents.filter(agent => {
      const metrics = agent.metrics;
      
      if (filter.minCpuAvailable && metrics.cpuUsage > (100 - filter.minCpuAvailable)) {
        return false;
      }
      
      if (filter.minMemoryAvailable && metrics.memoryUsage > (100 - filter.minMemoryAvailable)) {
        return false;
      }
      
      if (filter.maxLatency && metrics.averageResponseTime > filter.maxLatency) {
        return false;
      }
      
      return true;
    });
  }

  private applyAvailabilityFilter(agents: AgentRegistration[], filter: AvailabilityFilter): AgentRegistration[] {
    return agents.filter(agent => {
      if (filter.requiresHealthy && agent.healthCheck.status !== 'healthy') {
        return false;
      }
      
      if (filter.minUptime && agent.metrics.uptime < filter.minUptime) {
        return false;
      }
      
      if (filter.maxLoadFactor && agent.metrics.loadFactor > filter.maxLoadFactor) {
        return false;
      }
      
      return true;
    });
  }

  private calculateAgentScore(agent: AgentRegistration, criteria: AgentSelectionCriteria): number {
    let score = 100;
    
    // Factor in health
    if (agent.healthCheck.status === 'healthy') {
      score += 20;
    } else if (agent.healthCheck.status === 'degraded') {
      score -= 10;
    } else {
      score -= 50;
    }
    
    // Factor in performance
    score -= agent.metrics.cpuUsage * 0.5;
    score -= agent.metrics.memoryUsage * 0.3;
    score -= agent.metrics.loadFactor * 10;
    
    // Factor in response time
    score -= agent.metrics.averageResponseTime * 0.01;
    
    return Math.max(0, score);
  }

  private generateCacheKey(query: DiscoveryQuery): string {
    return JSON.stringify(query);
  }

  private async loadPersistedAgents(): Promise<void> {
    // Implement persistence loading
    console.log('Loading persisted agents...');
  }

  private async persistAgents(): Promise<void> {
    // Implement persistence saving
    console.log('Persisting agents...');
  }
}

/**
 * Load Balancer Implementation
 */
class LoadBalancer {
  private _strategy: LoadBalancingStrategy;
  private _agents: AgentRegistration[] = [];
  private _roundRobinIndex: number = 0;
  private _connectionCounts: Map<AgentId, number> = new Map();
  private _responseTimes: Map<AgentId, number[]> = new Map();

  constructor(strategy: LoadBalancingStrategy) {
    this._strategy = strategy;
  }

  addAgent(agent: AgentRegistration): void {
    this._agents.push(agent);
    this._connectionCounts.set(agent.id, 0);
    this._responseTimes.set(agent.id, []);
  }

  removeAgent(agent: AgentRegistration): void {
    this._agents = this._agents.filter(a => a.id !== agent.id);
    this._connectionCounts.delete(agent.id);
    this._responseTimes.delete(agent.id);
  }

  updateAgent(agent: AgentRegistration): void {
    const index = this._agents.findIndex(a => a.id === agent.id);
    if (index !== -1) {
      this._agents[index] = agent;
    }
  }

  selectAgent(candidates: AgentRegistration[], criteria: AgentSelectionCriteria): AgentRegistration {
    const availableAgents = candidates.filter(agent => 
      agent.status === AgentStatus.RUNNING && 
      agent.healthCheck.status === 'healthy'
    );

    if (availableAgents.length === 0) {
      throw new Error('No healthy agents available');
    }

    switch (this._strategy) {
      case LoadBalancingStrategy.ROUND_ROBIN:
        return this.selectRoundRobin(availableAgents);
      case LoadBalancingStrategy.LEAST_CONNECTIONS:
        return this.selectLeastConnections(availableAgents);
      case LoadBalancingStrategy.LEAST_RESPONSE_TIME:
        return this.selectLeastResponseTime(availableAgents);
      case LoadBalancingStrategy.RANDOM:
        return this.selectRandom(availableAgents);
      case LoadBalancingStrategy.PERFORMANCE_BASED:
        return this.selectPerformanceBased(availableAgents);
      default:
        return availableAgents[0];
    }
  }

  private selectRoundRobin(agents: AgentRegistration[]): AgentRegistration {
    const agent = agents[this._roundRobinIndex % agents.length];
    this._roundRobinIndex++;
    return agent;
  }

  private selectLeastConnections(agents: AgentRegistration[]): AgentRegistration {
    return agents.reduce((best, current) => {
      const bestConnections = this._connectionCounts.get(best.id) || 0;
      const currentConnections = this._connectionCounts.get(current.id) || 0;
      return currentConnections < bestConnections ? current : best;
    });
  }

  private selectLeastResponseTime(agents: AgentRegistration[]): AgentRegistration {
    return agents.reduce((best, current) => {
      const bestTime = best.metrics.averageResponseTime;
      const currentTime = current.metrics.averageResponseTime;
      return currentTime < bestTime ? current : best;
    });
  }

  private selectRandom(agents: AgentRegistration[]): AgentRegistration {
    const randomIndex = Math.floor(Math.random() * agents.length);
    return agents[randomIndex];
  }

  private selectPerformanceBased(agents: AgentRegistration[]): AgentRegistration {
    return agents.reduce((best, current) => {
      const bestScore = this.calculatePerformanceScore(best);
      const currentScore = this.calculatePerformanceScore(current);
      return currentScore > bestScore ? current : best;
    });
  }

  private calculatePerformanceScore(agent: AgentRegistration): number {
    let score = 100;
    score -= agent.metrics.cpuUsage * 0.5;
    score -= agent.metrics.memoryUsage * 0.3;
    score -= agent.metrics.loadFactor * 10;
    score -= agent.metrics.averageResponseTime * 0.01;
    return Math.max(0, score);
  }

  recordConnection(agentId: AgentId): void {
    const current = this._connectionCounts.get(agentId) || 0;
    this._connectionCounts.set(agentId, current + 1);
  }

  recordDisconnection(agentId: AgentId): void {
    const current = this._connectionCounts.get(agentId) || 0;
    this._connectionCounts.set(agentId, Math.max(0, current - 1));
  }

  recordResponseTime(agentId: AgentId, responseTime: number): void {
    let times = this._responseTimes.get(agentId) || [];
    times.push(responseTime);
    
    // Keep only last 100 response times
    if (times.length > 100) {
      times = times.slice(-100);
    }
    
    this._responseTimes.set(agentId, times);
  }
}